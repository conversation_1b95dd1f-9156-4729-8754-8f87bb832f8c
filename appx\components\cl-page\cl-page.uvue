<template>
  <view class="cl-page" :class="pageClass">
    <!-- 状态栏占位 -->
    <view v-if="showStatusBar" class="cl-page__status-bar" :style="statusBarStyle"></view>
    
    <!-- 导航栏 -->
    <view v-if="showNavBar" class="cl-page__nav-bar" :style="navBarStyle">
      <view class="cl-page__nav-content">
        <view class="cl-page__nav-left">
          <button v-if="showBack" class="cl-page__back-btn" @click="handleBack">
            <text class="cl-page__back-icon">←</text>
          </button>
          <slot name="nav-left" />
        </view>
        <view class="cl-page__nav-title">
          <text class="cl-page__title-text">{{ title }}</text>
          <slot name="nav-title" />
        </view>
        <view class="cl-page__nav-right">
          <slot name="nav-right" />
        </view>
      </view>
    </view>
    
    <!-- 页面内容 -->
    <view class="cl-page__content" :style="contentStyle">
      <slot />
    </view>
    
    <!-- 底部安全区域 -->
    <view v-if="showSafeArea" class="cl-page__safe-area" :style="safeAreaStyle"></view>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

// 定义props
interface Props {
  title?: string
  showStatusBar?: boolean
  showNavBar?: boolean
  showBack?: boolean
  showSafeArea?: boolean
  backgroundColor?: string
  navBarColor?: string
  titleColor?: string
  statusBarHeight?: string
  navBarHeight?: string
  safeAreaHeight?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showStatusBar: true,
  showNavBar: true,
  showBack: true,
  showSafeArea: true,
  backgroundColor: '#f5f5f5',
  navBarColor: '#ffffff',
  titleColor: '#333333',
  statusBarHeight: '44px',
  navBarHeight: '44px',
  safeAreaHeight: '34px'
})

// 定义事件
const emit = defineEmits<{
  back: []
}>()

// 计算样式
const pageClass = computed(() => ({
  'cl-page--with-nav': props.showNavBar,
  'cl-page--with-status': props.showStatusBar
}))

const statusBarStyle = computed(() => ({
  height: props.statusBarHeight,
  backgroundColor: props.navBarColor
}))

const navBarStyle = computed(() => ({
  height: props.navBarHeight,
  backgroundColor: props.navBarColor
}))

const contentStyle = computed(() => {
  let paddingTop = '0px'
  if (props.showStatusBar && props.showNavBar) {
    paddingTop = `calc(${props.statusBarHeight} + ${props.navBarHeight})`
  } else if (props.showNavBar) {
    paddingTop = props.navBarHeight
  } else if (props.showStatusBar) {
    paddingTop = props.statusBarHeight
  }
  
  return {
    paddingTop,
    backgroundColor: props.backgroundColor,
    paddingBottom: props.showSafeArea ? props.safeAreaHeight : '0px'
  }
})

const safeAreaStyle = computed(() => ({
  height: props.safeAreaHeight
}))

// 处理返回
const handleBack = () => {
  emit('back')
  uni.navigateBack()
}
</script>

<style>
.cl-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.cl-page__status-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

.cl-page__nav-bar {
  position: fixed;
  top: var(--status-bar-height, 44px);
  left: 0;
  right: 0;
  z-index: 998;
  border-bottom: 1rpx solid #eee;
}

.cl-page__nav-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 20rpx;
}

.cl-page__nav-left,
.cl-page__nav-right {
  width: 120rpx;
  display: flex;
  align-items: center;
}

.cl-page__nav-right {
  justify-content: flex-end;
}

.cl-page__nav-title {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cl-page__title-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.cl-page__back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  padding: 0;
}

.cl-page__back-icon {
  font-size: 20px;
  color: #333;
}

.cl-page__content {
  flex: 1;
  width: 100%;
  overflow-y: auto;
}

.cl-page__safe-area {
  width: 100%;
  background-color: #ffffff;
}

/* 适配不同状态 */
.cl-page--with-nav .cl-page__content {
  padding-top: calc(var(--status-bar-height, 44px) + var(--nav-bar-height, 44px));
}

.cl-page--with-status:not(.cl-page--with-nav) .cl-page__content {
  padding-top: var(--status-bar-height, 44px);
}
</style>
