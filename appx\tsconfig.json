{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*", "./*"], "@/components/*": ["./components/*"], "@/pages/*": ["./pages/*"], "@/stores/*": ["./stores/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"], "@/styles/*": ["./styles/*"], "@/static/*": ["./static/*"]}, "types": ["@dcloudio/types", "./types/uni-app"], "typeRoots": ["node_modules/@types", "types"]}, "include": ["src/**/*", "pages/**/*", "components/**/*", "stores/**/*", "utils/**/*", "types/**/*", "*.ts", "*.vue", "*.u<PERSON>"], "exclude": ["node_modules", "dist", "build", "unpackage"], "vueCompilerOptions": {"target": 3}}