// 格式化工具函数

// 格式化数字
export const formatNumber = (num: number | string, decimals: number = 2): string => {
  const n = typeof num === 'string' ? parseFloat(num) : num
  if (isNaN(n)) return '0'
  return n.toFixed(decimals)
}

// 格式化货币
export const formatCurrency = (amount: number | string, currency: string = '¥', decimals: number = 2): string => {
  const n = typeof amount === 'string' ? parseFloat(amount) : amount
  if (isNaN(n)) return `${currency}0`
  return `${currency}${n.toFixed(decimals)}`
}

// 格式化百分比
export const formatPercent = (value: number | string, decimals: number = 1): string => {
  const n = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(n)) return '0%'
  return `${(n * 100).toFixed(decimals)}%`
}

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// 格式化能耗单位
export const formatEnergy = (value: number | string, unit: string = 'kWh'): string => {
  const n = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(n)) return `0 ${unit}`
  
  if (n >= 1000) {
    return `${(n / 1000).toFixed(2)} M${unit}`
  } else if (n >= 1) {
    return `${n.toFixed(2)} ${unit}`
  } else {
    return `${(n * 1000).toFixed(0)} W${unit.replace('k', '').replace('K', '')}`
  }
}

// 格式化功率单位
export const formatPower = (value: number | string): string => {
  const n = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(n)) return '0 W'
  
  if (n >= 1000) {
    return `${(n / 1000).toFixed(2)} kW`
  } else {
    return `${n.toFixed(1)} W`
  }
}

// 格式化温度
export const formatTemperature = (value: number | string, unit: string = '°C'): string => {
  const n = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(n)) return `0${unit}`
  return `${n.toFixed(1)}${unit}`
}

// 格式化湿度
export const formatHumidity = (value: number | string): string => {
  const n = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(n)) return '0%'
  return `${n.toFixed(1)}%`
}

// 格式化电压
export const formatVoltage = (value: number | string): string => {
  const n = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(n)) return '0 V'
  return `${n.toFixed(1)} V`
}

// 格式化电流
export const formatCurrent = (value: number | string): string => {
  const n = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(n)) return '0 A'
  return `${n.toFixed(2)} A`
}

// 格式化时长
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.floor(seconds)}秒`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`
  } else if (seconds < 86400) {
    const hours = Math.floor(seconds / 3600)
    const remainingMinutes = Math.floor((seconds % 3600) / 60)
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  } else {
    const days = Math.floor(seconds / 86400)
    const remainingHours = Math.floor((seconds % 86400) / 3600)
    return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`
  }
}

// 格式化工作时长
export const formatWorkingTime = (minutes: number): string => {
  if (minutes < 60) {
    return `${Math.floor(minutes)}分钟`
  } else if (minutes < 1440) {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = Math.floor(minutes % 60)
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  } else {
    const days = Math.floor(minutes / 1440)
    const remainingHours = Math.floor((minutes % 1440) / 60)
    return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`
  }
}

// 格式化手机号
export const formatPhone = (phone: string): string => {
  if (!phone) return ''
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }
  return phone
}

// 隐藏手机号中间四位
export const maskPhone = (phone: string): string => {
  if (!phone) return ''
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }
  return phone
}

// 格式化身份证号
export const formatIdCard = (idCard: string): string => {
  if (!idCard) return ''
  const cleaned = idCard.replace(/\s/g, '')
  if (cleaned.length === 18) {
    return cleaned.replace(/(\d{6})(\d{8})(\d{4})/, '$1 $2 $3')
  }
  return idCard
}

// 隐藏身份证号中间部分
export const maskIdCard = (idCard: string): string => {
  if (!idCard) return ''
  const cleaned = idCard.replace(/\s/g, '')
  if (cleaned.length === 18) {
    return cleaned.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  }
  return idCard
}

// 格式化银行卡号
export const formatBankCard = (cardNumber: string): string => {
  if (!cardNumber) return ''
  const cleaned = cardNumber.replace(/\D/g, '')
  return cleaned.replace(/(\d{4})(?=\d)/g, '$1 ')
}

// 隐藏银行卡号中间部分
export const maskBankCard = (cardNumber: string): string => {
  if (!cardNumber) return ''
  const cleaned = cardNumber.replace(/\D/g, '')
  if (cleaned.length >= 8) {
    const first = cleaned.slice(0, 4)
    const last = cleaned.slice(-4)
    const middle = '*'.repeat(cleaned.length - 8)
    return `${first} ${middle} ${last}`
  }
  return cardNumber
}

// 格式化邮箱
export const maskEmail = (email: string): string => {
  if (!email) return ''
  const [username, domain] = email.split('@')
  if (!username || !domain) return email
  
  if (username.length <= 2) {
    return `${username[0]}*@${domain}`
  } else {
    const maskedUsername = `${username[0]}${'*'.repeat(username.length - 2)}${username[username.length - 1]}`
    return `${maskedUsername}@${domain}`
  }
}

// 首字母大写
export const capitalize = (str: string): string => {
  if (!str) return ''
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

// 驼峰转下划线
export const camelToSnake = (str: string): string => {
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
}

// 下划线转驼峰
export const snakeToCamel = (str: string): string => {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

// 移除HTML标签
export const stripHtml = (html: string): string => {
  return html.replace(/<[^>]*>/g, '')
}

// 截断文本
export const truncate = (text: string, length: number, suffix: string = '...'): string => {
  if (!text || text.length <= length) return text
  return text.slice(0, length) + suffix
}

// 格式化JSON
export const formatJson = (obj: any, indent: number = 2): string => {
  try {
    return JSON.stringify(obj, null, indent)
  } catch (error) {
    return String(obj)
  }
}

// 解析JSON
export const parseJson = <T = any>(jsonString: string, defaultValue?: T): T | undefined => {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    return defaultValue
  }
}
