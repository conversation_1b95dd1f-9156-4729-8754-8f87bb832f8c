<template>
	<cl-page>
		<cl-topbar title="能耗监控">
			<template #right>
				<cl-icon
					name="cl-icon-refresh"
					size="20"
					color="#1890ff"
					:class="{ rotating: isRefreshing }"
					@click="refreshData"
				/>
				<cl-icon
					name="cl-icon-setting"
					size="20"
					color="#666"
					@click="showSettings = true"
				/>
			</template>
		</cl-topbar>

		<scroll-view 
			class="energy-monitor" 
			scroll-y 
			refresher-enabled 
			:refresher-triggered="pullDownRefresh" 
			@refresherrefresh="onPullDownRefresh"
			refresher-background="#f5f5f5"
		>
			<!-- 实时状态指示器 -->
			<view class="realtime-indicator">
				<view class="indicator-dot" :class="{ 
					online: connectionStatus === 'connected' && !isOffline,
					offline: isOffline || connectionStatus === 'disconnected',
					connecting: connectionStatus === 'connecting'
				}"></view>
				<text class="indicator-text">
					{{ isOffline ? '离线模式' : 
					   connectionStatus === 'connected' ? '实时数据' :
					   connectionStatus === 'connecting' ? '连接中...' : '连接断开' }}
				</text>
				<text class="update-time">更新时间：{{ lastUpdateTime }}</text>
				<text v-if="isOffline" class="offline-tip">（显示缓存数据）</text>
			</view>

			<!-- 统计概览 -->
			<view class="stats-overview">
				<view class="stat-card" @click="showStatDetail('today')">
					<view class="stat-icon">
						<cl-icon name="cl-icon-flash" size="24" color="#1890ff" />
					</view>
					<view class="stat-info">
						<text class="stat-value">{{ todayConsumption }}kWh</text>
						<text class="stat-label">今日用电</text>
						<view
							class="stat-trend"
							:class="{ increase: todayTrend > 0, decrease: todayTrend < 0 }"
						>
							<cl-icon
								:name="todayTrend > 0 ? 'cl-icon-arrow-up' : 'cl-icon-arrow-down'"
								size="12"
							/>
							<text class="trend-text">{{ Math.abs(todayTrend) }}%</text>
						</view>
					</view>
				</view>

				<view class="stat-card" @click="showStatDetail('month')">
					<view class="stat-icon">
						<cl-icon name="cl-icon-calendar" size="24" color="#52c41a" />
					</view>
					<view class="stat-info">
						<text class="stat-value">{{ monthConsumption }}kWh</text>
						<text class="stat-label">本月用电</text>
						<view
							class="stat-trend"
							:class="{ increase: monthTrend > 0, decrease: monthTrend < 0 }"
						>
							<cl-icon
								:name="monthTrend > 0 ? 'cl-icon-arrow-up' : 'cl-icon-arrow-down'"
								size="12"
							/>
							<text class="trend-text">{{ Math.abs(monthTrend) }}%</text>
						</view>
					</view>
				</view>

				<view class="stat-card" @click="showStatDetail('cost')">
					<view class="stat-icon">
						<cl-icon name="cl-icon-money" size="24" color="#faad14" />
					</view>
					<view class="stat-info">
						<text class="stat-value">¥{{ todayCost }}</text>
						<text class="stat-label">今日电费</text>
						<view
							class="stat-trend"
							:class="{ increase: costTrend > 0, decrease: costTrend < 0 }"
						>
							<cl-icon
								:name="costTrend > 0 ? 'cl-icon-arrow-up' : 'cl-icon-arrow-down'"
								size="12"
							/>
							<text class="trend-text">{{ Math.abs(costTrend) }}%</text>
						</view>
					</view>
				</view>

				<view class="stat-card" @click="showStatDetail('co2')">
					<view class="stat-icon">
						<cl-icon name="cl-icon-leaf" size="24" color="#52c41a" />
					</view>
					<view class="stat-info">
						<text class="stat-value">{{ co2Reduction }}kg</text>
						<text class="stat-label">减排CO₂</text>
						<view class="stat-trend increase">
							<cl-icon name="cl-icon-arrow-up" size="12" />
							<text class="trend-text">{{ co2Trend }}%</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 时间范围选择 -->
			<view class="time-selector">
				<view class="selector-tabs">
					<view
						v-for="tab in timeTabs"
						:key="tab.value"
						class="tab-item"
						:class="{ active: activeTab === tab.value }"
						@click="switchTab(tab.value)"
					>
						{{ tab.label }}
					</view>
				</view>

				<view v-if="activeTab === 'custom'" class="custom-time">
					<cl-date-picker
						v-model="customStartDate"
						placeholder="开始日期"
						@change="handleDateChange"
					/>
					<text class="date-separator">至</text>
					<cl-date-picker
						v-model="customEndDate"
						placeholder="结束日期"
						@change="handleDateChange"
					/>
				</view>
			</view>

			<!-- 能耗分析 -->
			<view class="analysis-section">
				<view class="section-header">
					<text class="section-title">能耗分析</text>
					<view class="analysis-tabs">
						<view
							v-for="tab in analysisTabs"
							:key="tab.value"
							class="analysis-tab"
							:class="{ active: activeAnalysis === tab.value }"
							@click="switchAnalysis(tab.value)"
						>
							{{ tab.label }}
						</view>
					</view>
				</view>

				<!-- 趋势分析 -->
				<view v-if="activeAnalysis === 'trend'" class="trend-analysis">
					<view class="chart-container">
						<view class="chart-header">
							<text class="chart-title">能耗趋势</text>
							<view class="chart-legend">
								<view class="legend-item">
									<view
										class="legend-color"
										style="background-color: #1890ff"
									></view>
									<text class="legend-text">用电量</text>
								</view>
								<view class="legend-item">
									<view
										class="legend-color"
										style="background-color: #52c41a"
									></view>
									<text class="legend-text">节能量</text>
								</view>
							</view>
						</view>

						<energy-chart
							:data="chartData"
							:chart-type="chartType"
							@point-click="onChartPointClick"
						/>

						<view class="chart-controls">
							<cl-button
								v-for="type in chartTypes"
								:key="type.value"
								size="small"
								:type="chartType === type.value ? 'primary' : 'default'"
								class="chart-btn"
								@click="switchChartType(type.value)"
							>
								{{ type.label }}
							</cl-button>
						</view>
					</view>
				</view>

				<!-- 对比分析 -->
				<view v-else-if="activeAnalysis === 'compare'" class="compare-analysis">
					<view class="compare-options">
						<view class="compare-item">
							<text class="compare-label">对比类型</text>
							<cl-select v-model="compareType" placeholder="选择对比类型">
								<cl-option value="period" label="时间段对比" />
								<cl-option value="device" label="设备对比" />
								<cl-option value="location" label="区域对比" />
							</cl-select>
						</view>

						<view class="compare-item">
							<text class="compare-label">基准期间</text>
							<cl-date-picker v-model="baselinePeriod" type="daterange" />
						</view>

						<view class="compare-item">
							<text class="compare-label">对比期间</text>
							<cl-date-picker v-model="comparePeriod" type="daterange" />
						</view>
					</view>

					<view class="compare-results">
						<view class="result-card">
							<text class="result-title">用电量对比</text>
							<view class="result-data">
								<view class="data-item">
									<text class="data-label">基准期间</text>
									<text class="data-value">{{ baselineConsumption }}kWh</text>
								</view>
								<view class="data-item">
									<text class="data-label">对比期间</text>
									<text class="data-value">{{ compareConsumption }}kWh</text>
								</view>
								<view class="data-item">
									<text class="data-label">变化幅度</text>
									<text
										class="data-value"
										:class="{
											increase: compareChange > 0,
											decrease: compareChange < 0
										}"
									>
										{{ compareChange > 0 ? "+" : "" }}{{ compareChange }}%
									</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 预警分析 -->
				<view v-else-if="activeAnalysis === 'alert'" class="alert-analysis">
					<view class="alert-summary">
						<view class="alert-item" :class="alertLevels.high > 0 ? 'high' : ''">
							<cl-icon name="cl-icon-warning" size="20" />
							<text class="alert-count">{{ alertLevels.high }}</text>
							<text class="alert-label">高风险</text>
						</view>
						<view class="alert-item" :class="alertLevels.medium > 0 ? 'medium' : ''">
							<cl-icon name="cl-icon-info" size="20" />
							<text class="alert-count">{{ alertLevels.medium }}</text>
							<text class="alert-label">中风险</text>
						</view>
						<view class="alert-item" :class="alertLevels.low > 0 ? 'low' : ''">
							<cl-icon name="cl-icon-check" size="20" />
							<text class="alert-count">{{ alertLevels.low }}</text>
							<text class="alert-label">低风险</text>
						</view>
					</view>

					<view class="alert-list">
						<view
							v-for="alert in alertList"
							:key="alert.id"
							class="alert-card"
							:class="alert.level"
							@click="handleAlert(alert)"
						>
							<view class="alert-header">
								<cl-icon :name="getAlertIcon(alert.level)" size="16" />
								<text class="alert-title">{{ alert.title }}</text>
								<text class="alert-time">{{ alert.time }}</text>
							</view>
							<text class="alert-desc">{{ alert.description }}</text>
							<view class="alert-actions">
								<cl-button
									size="mini"
									type="primary"
									@click.stop="resolveAlert(alert.id)"
									>处理</cl-button
								>
								<cl-button size="mini" @click.stop="ignoreAlert(alert.id)"
									>忽略</cl-button
								>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 设备能耗排行 -->
			<view class="device-ranking">
				<view class="section-title">
					<cl-icon name="cl-icon-bar-chart" />
					<text>设备能耗排行</text>
				</view>

				<view class="ranking-list">
					<view
						v-for="(device, index) in deviceRanking"
						:key="device.id"
						class="ranking-item"
						@click="viewDeviceDetail(device)"
					>
						<view class="ranking-number">
							<text class="rank-text" :class="getRankClass(index)">{{
								index + 1
							}}</text>
						</view>

						<view class="device-info">
							<text class="device-name">{{ device.deviceName }}</text>
							<text class="device-location">{{ device.location }}</text>
						</view>

						<view class="consumption-info">
							<text class="consumption-value"
								>{{ device.consumption.toFixed(2) }}kWh</text
							>
							<view class="consumption-bar">
								<view
									class="bar-fill"
									:style="{ width: getBarWidth(device.consumption) + '%' }"
								></view>
							</view>
						</view>

						<cl-icon name="cl-icon-arrow-right" size="16" color="#ccc" />
					</view>
				</view>
			</view>

			<!-- 节能建议 -->
			<view class="energy-tips">
				<view class="section-title">
					<cl-icon name="cl-icon-bulb" />
					<text>节能建议</text>
				</view>

				<view class="tips-list">
					<view v-for="tip in energyTips" :key="tip.id" class="tip-item">
						<view class="tip-icon">
							<cl-icon :name="tip.icon" size="20" :color="tip.color" />
						</view>
						<view class="tip-content">
							<text class="tip-title">{{ tip.title }}</text>
							<text class="tip-desc">{{ tip.description }}</text>
							<text class="tip-saving">预计节省：{{ tip.saving }}</text>
						</view>
						<cl-button size="small" type="primary" plain @click="applyTip(tip)">
							应用
						</cl-button>
					</view>
				</view>
			</view>

			<!-- 导出报告 -->
			<view class="export-section">
				<cl-button type="primary" size="large" class="export-btn" @click="exportReport">
					<cl-icon name="cl-icon-download" size="16" />
					导出报告
				</cl-button>
			</view>
		</view>

		<!-- 设置弹窗 -->
		<cl-popup v-model="showSettings" direction="bottom">
			<view class="settings-modal">
				<view class="modal-header">
					<text class="modal-title">监控设置</text>
					<cl-button text @click="showSettings = false">关闭</cl-button>
				</view>

				<view class="settings-content">
					<view class="setting-item">
						<text class="setting-label">自动刷新</text>
						<cl-switch v-model="autoRefresh" @change="toggleAutoRefresh" />
					</view>

					<view class="setting-item">
						<text class="setting-label">刷新间隔</text>
						<cl-select v-model="refreshInterval" :disabled="!autoRefresh">
							<cl-option value="30" label="30秒" />
							<cl-option value="60" label="1分钟" />
							<cl-option value="300" label="5分钟" />
							<cl-option value="600" label="10分钟" />
						</cl-select>
					</view>

					<view class="setting-item">
						<text class="setting-label">预警通知</text>
						<cl-switch v-model="alertNotification" />
					</view>

					<view class="setting-item">
						<text class="setting-label">数据精度</text>
						<cl-select v-model="dataPrecision">
							<cl-option value="1" label="1位小数" />
							<cl-option value="2" label="2位小数" />
							<cl-option value="3" label="3位小数" />
						</cl-select>
					</view>

					<view class="setting-item">
						<text class="setting-label">电价设置</text>
						<cl-input
							v-model="electricityPrice"
							type="number"
							placeholder="请输入电价（元/kWh）"
							suffix="元/kWh"
						/>
					</view>
				</view>
			</view>
		</cl-popup>

		<!-- 统计详情弹窗 -->
		<cl-popup v-model="showStatDetail" direction="bottom">
			<view class="stat-detail-modal">
				<view class="modal-header">
					<text class="modal-title">{{ statDetailTitle }}</text>
					<cl-button text @click="showStatDetail = false">关闭</cl-button>
				</view>

				<view class="stat-detail-content">
					<view v-if="selectedStatType === 'today'" class="detail-section">
						<view class="detail-item">
							<text class="detail-label">今日用电量</text>
							<text class="detail-value">{{ todayConsumption }}kWh</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">昨日用电量</text>
							<text class="detail-value">{{ yesterdayConsumption }}kWh</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">环比变化</text>
							<text
								class="detail-value"
								:class="{ increase: todayTrend > 0, decrease: todayTrend < 0 }"
							>
								{{ todayTrend > 0 ? "+" : "" }}{{ todayTrend }}%
							</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">峰值时段</text>
							<text class="detail-value">{{ peakHour }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">谷值时段</text>
							<text class="detail-value">{{ valleyHour }}</text>
						</view>
					</view>

					<view v-else-if="selectedStatType === 'month'" class="detail-section">
						<view class="detail-item">
							<text class="detail-label">本月用电量</text>
							<text class="detail-value">{{ monthConsumption }}kWh</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">上月用电量</text>
							<text class="detail-value">{{ lastMonthConsumption }}kWh</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">环比变化</text>
							<text
								class="detail-value"
								:class="{ increase: monthTrend > 0, decrease: monthTrend < 0 }"
							>
								{{ monthTrend > 0 ? "+" : "" }}{{ monthTrend }}%
							</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">日均用电</text>
							<text class="detail-value">{{ dailyAverage }}kWh</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">预计月底</text>
							<text class="detail-value">{{ monthlyForecast }}kWh</text>
						</view>
					</view>

					<view v-else-if="selectedStatType === 'cost'" class="detail-section">
						<view class="detail-item">
							<text class="detail-label">今日电费</text>
							<text class="detail-value">¥{{ todayCost }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">本月电费</text>
							<text class="detail-value">¥{{ monthCost }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">电价</text>
							<text class="detail-value">{{ electricityPrice }}元/kWh</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">预计月费</text>
							<text class="detail-value">¥{{ monthlyCostForecast }}</text>
						</view>
					</view>

					<view v-else-if="selectedStatType === 'co2'" class="detail-section">
						<view class="detail-item">
							<text class="detail-label">今日减排</text>
							<text class="detail-value">{{ co2Reduction }}kg</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">本月减排</text>
							<text class="detail-value">{{ monthCo2Reduction }}kg</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">减排系数</text>
							<text class="detail-value">{{ co2Factor }}kg/kWh</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">等效植树</text>
							<text class="detail-value">{{ equivalentTrees }}棵</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</cl-popup>
	</cl-page>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from "vue";
import { router } from "@/cool";
import { energyMonitorStore } from "@/stores/energy-monitor";
import { energyDeviceStore } from "@/stores/energy-device";
import EnergyChart from "@/components/business/energy-chart.uvue";

// 响应式数据
const activeTab = ref("today");
const chartType = ref("line");
const chartLoading = ref(false);
const customStartDate = ref("");
const customEndDate = ref("");

// WebSocket连接
const wsConnection = ref(null);
const connectionStatus = ref("disconnected"); // connected, connecting, disconnected
const reconnectAttempts = ref(0);
const maxReconnectAttempts = 5;
const reconnectTimer = ref(null);

// 时间选项卡
const timeTabs = [
	{ label: "日", value: "day" },
	{ label: "周", value: "week" },
	{ label: "月", value: "month" },
	{ label: "年", value: "year" },
	{ label: "自定义", value: "custom" }
];

// 分析相关
const activeAnalysis = ref("trend");
const analysisTabs = [
	{ label: "趋势分析", value: "trend" },
	{ label: "对比分析", value: "compare" },
	{ label: "预警分析", value: "alert" }
];

// 对比分析数据
const compareType = ref("period");
const baselinePeriod = ref("");
const comparePeriod = ref("");
const baselineConsumption = ref(0);
const compareConsumption = ref(0);
const compareChange = ref(0);

// 预警数据
const alertLevels = ref({
	high: 2,
	medium: 5,
	low: 1
});
const alertList = ref([
	{
		id: 1,
		title: "用电量异常",
		description: "办公区域用电量超出正常范围30%",
		level: "high",
		time: "10分钟前"
	},
	{
		id: 2,
		title: "设备离线",
		description: "会议室照明控制器失去连接",
		level: "medium",
		time: "1小时前"
	}
]);

// 趋势数据
const todayTrend = ref(5.2);
const monthTrend = ref(-3.1);
const costTrend = ref(4.8);
const co2Trend = ref(8.5);

// 详情数据
const yesterdayConsumption = ref(125.6);
const lastMonthConsumption = ref(3250.8);
const peakHour = ref("14:00-15:00");
const valleyHour = ref("02:00-06:00");
const dailyAverage = ref(108.5);
const monthlyForecast = ref(3580.2);
const monthCost = ref(1890.5);
const monthlyCostForecast = ref(2156.8);
const monthCo2Reduction = ref(285.6);
const co2Factor = ref(0.785);
const equivalentTrees = ref(12);

// 图表类型
const chartTypes = [
	{ label: "折线图", value: "line" },
	{ label: "柱状图", value: "bar" },
	{ label: "面积图", value: "area" }
];

// 统计数据
const todayConsumption = computed(() => energyMonitorStore.todayConsumption);
const monthConsumption = computed(() => energyMonitorStore.monthConsumption);
const todayCost = computed(() => energyMonitorStore.todayCost);
const co2Reduction = computed(() => energyMonitorStore.co2Reduction);

// 图表数据
const chartData = computed(() => energyMonitorStore.chartData);

// 设备排行
const deviceRanking = computed(() => {
	return energyMonitorStore.deviceConsumption
		.sort((a, b) => b.consumption - a.consumption)
		.slice(0, 10);
});

// 节能建议
const energyTips = ref([
	{
		id: 1,
		title: "优化照明时间",
		description: "根据使用习惯，建议在22:00后自动调低亮度",
		saving: "15%电费",
		icon: "cl-icon-clock",
		color: "#1890ff",
		action: "schedule"
	},
	{
		id: 2,
		title: "启用节能模式",
		description: "办公区域可在非工作时间启用节能模式",
		saving: "25%电费",
		icon: "cl-icon-leaf",
		color: "#52c41a",
		action: "energy_mode"
	},
	{
		id: 3,
		title: "更换LED灯具",
		description: "建议将传统灯具更换为高效LED灯具",
		saving: "40%电费",
		icon: "cl-icon-bulb",
		color: "#fa8c16",
		action: "upgrade"
	},
	{
		id: 4,
		title: "智能感应控制",
		description: "在走廊等区域安装人体感应器，实现按需照明",
		saving: "30%电费",
		icon: "cl-icon-eye",
		color: "#722ed1",
		action: "sensor"
	}
]);

// 切换时间选项卡
const switchTab = async (tab: string) => {
	activeTab.value = tab;
	await loadChartData();
};

// 切换图表类型
const switchChartType = (type: string) => {
	chartType.value = type;
};

// 实时状态
const isRealtime = ref(true);
const lastUpdateTime = ref("刚刚");
const isRefreshing = ref(false);
const realtimeData = ref({
	power: 0,
	current: 0,
	voltage: 220,
	frequency: 50,
	powerFactor: 0.95
});

// 弹窗状态
const showSettings = ref(false);
const showStatDetail = ref(false);
const selectedStatType = ref("");
const statDetailTitle = ref("");

// 设置选项
const autoRefresh = ref(true);
const refreshInterval = ref("60");
const alertNotification = ref(true);
const dataPrecision = ref("2");
const electricityPrice = ref("0.52");
const refreshTimer = ref(null);

// 分析相关
const activeAnalysis = ref("trend");
const analysisTabs = [
	{ label: "趋势分析", value: "trend" },
	{ label: "对比分析", value: "compare" },
	{ label: "预警分析", value: "alert" }
];

// 对比分析数据
const compareType = ref("period");
const baselinePeriod = ref("");
const comparePeriod = ref("");
const baselineConsumption = ref(0);
const compareConsumption = ref(0);
const compareChange = ref(0);

// 预警数据
const alertLevels = ref({
	high: 2,
	medium: 5,
	low: 1
});

const alertList = ref([
	{
		id: 1,
		title: "用电量异常",
		description: "办公区域用电量超出正常范围30%",
		level: "high",
		time: "10分钟前"
	},
	{
		id: 2,
		title: "设备离线",
		description: "会议室照明控制器失去连接",
		level: "medium",
		time: "1小时前"
	}
]);

// 趋势数据
const todayTrend = ref(5.2);
const monthTrend = ref(-3.1);
const costTrend = ref(4.8);
const co2Trend = ref(8.5);

// 详情数据
const yesterdayConsumption = ref(125.6);
const lastMonthConsumption = ref(3250.8);
const peakHour = ref("14:00-15:00");
const valleyHour = ref("02:00-06:00");
const dailyAverage = ref(108.5);
const monthlyForecast = ref(3580.2);
const monthCost = ref(1890.5);
const monthlyCostForecast = ref(2156.8);
const monthCo2Reduction = ref(285.6);
const co2Factor = ref(0.785);
const equivalentTrees = ref(12);

// 处理日期变化
const handleDateChange = async () => {
	if (customStartDate.value && customEndDate.value) {
		await loadChartData();
	}
};

// 加载图表数据
const loadChartData = async () => {
	chartLoading.value = true;
	try {
		let startDate = "";
		let endDate = "";

		switch (activeTab.value) {
			case "today":
				startDate = new Date().toISOString().split("T")[0];
				endDate = startDate;
				break;
			case "week":
				const weekStart = new Date();
				weekStart.setDate(weekStart.getDate() - weekStart.getDay());
				startDate = weekStart.toISOString().split("T")[0];
				endDate = new Date().toISOString().split("T")[0];
				break;
			case "month":
				const monthStart = new Date();
				monthStart.setDate(1);
				startDate = monthStart.toISOString().split("T")[0];
				endDate = new Date().toISOString().split("T")[0];
				break;
			case "year":
				const yearStart = new Date();
				yearStart.setMonth(0, 1);
				startDate = yearStart.toISOString().split("T")[0];
				endDate = new Date().toISOString().split("T")[0];
				break;
			case "custom":
				startDate = customStartDate.value;
				endDate = customEndDate.value;
				break;
		}

		await energyMonitorStore.getConsumptionData(startDate, endDate);
	} catch (error: any) {
		uni.showToast({
			title: error.message || "数据加载失败",
			icon: "none"
		});
	} finally {
		chartLoading.value = false;
	}
};

// 图表交互功能
const chartZoom = ref({ start: 0, end: 100 });
const chartSelection = ref(null);
const showChartTooltip = ref(false);
const tooltipData = ref({});
const tooltipPosition = ref({ x: 0, y: 0 });

// 图表点击事件
const onChartPointClick = (point: any) => {
	tooltipData.value = {
		time: point.time,
		value: point.value,
		cost: (point.value * parseFloat(electricityPrice.value)).toFixed(2),
		co2: (point.value * co2Factor.value).toFixed(2)
	};
	tooltipPosition.value = { x: point.x, y: point.y };
	showChartTooltip.value = true;
};

// 图表缩放事件
const onChartZoom = (zoom: any) => {
	chartZoom.value = zoom;
	// 根据缩放范围重新加载数据
	loadChartDataByRange(zoom.start, zoom.end);
};

// 图表选择事件
const onChartSelection = (selection: any) => {
	chartSelection.value = selection;
	// 显示选择区域的统计信息
	showSelectionStats(selection);
};

// 根据范围加载数据
const loadChartDataByRange = async (start: number, end: number) => {
	try {
		const totalPoints = chartData.value.length;
		const startIndex = Math.floor((start / 100) * totalPoints);
		const endIndex = Math.ceil((end / 100) * totalPoints);
		
		// 获取指定范围的详细数据
		await energyMonitorStore.getDetailedData(startIndex, endIndex);
	} catch (error: any) {
		console.error("加载范围数据失败:", error);
	}
};

// 显示选择区域统计
const showSelectionStats = (selection: any) => {
	if (!selection || !selection.data) return;
	
	const selectedData = selection.data;
	const totalConsumption = selectedData.reduce((sum, item) => sum + item.value, 0);
	const avgConsumption = totalConsumption / selectedData.length;
	const maxConsumption = Math.max(...selectedData.map(item => item.value));
	const minConsumption = Math.min(...selectedData.map(item => item.value));
	
	uni.showModal({
		title: "选择区域统计",
		content: `时间范围：${selection.startTime} - ${selection.endTime}\n总用电量：${totalConsumption.toFixed(2)}kWh\n平均用电量：${avgConsumption.toFixed(2)}kWh\n最大值：${maxConsumption.toFixed(2)}kWh\n最小值：${minConsumption.toFixed(2)}kWh\n总电费：¥${(totalConsumption * parseFloat(electricityPrice.value)).toFixed(2)}`,
		confirmText: "导出数据",
		cancelText: "关闭",
		success: (res) => {
			if (res.confirm) {
				exportSelectionData(selectedData);
			}
		}
	});
};

// 导出选择的数据
const exportSelectionData = async (data: any[]) => {
	try {
		const csvContent = generateCSV(data);
		await saveToFile(csvContent, `energy_selection_${Date.now()}.csv`);
		uni.showToast({
			title: "数据导出成功",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: "导出失败",
			icon: "none"
		});
	}
};

// 刷新数据
const refreshData = async () => {
	isRefreshing.value = true;
	try {
		await Promise.all([
			energyMonitorStore.getStatistics(),
			energyMonitorStore.getDeviceConsumption(),
			loadChartData()
		]);
		lastUpdateTime.value = new Date().toLocaleTimeString();
		
		// 保存数据到缓存
		const cacheData = {
			stats: {
				todayConsumption: todayConsumption.value,
				monthConsumption: monthConsumption.value,
				todayCost: todayCost.value,
				co2Reduction: co2Reduction.value
			},
			chartData: chartData.value,
			deviceRanking: deviceRanking.value,
			realtimeData: realtimeData.value,
			alerts: alertList.value,
			lastUpdateTime: lastUpdateTime.value
		};
		saveCachedData(cacheData);
	} catch (error: any) {
		uni.showToast({
			title: error.message || "刷新失败",
			icon: "none"
		});
	} finally {
		isRefreshing.value = false;
	}
};

// 切换分析类型
const switchAnalysis = (type: string) => {
	activeAnalysis.value = type;
};

// 显示统计详情
const showStatDetail = (type: string) => {
	selectedStatType.value = type;
	switch (type) {
		case "today":
			statDetailTitle.value = "今日用电详情";
			break;
		case "month":
			statDetailTitle.value = "本月用电详情";
			break;
		case "cost":
			statDetailTitle.value = "电费详情";
			break;
		case "co2":
			statDetailTitle.value = "CO₂减排详情";
			break;
	}
	showStatDetail.value = true;
};

// 获取预警图标
const getAlertIcon = (level: string) => {
	switch (level) {
		case "high":
			return "cl-icon-warning";
		case "medium":
			return "cl-icon-info";
		case "low":
			return "cl-icon-check";
		default:
			return "cl-icon-info";
	}
};

// 处理预警
const handleAlert = (alert: any) => {
	uni.showModal({
		title: alert.title,
		content: alert.description,
		confirmText: "处理",
		cancelText: "忽略",
		success: (res) => {
			if (res.confirm) {
				resolveAlert(alert.id);
			} else if (res.cancel) {
				ignoreAlert(alert.id);
			}
		}
	});
};

// 解决预警
const resolveAlert = async (alertId: number) => {
	try {
		// 调用API解决预警
		await energyMonitorStore.resolveAlert(alertId);
		// 从列表中移除
		alertList.value = alertList.value.filter((alert) => alert.id !== alertId);
		uni.showToast({
			title: "预警已处理",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "处理失败",
			icon: "none"
		});
	}
};

// 忽略预警
const ignoreAlert = async (alertId: number) => {
	try {
		// 调用API忽略预警
		await energyMonitorStore.ignoreAlert(alertId);
		// 从列表中移除
		alertList.value = alertList.value.filter((alert) => alert.id !== alertId);
		uni.showToast({
			title: "预警已忽略",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
	}
};

// 离线缓存相关
const offlineData = ref(null);
const isOffline = ref(false);
const cacheKey = 'energy_monitor_cache';
const maxCacheSize = 50; // 最大缓存条数
const pullDownRefresh = ref(false); // 下拉刷新状态

// 下拉刷新相关
const refreshing = ref(false);
const pullDownRefresh = ref(false);

// WebSocket连接相关
const wsConnection = ref<WebSocket | null>(null);
const connectionStatus = ref<'connected' | 'disconnected' | 'connecting'>('disconnected');
const reconnectAttempts = ref(0);
const maxReconnectAttempts = ref(5);
const reconnectTimer = ref<number | null>(null);

// 缓存管理
const loadCachedData = () => {
	try {
		const cached = uni.getStorageSync(cacheKey);
		if (cached && cached.length > 0) {
			offlineData.value = cached;
			return true;
		}
	} catch (error) {
		console.error('加载缓存数据失败:', error);
	}
	return false;
};

const saveCachedData = (data: any) => {
	try {
		// 添加时间戳
		const dataWithTimestamp = {
			...data,
			timestamp: Date.now(),
			cached: true
		};
		
		// 添加到离线数据
		offlineData.value.unshift(dataWithTimestamp);
		
		// 限制缓存大小
		if (offlineData.value.length > maxCacheSize) {
			offlineData.value = offlineData.value.slice(0, maxCacheSize);
		}
		
		// 保存到本地存储
		uni.setStorageSync(cacheKey, offlineData.value);
	} catch (error) {
		console.error('保存缓存数据失败:', error);
	}
};

const clearCachedData = () => {
	try {
		offlineData.value = [];
		uni.removeStorageSync(cacheKey);
	} catch (error) {
		console.error('清除缓存数据失败:', error);
	}
};

// 网络状态监听
const checkNetworkStatus = () => {
	uni.getNetworkType({
		success: (res) => {
			isOffline.value = res.networkType === 'none';
			if (!isOffline.value && connectionStatus.value === 'disconnected') {
				// 网络恢复，重新连接WebSocket
				connectWebSocket();
			}
		}
	});
};

// 下拉刷新处理
const onPullDownRefresh = async () => {
	refreshing.value = true;
	pullDownRefresh.value = true;
	
	try {
		// 检查网络状态
		checkNetworkStatus();
		
		if (isOffline.value) {
			// 离线模式，加载缓存数据
			const hasCachedData = loadCachedData();
			if (hasCachedData) {
				uni.showToast({
					title: '已加载离线数据',
					icon: 'none'
				});
			} else {
				uni.showToast({
					title: '无网络连接，无缓存数据',
					icon: 'none'
				});
			}
		} else {
			// 在线模式，刷新数据
			await refreshData();
		}
	} catch (error) {
		console.error('下拉刷新失败:', error);
		uni.showToast({
			title: '刷新失败',
			icon: 'none'
		});
	} finally {
		refreshing.value = false;
		pullDownRefresh.value = false;
		uni.stopPullDownRefresh();
	}
};

// 切换自动刷新
const toggleAutoRefresh = () => {
	if (autoRefresh.value) {
		startAutoRefresh();
	} else {
		stopAutoRefresh();
	}
};

// 开始自动刷新
const startAutoRefresh = () => {
	stopAutoRefresh();
	refreshTimer.value = setInterval(
		() => {
			refreshData();
		},
		parseInt(refreshInterval.value) * 1000
	);
};

// 停止自动刷新
const stopAutoRefresh = () => {
	if (refreshTimer.value) {
		clearInterval(refreshTimer.value);
		refreshTimer.value = null;
	}
};

// 获取排行样式
const getRankClass = (index: number) => {
	if (index === 0) return "rank-first";
	if (index === 1) return "rank-second";
	if (index === 2) return "rank-third";
	return "rank-normal";
};

// 获取进度条宽度
const getBarWidth = (consumption: number) => {
	const maxConsumption = Math.max(...deviceRanking.value.map((d) => d.consumption));
	return maxConsumption > 0 ? (consumption / maxConsumption) * 100 : 0;
};

// 查看设备详情
const viewDeviceDetail = (device: any) => {
	router.push("/pages/energy/device-detail", {
		deviceId: device.id
	});
};

// 应用节能建议
const applyTip = async (tip: any) => {
	try {
		switch (tip.action) {
			case "schedule":
				// 跳转到定时设置
				router.push("/pages/energy/lighting-control");
				break;
			case "energy_mode":
				// 批量启用节能模式
				await energyDeviceStore.batchControl([], "energy_mode");
				uni.showToast({
					title: "节能模式已启用",
					icon: "success"
				});
				break;
			case "upgrade":
			case "sensor":
				// 显示建议详情
				uni.showModal({
					title: tip.title,
					content: tip.description + "\n\n建议联系专业人员进行升级改造。",
					confirmText: "联系客服",
					success: (res) => {
						if (res.confirm) {
							// 跳转到客服页面或拨打电话
							uni.makePhoneCall({
								phoneNumber: "************"
							});
						}
					}
				});
				break;
		}
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
	}
};

// 生成CSV内容
const generateCSV = (data: any[]) => {
	const headers = ["时间", "用电量(kWh)", "电费(元)", "CO₂减排(kg)"];
	const rows = data.map(item => [
		item.time,
		item.value.toFixed(2),
		(item.value * parseFloat(electricityPrice.value)).toFixed(2),
		(item.value * co2Factor.value).toFixed(2)
	]);
	
	return [headers, ...rows].map(row => row.join(",")).join("\n");
};

// 保存文件到本地
const saveToFile = async (content: string, filename: string) => {
	try {
		// 使用uni-app的文件系统API
		const fs = uni.getFileSystemManager();
		const filePath = `${uni.env.USER_DATA_PATH}/${filename}`;
		
		fs.writeFileSync(filePath, content, "utf8");
		
		// 分享文件
		uni.shareWithSystem({
			type: "file",
			filePath: filePath,
			success: () => {
				console.log("文件分享成功");
			},
			fail: (error) => {
				console.error("文件分享失败:", error);
			}
		});
	} catch (error) {
		throw new Error("文件保存失败");
	}
};

// 导出报告
const exportReport = async () => {
	try {
		uni.showLoading({
			title: "生成报告中..."
		});

		const reportData = {
			timeRange: activeTab.value,
			startDate: customStartDate.value || "",
			endDate: customEndDate.value || "",
			consumptionData: chartData.value,
			deviceRanking: deviceRanking.value,
			stats: {
				todayConsumption: todayConsumption.value,
				monthConsumption: monthConsumption.value,
				todayCost: todayCost.value,
				co2Reduction: co2Reduction.value
			},
			realtimeData: realtimeData.value,
			alerts: alertList.value
		};

		// 生成多种格式的报告
		const csvContent = generateReportCSV(reportData);
		const jsonContent = JSON.stringify(reportData, null, 2);
		
		// 保存CSV格式
		await saveToFile(csvContent, `energy_report_${Date.now()}.csv`);
		
		// 保存JSON格式
		await saveToFile(jsonContent, `energy_report_${Date.now()}.json`);

		uni.hideLoading();
		uni.showToast({
			title: "报告导出成功",
			icon: "success"
		});
	} catch (error: any) {
		uni.hideLoading();
		uni.showToast({
			title: error.message || "导出失败",
			icon: "none"
		});
	}
};

// 生成报告CSV
const generateReportCSV = (reportData: any) => {
	let csv = "节能灯能耗监控报告\n";
	csv += `生成时间,${new Date().toLocaleString()}\n`;
	csv += `时间范围,${reportData.timeRange}\n`;
	csv += `开始日期,${reportData.startDate}\n`;
	csv += `结束日期,${reportData.endDate}\n\n`;
	
	csv += "统计概览\n";
	csv += `今日用电量,${reportData.stats.todayConsumption}kWh\n`;
	csv += `本月用电量,${reportData.stats.monthConsumption}kWh\n`;
	csv += `今日电费,¥${reportData.stats.todayCost}\n`;
	csv += `CO₂减排,${reportData.stats.co2Reduction}kg\n\n`;
	
	csv += "实时数据\n";
	csv += `功率,${reportData.realtimeData.power}W\n`;
	csv += `电流,${reportData.realtimeData.current}A\n`;
	csv += `电压,${reportData.realtimeData.voltage}V\n`;
	csv += `频率,${reportData.realtimeData.frequency}Hz\n`;
	csv += `功率因数,${reportData.realtimeData.powerFactor}\n\n`;
	
	csv += "设备排行\n";
	csv += "排名,设备名称,位置,用电量(kWh)\n";
	reportData.deviceRanking.forEach((device, index) => {
		csv += `${index + 1},${device.name},${device.location},${device.consumption}\n`;
	});
	
	csv += "\n历史数据\n";
	csv += "时间,用电量(kWh),电费(元),CO₂减排(kg)\n";
	reportData.consumptionData.forEach(item => {
		csv += `${item.time},${item.value},${(item.value * parseFloat(electricityPrice.value)).toFixed(2)},${(item.value * co2Factor.value).toFixed(2)}\n`;
	});
	
	return csv;
};

// 刷新数据
const refreshData = async () => {
	isRefreshing.value = true;
	try {
		await Promise.all([
			energyMonitorStore.getStatistics(),
			energyMonitorStore.getDeviceConsumption(),
			loadChartData()
		]);
		lastUpdateTime.value = new Date().toLocaleTimeString();
	} catch (error: any) {
		uni.showToast({
			title: error.message || "刷新失败",
			icon: "none"
		});
	} finally {
		isRefreshing.value = false;
	}
};

// 显示统计详情
const showStatDetail = (type: string) => {
	selectedStatType.value = type;
	switch (type) {
		case "today":
			statDetailTitle.value = "今日用电详情";
			break;
		case "month":
			statDetailTitle.value = "本月用电详情";
			break;
		case "cost":
			statDetailTitle.value = "电费详情";
			break;
		case "co2":
			statDetailTitle.value = "CO₂减排详情";
			break;
	}
	showStatDetail.value = true;
};

// 切换分析类型
const switchAnalysis = (type: string) => {
	activeAnalysis.value = type;
};

// 获取预警图标
const getAlertIcon = (level: string) => {
	switch (level) {
		case "high":
			return "cl-icon-warning";
		case "medium":
			return "cl-icon-info";
		case "low":
			return "cl-icon-check";
		default:
			return "cl-icon-info";
	}
};

// 处理预警
const handleAlert = (alert: any) => {
	uni.showModal({
		title: alert.title,
		content: alert.description,
		confirmText: "处理",
		cancelText: "忽略",
		success: (res) => {
			if (res.confirm) {
				resolveAlert(alert.id);
			} else if (res.cancel) {
				ignoreAlert(alert.id);
			}
		}
	});
};

// 解决预警
const resolveAlert = async (alertId: number) => {
	try {
		// 调用API解决预警
		await energyMonitorStore.resolveAlert(alertId);
		// 从列表中移除
		alertList.value = alertList.value.filter((alert) => alert.id !== alertId);
		uni.showToast({
			title: "预警已处理",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "处理失败",
			icon: "none"
		});
	}
};

// 忽略预警
const ignoreAlert = async (alertId: number) => {
	try {
		// 调用API忽略预警
		await energyMonitorStore.ignoreAlert(alertId);
		// 从列表中移除
		alertList.value = alertList.value.filter((alert) => alert.id !== alertId);
		uni.showToast({
			title: "预警已忽略",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
	}
};

// 切换自动刷新
const toggleAutoRefresh = () => {
	if (autoRefresh.value) {
		startAutoRefresh();
	} else {
		stopAutoRefresh();
	}
};

// 开始自动刷新
const startAutoRefresh = () => {
	stopAutoRefresh();
	refreshTimer.value = setInterval(
		() => {
			refreshData();
		},
		parseInt(refreshInterval.value) * 1000
	);
};

// 停止自动刷新
const stopAutoRefresh = () => {
	if (refreshTimer.value) {
		clearInterval(refreshTimer.value);
		refreshTimer.value = null;
	}
};

// WebSocket连接管理
const connectWebSocket = () => {
	if (wsConnection.value) {
		wsConnection.value.close();
	}

	connectionStatus.value = "connecting";
	reconnectAttempts.value++;

	try {
		// 创建WebSocket连接
		wsConnection.value = uni.connectSocket({
			url: "wss://api.example.com/energy/realtime",
			header: {
				"Authorization": "Bearer " + uni.getStorageSync("token")
			}
		});

		wsConnection.value.onOpen(() => {
			connectionStatus.value = "connected";
			reconnectAttempts.value = 0;
			isRealtime.value = true;
			console.log("WebSocket连接成功");
		});

		wsConnection.value.onMessage((res) => {
			try {
				const data = JSON.parse(res.data);
				handleRealtimeData(data);
			} catch (error) {
				console.error("解析实时数据失败:", error);
			}
		});

		wsConnection.value.onError((error) => {
			console.error("WebSocket连接错误:", error);
			connectionStatus.value = "disconnected";
			isRealtime.value = false;
			scheduleReconnect();
		});

		wsConnection.value.onClose(() => {
			connectionStatus.value = "disconnected";
			isRealtime.value = false;
			if (reconnectAttempts.value < maxReconnectAttempts) {
				scheduleReconnect();
			}
		});
	} catch (error) {
		console.error("创建WebSocket连接失败:", error);
		connectionStatus.value = "disconnected";
		isRealtime.value = false;
	}
};

// 处理实时数据
const handleRealtimeData = (data: any) => {
	if (data.type === "realtime") {
		realtimeData.value = {
			power: data.power || 0,
			current: data.current || 0,
			voltage: data.voltage || 220,
			frequency: data.frequency || 50,
			powerFactor: data.powerFactor || 0.95
		};
		lastUpdateTime.value = new Date().toLocaleTimeString();
	} else if (data.type === "consumption") {
		// 更新消耗数据
		energyMonitorStore.updateRealtimeConsumption(data);
	} else if (data.type === "alert") {
		// 处理预警消息
		handleRealtimeAlert(data);
	}
};

// 处理实时预警
const handleRealtimeAlert = (alertData: any) => {
	// 添加到预警列表
	alertList.value.unshift({
		id: Date.now(),
		title: alertData.title,
		description: alertData.description,
		level: alertData.level,
		time: "刚刚"
	});

	// 更新预警统计
	if (alertLevels.value[alertData.level] !== undefined) {
		alertLevels.value[alertData.level]++;
	}

	// 显示通知
	if (alertNotification.value) {
		uni.showToast({
			title: alertData.title,
			icon: "none",
			duration: 3000
		});
	}
};

// 计划重连
const scheduleReconnect = () => {
	if (reconnectTimer.value) {
		clearTimeout(reconnectTimer.value);
	}

	const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 30000);
	reconnectTimer.value = setTimeout(() => {
		if (reconnectAttempts.value < maxReconnectAttempts) {
			connectWebSocket();
		}
	}, delay);
};

// 断开WebSocket连接
const disconnectWebSocket = () => {
	if (wsConnection.value) {
		wsConnection.value.close();
		wsConnection.value = null;
	}
	if (reconnectTimer.value) {
		clearTimeout(reconnectTimer.value);
		reconnectTimer.value = null;
	}
	connectionStatus.value = "disconnected";
	isRealtime.value = false;
};

// 监听时间选项卡变化
watch(activeTab, () => {
	if (activeTab.value !== "custom") {
		customStartDate.value = "";
		customEndDate.value = "";
	}
});

onMounted(async () => {
	// 检查网络状态
	checkNetworkStatus();
	
	// 监听网络状态变化
	uni.onNetworkStatusChange((res) => {
		isOffline.value = !res.isConnected;
		if (res.isConnected && connectionStatus.value === 'disconnected') {
			// 网络恢复，重新连接WebSocket
			connectWebSocket();
			// 刷新数据
			refreshData();
		}
	});
	
	try {
		if (isOffline.value) {
			// 离线模式，尝试加载缓存数据
			const hasCachedData = loadCachedData();
			if (hasCachedData) {
				uni.showToast({
					title: '已加载离线数据',
					icon: 'none'
				});
			}
		} else {
			// 在线模式，加载初始数据
			await Promise.all([
				energyMonitorStore.getStatistics(),
				energyMonitorStore.getDeviceConsumption(),
				loadChartData()
			]);
			
			// 连接WebSocket
			connectWebSocket();
		}
		
		// 启用下拉刷新
		uni.startPullDownRefresh();
		
		if (autoRefresh.value) {
			startAutoRefresh();
		}
	} catch (error) {
		console.error('初始化失败:', error);
		// 尝试加载缓存数据作为备选
		loadCachedData();
	}
});

// 组件卸载时清理定时器和连接
onUnmounted(() => {
	stopAutoRefresh();
	disconnectWebSocket();
	// 移除网络状态监听
	uni.offNetworkStatusChange();
});

// 监听刷新间隔变化
watch(refreshInterval, () => {
	if (autoRefresh.value) {
		startAutoRefresh();
	}
});
</script>

<style scoped>
.energy-monitor {
	padding: 20rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.realtime-indicator {
	display: flex;
	align-items: center;
	gap: 15rpx;
	padding: 20rpx 30rpx;
	background-color: white;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.indicator-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background-color: #ff4d4f;
	animation: blink 2s infinite;
}

.indicator-dot.online {
	background-color: #52c41a;
	animation: none;
}

.indicator-dot.offline {
	background-color: #ff4d4f;
	animation: blink 2s infinite;
}

.indicator-dot.connecting {
	background-color: #faad14;
	animation: pulse 1.5s infinite;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.2);
		opacity: 0.7;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

@keyframes blink {
	0%,
	50% {
		opacity: 1;
	}
	51%,
	100% {
		opacity: 0.3;
	}
}

.indicator-text {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.update-time {
	font-size: 12px;
	color: #666;
	margin-left: auto;
}

.offline-tip {
	font-size: 12px;
	color: #faad14;
	margin-left: 10rpx;
	font-style: italic;
}

.rotating {
	animation: rotate 1s linear infinite;
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.stats-overview {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.stat-card {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stat-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background-color: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.stat-info {
	flex: 1;
}

.stat-value {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.stat-label {
	font-size: 12px;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.stat-trend {
	display: flex;
	align-items: center;
	gap: 5rpx;
	font-size: 12px;
}

.stat-trend.increase {
	color: #ff4d4f;
}

.stat-trend.decrease {
	color: #52c41a;
}

.trend-text {
	font-weight: 500;
}

.time-selector {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.selector-tabs {
	display: flex;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.tab-item {
	padding: 15rpx 25rpx;
	border-radius: 20rpx;
	background-color: #f0f0f0;
	color: #666;
	font-size: 14px;
	transition: all 0.3s ease;
}

.tab-item.active {
	background-color: #1890ff;
	color: white;
}

.custom-time {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.date-separator {
	color: #666;
	font-size: 14px;
}

.analysis-section {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.analysis-tabs {
	display: flex;
	gap: 10rpx;
}

.analysis-tab {
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	background-color: #f0f0f0;
	color: #666;
	font-size: 12px;
	transition: all 0.3s ease;
}

.analysis-tab.active {
	background-color: #1890ff;
	color: white;
}

.chart-container {
	background-color: #f9f9f9;
	border-radius: 8rpx;
	padding: 20rpx;
}

.compare-analysis {
	padding: 20rpx 0;
}

.compare-options {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.compare-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.compare-label {
	font-size: 14px;
	color: #333;
	width: 120rpx;
	flex-shrink: 0;
}

.compare-results {
	padding: 20rpx 0;
}

.result-card {
	background-color: #f9f9f9;
	border-radius: 8rpx;
	padding: 20rpx;
}

.result-title {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.result-data {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.data-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.data-label {
	font-size: 12px;
	color: #666;
}

.data-value {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.data-value.increase {
	color: #ff4d4f;
}

.data-value.decrease {
	color: #52c41a;
}

.alert-analysis {
	padding: 20rpx 0;
}

.alert-summary {
	display: flex;
	justify-content: space-around;
	margin-bottom: 30rpx;
}

.alert-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	padding: 20rpx;
	border-radius: 8rpx;
	background-color: #f0f0f0;
	color: #999;
	transition: all 0.3s ease;
}

.alert-item.high {
	background-color: #fff2f0;
	color: #ff4d4f;
}

.alert-item.medium {
	background-color: #fff7e6;
	color: #fa8c16;
}

.alert-item.low {
	background-color: #f6ffed;
	color: #52c41a;
}

.alert-count {
	font-size: 18px;
	font-weight: bold;
}

.alert-label {
	font-size: 12px;
}

.alert-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.alert-card {
	padding: 20rpx;
	border-radius: 8rpx;
	border-left: 4rpx solid #ddd;
	background-color: #f9f9f9;
}

.alert-card.high {
	border-left-color: #ff4d4f;
	background-color: #fff2f0;
}

.alert-card.medium {
	border-left-color: #fa8c16;
	background-color: #fff7e6;
}

.alert-card.low {
	border-left-color: #52c41a;
	background-color: #f6ffed;
}

.alert-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 10rpx;
}

.alert-title {
	flex: 1;
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.alert-time {
	font-size: 12px;
	color: #666;
}

.alert-desc {
	font-size: 12px;
	color: #666;
	line-height: 1.4;
	margin-bottom: 15rpx;
	display: block;
}

.alert-actions {
	display: flex;
	gap: 10rpx;
	justify-content: flex-end;
}

.chart-container {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.chart-title {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.chart-legend {
	display: flex;
	gap: 20rpx;
}

.legend-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.legend-color {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
}

.legend-text {
	font-size: 12px;
	color: #666;
}

.chart-controls {
	display: flex;
	justify-content: center;
	gap: 15rpx;
	margin-top: 20rpx;
}

.chart-btn {
	min-width: 100rpx;
}

.device-ranking {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.ranking-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.ranking-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	transition: all 0.3s ease;
}

.ranking-item:active {
	background-color: #f0f0f0;
}

.ranking-number {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #e0e0e0;
}

.rank-text {
	font-size: 16px;
	font-weight: bold;
	color: white;
}

.rank-first {
	background-color: #ffd700 !important;
}

.rank-second {
	background-color: #c0c0c0 !important;
}

.rank-third {
	background-color: #cd7f32 !important;
}

.rank-normal {
	color: #666 !important;
}

.device-info {
	flex: 1;
}

.device-name {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.device-location {
	font-size: 12px;
	color: #666;
	display: block;
}

.consumption-info {
	width: 150rpx;
	text-align: right;
}

.consumption-value {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.consumption-bar {
	height: 6rpx;
	background-color: #f0f0f0;
	border-radius: 3rpx;
	overflow: hidden;
}

.bar-fill {
	height: 100%;
	background: linear-gradient(90deg, #52c41a, #1890ff);
	border-radius: 3rpx;
	transition: width 0.3s ease;
}

.energy-tips {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tips-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.tip-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
	border-left: 4rpx solid #1890ff;
}

.tip-icon {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	background-color: white;
	display: flex;
	align-items: center;
	justify-content: center;
}

.tip-content {
	flex: 1;
}

.tip-title {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.tip-desc {
	font-size: 12px;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
	line-height: 1.4;
}

.tip-saving {
	font-size: 12px;
	color: #52c41a;
	font-weight: 500;
	display: block;
}

.export-section {
	padding: 30rpx;
	text-align: center;
}

.export-btn {
	width: 100%;
	max-width: 400rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
}

.settings-modal {
	padding: 30rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 18px;
	font-weight: 500;
	color: #333;
}

.settings-content {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.setting-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.setting-label {
	font-size: 14px;
	color: #333;
	flex: 1;
}

.stat-detail-modal {
	padding: 30rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.stat-detail-content {
	padding: 20rpx 0;
}

.detail-section {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.detail-label {
	font-size: 14px;
	color: #666;
}

.detail-value {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.detail-value.increase {
	color: #ff4d4f;
}

.detail-value.decrease {
	color: #52c41a;
}
</style>
