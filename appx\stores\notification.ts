import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Notification, PageParams, PageResult } from '@/types'
import { notificationApi } from '@/utils/api'

// 定义通知store
export const useNotificationStore = defineStore('notification', () => {
  // 响应式数据
  const notifications = ref<Notification[]>([])
  const unreadCount = ref(0)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取通知列表
  const getNotifications = async (params: { page: number, pageSize: number }) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      // 实际项目中应该替换为真实的API调用
      const response = await uni.request({
        url: '/api/notifications',
        method: 'GET',
        data: params
      })

      if (response.statusCode === 200 && response.data.success) {
        const data = response.data.data
        notifications.value = data.items
        updateUnreadCount()
        return {
          items: data.items,
          total: data.total
        }
      } else {
        error.value = response.data.message || '获取通知列表失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return {
          items: [],
          total: 0
        }
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to get notifications:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return {
        items: [],
          total: 0
        }
    } finally {
      loading.value = false
    }
  }

  // 获取未读通知数量
  const getUnreadCount = async (): Promise<number> => {
    try {
      const count = await notificationApi.getUnreadCount()
      unreadCount.value = count
      return count
    } catch (err) {
      console.error('Failed to get unread count:', err)
      // 返回模拟数据
      const count = 3
      unreadCount.value = count
      return count
    }
  }

  // 标记通知为已读
  const markAsRead = async (notificationId: string) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/notifications/${notificationId}/read`,
        method: 'POST'
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地通知状态
        const index = notifications.value.findIndex(notify => notify.id === notificationId)
        if (index !== -1) {
          notifications.value[index].read = true
        }
        updateUnreadCount()
        return true
      } else {
        error.value = response.data.message || '标记通知为已读失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to mark notification as read:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: '/api/notifications/readAll',
        method: 'POST'
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地通知状态
        notifications.value.forEach(notify => {
          notify.read = true
        })
        updateUnreadCount()
        return true
      } else {
        error.value = response.data.message || '标记所有通知为已读失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to mark all notifications as read:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 更新未读通知数量
  const updateUnreadCount = () => {
    unreadCount.value = notifications.value.filter(notify => !notify.read).length
  }

  return {
    notifications,
    unreadCount,
    loading,
    error,
    getNotifications,
    getUnreadCount,
    markAsRead,
    markAllAsRead
  }
})