/**
 * 性能优化工具类
 * 包含防抖、节流、图片懒加载、内存优化等功能
 */

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @param immediate 是否立即执行
 */
export function debounce<T extends (...args: any[]) => any>(
	func: T,
	delay: number,
	immediate: boolean = false
): (...args: Parameters<T>) => void {
	let timeoutId: number | null = null
	let lastCallTime = 0
	
	return function (this: any, ...args: Parameters<T>) {
		const now = Date.now()
		const callNow = immediate && !timeoutId
		
		if (timeoutId) {
			clearTimeout(timeoutId)
		}
		
		timeoutId = setTimeout(() => {
			timeoutId = null
			if (!immediate) {
				func.apply(this, args)
			}
		}, delay)
		
		if (callNow) {
			func.apply(this, args)
		}
		
		lastCallTime = now
	}
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @param options 选项
 */
export function throttle<T extends (...args: any[]) => any>(
	func: T,
	delay: number,
	options: { leading?: boolean; trailing?: boolean } = {}
): (...args: Parameters<T>) => void {
	const { leading = true, trailing = true } = options
	let timeoutId: number | null = null
	let lastCallTime = 0
	let lastArgs: Parameters<T> | null = null
	
	return function (this: any, ...args: Parameters<T>) {
		const now = Date.now()
		lastArgs = args
		
		if (lastCallTime === 0 && !leading) {
			lastCallTime = now
			return
		}
		
		const remaining = delay - (now - lastCallTime)
		
		if (remaining <= 0 || remaining > delay) {
			if (timeoutId) {
				clearTimeout(timeoutId)
				timeoutId = null
			}
			lastCallTime = now
			func.apply(this, args)
		} else if (!timeoutId && trailing) {
			timeoutId = setTimeout(() => {
				lastCallTime = leading ? Date.now() : 0
				timeoutId = null
				if (lastArgs) {
					func.apply(this, lastArgs)
				}
			}, remaining)
		}
	}
}

/**
 * 图片懒加载类
 */
export class LazyImageLoader {
	private observer: IntersectionObserver | null = null
	private imageCache = new Map<string, HTMLImageElement>()
	private loadingImages = new Set<string>()
	
	constructor(options: IntersectionObserverInit = {}) {
		if (typeof IntersectionObserver !== 'undefined') {
			this.observer = new IntersectionObserver(
				this.handleIntersection.bind(this),
				{
					rootMargin: '50px',
					threshold: 0.1,
					...options
				}
			)
		}
	}
	
	/**
	 * 观察图片元素
	 */
	observe(element: Element) {
		if (this.observer) {
			this.observer.observe(element)
		} else {
			// 降级处理：直接加载图片
			this.loadImage(element as HTMLImageElement)
		}
	}
	
	/**
	 * 停止观察图片元素
	 */
	unobserve(element: Element) {
		if (this.observer) {
			this.observer.unobserve(element)
		}
	}
	
	/**
	 * 处理交叉观察
	 */
	private handleIntersection(entries: IntersectionObserverEntry[]) {
		entries.forEach(entry => {
			if (entry.isIntersecting) {
				const img = entry.target as HTMLImageElement
				this.loadImage(img)
				this.observer?.unobserve(img)
			}
		})
	}
	
	/**
	 * 加载图片
	 */
	private async loadImage(img: HTMLImageElement) {
		const src = img.dataset.src
		if (!src || this.loadingImages.has(src)) {
			return
		}
		
		this.loadingImages.add(src)
		
		try {
			// 检查缓存
			if (this.imageCache.has(src)) {
				img.src = src
				img.classList.add('loaded')
				return
			}
			
			// 预加载图片
			const image = new Image()
			image.onload = () => {
				this.imageCache.set(src, image)
				img.src = src
				img.classList.add('loaded')
				this.loadingImages.delete(src)
			}
			
			image.onerror = () => {
				img.classList.add('error')
				this.loadingImages.delete(src)
			}
			
			image.src = src
		} catch (error) {
			console.error('Image load failed:', error)
			img.classList.add('error')
			this.loadingImages.delete(src)
		}
	}
	
	/**
	 * 清理缓存
	 */
	clearCache() {
		this.imageCache.clear()
		this.loadingImages.clear()
	}
	
	/**
	 * 销毁
	 */
	destroy() {
		if (this.observer) {
			this.observer.disconnect()
			this.observer = null
		}
		this.clearCache()
	}
}

/**
 * 内存优化工具
 */
export class MemoryOptimizer {
	private static instance: MemoryOptimizer
	private timers = new Set<number>()
	private intervals = new Set<number>()
	private eventListeners = new Map<Element, Array<{ event: string; handler: EventListener }>>()
	
	static getInstance(): MemoryOptimizer {
		if (!MemoryOptimizer.instance) {
			MemoryOptimizer.instance = new MemoryOptimizer()
		}
		return MemoryOptimizer.instance
	}
	
	/**
	 * 安全的 setTimeout
	 */
	setTimeout(callback: Function, delay: number): number {
		const timerId = setTimeout(() => {
			this.timers.delete(timerId)
			callback()
		}, delay)
		this.timers.add(timerId)
		return timerId
	}
	
	/**
	 * 安全的 setInterval
	 */
	setInterval(callback: Function, delay: number): number {
		const intervalId = setInterval(callback, delay)
		this.intervals.add(intervalId)
		return intervalId
	}
	
	/**
	 * 清除定时器
	 */
	clearTimeout(timerId: number) {
		clearTimeout(timerId)
		this.timers.delete(timerId)
	}
	
	/**
	 * 清除间隔器
	 */
	clearInterval(intervalId: number) {
		clearInterval(intervalId)
		this.intervals.delete(intervalId)
	}
	
	/**
	 * 安全的事件监听器
	 */
	addEventListener(element: Element, event: string, handler: EventListener, options?: AddEventListenerOptions) {
		element.addEventListener(event, handler, options)
		
		if (!this.eventListeners.has(element)) {
			this.eventListeners.set(element, [])
		}
		this.eventListeners.get(element)!.push({ event, handler })
	}
	
	/**
	 * 移除事件监听器
	 */
	removeEventListener(element: Element, event: string, handler: EventListener) {
		element.removeEventListener(event, handler)
		
		const listeners = this.eventListeners.get(element)
		if (listeners) {
			const index = listeners.findIndex(l => l.event === event && l.handler === handler)
			if (index > -1) {
				listeners.splice(index, 1)
			}
			if (listeners.length === 0) {
				this.eventListeners.delete(element)
			}
		}
	}
	
	/**
	 * 清理所有资源
	 */
	cleanup() {
		// 清理定时器
		this.timers.forEach(timerId => clearTimeout(timerId))
		this.timers.clear()
		
		// 清理间隔器
		this.intervals.forEach(intervalId => clearInterval(intervalId))
		this.intervals.clear()
		
		// 清理事件监听器
		this.eventListeners.forEach((listeners, element) => {
			listeners.forEach(({ event, handler }) => {
				element.removeEventListener(event, handler)
			})
		})
		this.eventListeners.clear()
	}
	
	/**
	 * 获取内存使用情况
	 */
	getMemoryUsage() {
		if ('memory' in performance) {
			return {
				usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
				totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
				jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
				timers: this.timers.size,
				intervals: this.intervals.size,
				eventListeners: this.eventListeners.size
			}
		}
		return {
			timers: this.timers.size,
			intervals: this.intervals.size,
			eventListeners: this.eventListeners.size
		}
	}
}

/**
 * 虚拟滚动类
 */
export class VirtualScroller {
	private container: HTMLElement
	private itemHeight: number
	private visibleCount: number
	private startIndex = 0
	private endIndex = 0
	private scrollTop = 0
	private data: any[] = []
	private renderCallback: (item: any, index: number) => HTMLElement
	
	constructor(
		container: HTMLElement,
		itemHeight: number,
		renderCallback: (item: any, index: number) => HTMLElement
	) {
		this.container = container
		this.itemHeight = itemHeight
		this.renderCallback = renderCallback
		this.visibleCount = Math.ceil(container.clientHeight / itemHeight) + 2
		
		this.bindEvents()
	}
	
	/**
	 * 设置数据
	 */
	setData(data: any[]) {
		this.data = data
		this.render()
	}
	
	/**
	 * 绑定事件
	 */
	private bindEvents() {
		const throttledScroll = throttle(() => {
			this.handleScroll()
		}, 16) // 60fps
		
		this.container.addEventListener('scroll', throttledScroll)
	}
	
	/**
	 * 处理滚动
	 */
	private handleScroll() {
		this.scrollTop = this.container.scrollTop
		const newStartIndex = Math.floor(this.scrollTop / this.itemHeight)
		const newEndIndex = Math.min(
			newStartIndex + this.visibleCount,
			this.data.length - 1
		)
		
		if (newStartIndex !== this.startIndex || newEndIndex !== this.endIndex) {
			this.startIndex = newStartIndex
			this.endIndex = newEndIndex
			this.render()
		}
	}
	
	/**
	 * 渲染
	 */
	private render() {
		const fragment = document.createDocumentFragment()
		
		// 清空容器
		this.container.innerHTML = ''
		
		// 创建占位元素（上方）
		const topSpacer = document.createElement('div')
		topSpacer.style.height = `${this.startIndex * this.itemHeight}px`
		fragment.appendChild(topSpacer)
		
		// 渲染可见项
		for (let i = this.startIndex; i <= this.endIndex && i < this.data.length; i++) {
			const item = this.renderCallback(this.data[i], i)
			fragment.appendChild(item)
		}
		
		// 创建占位元素（下方）
		const bottomSpacer = document.createElement('div')
		const remainingHeight = (this.data.length - this.endIndex - 1) * this.itemHeight
		bottomSpacer.style.height = `${Math.max(0, remainingHeight)}px`
		fragment.appendChild(bottomSpacer)
		
		this.container.appendChild(fragment)
	}
}

// 导出单例实例
export const memoryOptimizer = MemoryOptimizer.getInstance()
export const lazyImageLoader = new LazyImageLoader()

// 导出便捷方法
export const safeSetTimeout = (callback: Function, delay: number) => {
	return memoryOptimizer.setTimeout(callback, delay)
}

export const safeSetInterval = (callback: Function, delay: number) => {
	return memoryOptimizer.setInterval(callback, delay)
}

export const safeClearTimeout = (timerId: number) => {
	memoryOptimizer.clearTimeout(timerId)
}

export const safeClearInterval = (intervalId: number) => {
	memoryOptimizer.clearInterval(intervalId)
}

export const safeAddEventListener = (
	element: Element,
	event: string,
	handler: EventListener,
	options?: AddEventListenerOptions
) => {
	memoryOptimizer.addEventListener(element, event, handler, options)
}

export const safeRemoveEventListener = (
	element: Element,
	event: string,
	handler: EventListener
) => {
	memoryOptimizer.removeEventListener(element, event, handler)
}