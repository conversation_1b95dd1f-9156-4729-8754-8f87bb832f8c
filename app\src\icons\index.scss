// 图标样式文件
// 这里可以定义项目中使用的图标样式

// 基础图标样式
.cl-icon {
  display: inline-block;
  font-style: normal;
  vertical-align: baseline;
  text-align: center;
  text-transform: none;
  line-height: 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 图标大小
.cl-icon--small {
  font-size: 12px;
}

.cl-icon--medium {
  font-size: 16px;
}

.cl-icon--large {
  font-size: 20px;
}

.cl-icon--xlarge {
  font-size: 24px;
}

// 图标颜色
.cl-icon--primary {
  color: #007aff;
}

.cl-icon--success {
  color: #4cd964;
}

.cl-icon--warning {
  color: #f0ad4e;
}

.cl-icon--danger {
  color: #dd524d;
}

.cl-icon--info {
  color: #909399;
}

// 旋转动画
.cl-icon--spin {
  animation: cl-icon-spin 1s linear infinite;
}

@keyframes cl-icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}