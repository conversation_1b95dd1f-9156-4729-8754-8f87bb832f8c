<template>
	<view class="loading-skeleton" :class="[`type-${type}`, `size-${size}`]">
		<!-- 卡片骨架 -->
		<view v-if="type === 'card'" class="skeleton-card">
			<view class="skeleton-header">
				<view class="skeleton-avatar" :class="`size-${size}`"></view>
				<view class="skeleton-content">
					<view class="skeleton-line" :style="{ width: '60%' }"></view>
					<view class="skeleton-line" :style="{ width: '40%' }"></view>
				</view>
			</view>
			<view v-if="showImage" class="skeleton-image"></view>
			<view class="skeleton-body">
				<view 
					v-for="line in lines" 
					:key="line" 
					class="skeleton-line" 
					:style="{ width: getLineWidth(line) }"
				></view>
			</view>
			<view v-if="showActions" class="skeleton-actions">
				<view class="skeleton-button"></view>
				<view class="skeleton-button"></view>
			</view>
		</view>
		
		<!-- 列表骨架 -->
		<view v-else-if="type === 'list'" class="skeleton-list">
			<view 
				v-for="item in count" 
				:key="item" 
				class="skeleton-list-item"
			>
				<view class="skeleton-avatar" :class="`size-${size}`"></view>
				<view class="skeleton-content">
					<view class="skeleton-line" :style="{ width: '70%' }"></view>
					<view class="skeleton-line" :style="{ width: '50%' }"></view>
				</view>
				<view class="skeleton-meta">
					<view class="skeleton-line" :style="{ width: '30px' }"></view>
				</view>
			</view>
		</view>
		
		<!-- 文本骨架 -->
		<view v-else-if="type === 'text'" class="skeleton-text">
			<view 
				v-for="line in lines" 
				:key="line" 
				class="skeleton-line" 
				:style="{ width: getLineWidth(line) }"
			></view>
		</view>
		
		<!-- 图表骨架 -->
		<view v-else-if="type === 'chart'" class="skeleton-chart">
			<view class="skeleton-chart-header">
				<view class="skeleton-line" :style="{ width: '40%' }"></view>
				<view class="skeleton-line" :style="{ width: '20%' }"></view>
			</view>
			<view class="skeleton-chart-body">
				<view class="skeleton-chart-y-axis">
					<view v-for="i in 5" :key="i" class="skeleton-line" :style="{ width: '20px' }"></view>
				</view>
				<view class="skeleton-chart-content">
					<view class="skeleton-chart-bars">
						<view 
							v-for="i in 7" 
							:key="i" 
							class="skeleton-chart-bar" 
							:style="{ height: getBarHeight(i) }"
						></view>
					</view>
					<view class="skeleton-chart-x-axis">
						<view v-for="i in 7" :key="i" class="skeleton-line" :style="{ width: '15px' }"></view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 表格骨架 -->
		<view v-else-if="type === 'table'" class="skeleton-table">
			<view class="skeleton-table-header">
				<view v-for="col in columns" :key="col" class="skeleton-table-cell">
					<view class="skeleton-line" :style="{ width: '80%' }"></view>
				</view>
			</view>
			<view class="skeleton-table-body">
				<view v-for="row in rows" :key="row" class="skeleton-table-row">
					<view v-for="col in columns" :key="col" class="skeleton-table-cell">
						<view class="skeleton-line" :style="{ width: getTableCellWidth(col) }"></view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 自定义骨架 -->
		<view v-else-if="type === 'custom'" class="skeleton-custom">
			<slot></slot>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { computed } from "vue";

// 骨架屏类型
type SkeletonType = 'card' | 'list' | 'text' | 'chart' | 'table' | 'custom';
type SkeletonSize = 'small' | 'medium' | 'large';

// Props
interface LoadingSkeletonProps {
	type?: SkeletonType;
	size?: SkeletonSize;
	lines?: number;
	count?: number;
	showImage?: boolean;
	showActions?: boolean;
	columns?: number;

ows?: number;
	animated?: boolean;
	theme?: 'light' | 'dark';
}

const props = withDefaults(defineProps<LoadingSkeletonProps>(), {
	type: 'card',
	size: 'medium',
	lines: 3,
	count: 3,
	showImage: false,
	showActions: false,
	columns: 4,
	rows: 5,
	animated: true,
	theme: 'light'
});

// 计算属性
const getLineWidth = (line: number) => {
	const widths = ['100%', '80%', '60%', '90%', '70%'];
	return widths[(line - 1) % widths.length];
};

const getBarHeight = (index: number) => {
	const heights = ['60%', '80%', '40%', '90%', '50%', '70%', '30%'];
	return heights[(index - 1) % heights.length];
};

const getTableCellWidth = (col: number) => {
	const widths = ['60%', '80%', '40%', '70%'];
	return widths[(col - 1) % widths.length];
};
</script>

<style scoped>
.loading-skeleton {
	padding: 0;
}

/* 基础骨架元素 */
.skeleton-line {
	height: 16rpx;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	border-radius: 4rpx;
	margin-bottom: 12rpx;
	animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-avatar {
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	border-radius: 50%;
	animation: skeleton-loading 1.5s ease-in-out infinite;
	flex-shrink: 0;
}

.skeleton-avatar.size-small {
	width: 32rpx;
	height: 32rpx;
}

.skeleton-avatar.size-medium {
	width: 48rpx;
	height: 48rpx;
}

.skeleton-avatar.size-large {
	width: 64rpx;
	height: 64rpx;
}

.skeleton-button {
	height: 32rpx;
	width: 80rpx;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	border-radius: 6rpx;
	animation: skeleton-loading 1.5s ease-in-out infinite;
}

/* 卡片骨架 */
.skeleton-card {
	padding: 24rpx;
	background-color: #fff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.skeleton-header {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 20rpx;
}

.skeleton-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.skeleton-image {
	height: 200rpx;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
	animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-body {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	margin-bottom: 20rpx;
}

.skeleton-actions {
	display: flex;
	gap: 16rpx;
	justify-content: flex-end;
}

/* 列表骨架 */
.skeleton-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.skeleton-list-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 16rpx;
	background-color: #fff;
	border-radius: 8rpx;
}

.skeleton-meta {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 8rpx;
}

/* 文本骨架 */
.skeleton-text {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	padding: 16rpx;
}

/* 图表骨架 */
.skeleton-chart {
	padding: 24rpx;
	background-color: #fff;
	border-radius: 12rpx;
}

.skeleton-chart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.skeleton-chart-body {
	display: flex;
	gap: 16rpx;
	height: 200rpx;
}

.skeleton-chart-y-axis {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: flex-end;
	width: 40rpx;
}

.skeleton-chart-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.skeleton-chart-bars {
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	height: 160rpx;
	gap: 8rpx;
}

.skeleton-chart-bar {
	flex: 1;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	border-radius: 4rpx 4rpx 0 0;
	animation: skeleton-loading 1.5s ease-in-out infinite;
}

.skeleton-chart-x-axis {
	display: flex;
	justify-content: space-between;
	gap: 8rpx;
	margin-top: 16rpx;
}

/* 表格骨架 */
.skeleton-table {
	background-color: #fff;
	border-radius: 8rpx;
	overflow: hidden;
}

.skeleton-table-header {
	display: flex;
	background-color: #fafafa;
	border-bottom: 1rpx solid #f0f0f0;
}

.skeleton-table-body {
	display: flex;
	flex-direction: column;
}

.skeleton-table-row {
	display: flex;
	border-bottom: 1rpx solid #f0f0f0;
}

.skeleton-table-row:last-child {
	border-bottom: none;
}

.skeleton-table-cell {
	flex: 1;
	padding: 16rpx;
	display: flex;
	align-items: center;
}

/* 自定义骨架 */
.skeleton-custom {
	padding: 16rpx;
}

/* 尺寸变体 */
.size-small .skeleton-line {
	height: 12rpx;
	margin-bottom: 8rpx;
}

.size-small .skeleton-button {
	height: 28rpx;
	width: 60rpx;
}

.size-large .skeleton-line {
	height: 20rpx;
	margin-bottom: 16rpx;
}

.size-large .skeleton-button {
	height: 36rpx;
	width: 100rpx;
}

/* 动画效果 */
@keyframes skeleton-loading {
	0% {
		background-position: -200% 0;
	}
	100% {
		background-position: 200% 0;
	}
}

/* 暗色主题 */
.loading-skeleton[class*="theme-dark"] .skeleton-line,
.loading-skeleton[class*="theme-dark"] .skeleton-avatar,
.loading-skeleton[class*="theme-dark"] .skeleton-button,
.loading-skeleton[class*="theme-dark"] .skeleton-image,
.loading-skeleton[class*="theme-dark"] .skeleton-chart-bar {
	background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
	background-size: 200% 100%;
}

.loading-skeleton[class*="theme-dark"] .skeleton-card,
.loading-skeleton[class*="theme-dark"] .skeleton-list-item,
.loading-skeleton[class*="theme-dark"] .skeleton-chart,
.loading-skeleton[class*="theme-dark"] .skeleton-table {
	background-color: #1a1a1a;
}

.loading-skeleton[class*="theme-dark"] .skeleton-table-header {
	background-color: #2a2a2a;
}

.loading-skeleton[class*="theme-dark"] .skeleton-table-row {
	border-bottom-color: #3a3a3a;
}

/* 禁用动画 */
.loading-skeleton:not([class*="animated"]) .skeleton-line,
.loading-skeleton:not([class*="animated"]) .skeleton-avatar,
.loading-skeleton:not([class*="animated"]) .skeleton-button,
.loading-skeleton:not([class*="animated"]) .skeleton-image,
.loading-skeleton:not([class*="animated"]) .skeleton-chart-bar {
	animation: none;
	background: #f0f0f0;
}

.loading-skeleton:not([class*="animated"])[class*="theme-dark"] .skeleton-line,
.loading-skeleton:not([class*="animated"])[class*="theme-dark"] .skeleton-avatar,
.loading-skeleton:not([class*="animated"])[class*="theme-dark"] .skeleton-button,
.loading-skeleton:not([class*="animated"])[class*="theme-dark"] .skeleton-image,
.loading-skeleton:not([class*="animated"])[class*="theme-dark"] .skeleton-chart-bar {
	background: #2a2a2a;
}
</style>