<template>
	<cl-page>
		<cl-topbar title="故障管理">
			<template #right>
				<cl-button text @click="showBatchActions = !showBatchActions">
					<cl-icon name="cl-icon-more" size="20" />
				</cl-button>
			</template>
		</cl-topbar>

		<!-- 批量操作栏 -->
		<view v-if="showBatchActions" class="batch-actions">
			<view class="batch-info">
				<cl-checkbox v-model="selectAll" @change="handleSelectAll"> 全选 </cl-checkbox>
				<text class="selected-count">已选择 {{ selectedFaults.length }} 项</text>
			</view>

			<view class="batch-buttons">
				<cl-button
					size="small"
					type="primary"
					@click="batchAssign"
					:disabled="selectedFaults.length === 0"
				>
					批量分配
				</cl-button>
				<cl-button
					size="small"
					type="success"
					@click="batchResolve"
					:disabled="selectedFaults.length === 0"
				>
					批量解决
				</cl-button>
				<cl-button
					size="small"
					type="default"
					@click="exportFaults"
					:disabled="selectedFaults.length === 0"
				>
					导出
				</cl-button>
			</view>
		</view>

		<!-- 快速操作栏 -->
		<view class="quick-actions">
			<cl-button type="primary" @click="reportFault">
				<cl-icon name="cl-icon-plus" size="16" />
				故障上报
			</cl-button>
			<cl-button type="default" @click="showMaintenancePlan = true">
				<cl-icon name="cl-icon-calendar" size="16" />
				维护计划
			</cl-button>
			<cl-button type="default" @click="showStatistics = true">
				<cl-icon name="cl-icon-bar-chart" size="16" />
				统计分析
			</cl-button>
			<cl-button type="default" @click="toggleBatchActions">
				<cl-icon name="cl-icon-check-square" size="16" />
				{{ showBatchActions ? '取消选择' : '批量操作' }}
			</cl-button>
		</view>

		<view class="fault-manage">
			<!-- 故障统计 -->
			<view class="fault-stats">
				<view class="stat-item">
					<view class="stat-icon pending">
						<cl-icon name="cl-icon-clock" size="20" color="#fa8c16" />
					</view>
					<view class="stat-info">
						<text class="stat-number">{{ pendingFaults }}</text>
						<text class="stat-label">待处理</text>
						<text class="stat-trend">{{ pendingTrend > 0 ? '+' : '' }}{{ pendingTrend }}%</text>
					</view>
				</view>

				<view class="stat-item">
					<view class="stat-icon processing">
						<cl-icon name="cl-icon-tool" size="20" color="#1890ff" />
					</view>
					<view class="stat-info">
						<text class="stat-number">{{ processingFaults }}</text>
						<text class="stat-label">处理中</text>
						<text class="stat-trend">{{ processingTrend > 0 ? '+' : '' }}{{ processingTrend }}%</text>
					</view>
				</view>

				<view class="stat-item">
					<view class="stat-icon resolved">
						<cl-icon name="cl-icon-check" size="20" color="#52c41a" />
					</view>
					<view class="stat-info">
						<text class="stat-number">{{ resolvedFaults }}</text>
						<text class="stat-label">已解决</text>
						<text class="stat-trend">{{ resolvedTrend > 0 ? '+' : '' }}{{ resolvedTrend }}%</text>
					</view>
				</view>

				<view class="stat-item">
					<view class="stat-icon critical">
						<cl-icon name="cl-icon-alert-triangle" size="20" color="#ff4d4f" />
					</view>
					<view class="stat-info">
						<text class="stat-number">{{ criticalFaults }}</text>
						<text class="stat-label">紧急故障</text>
						<text class="stat-trend urgent">需立即处理</text>
					</view>
				</view>
			</view>

			<!-- 筛选和搜索 -->
			<view class="filter-section">
				<view class="search-bar">
					<cl-input
						v-model="searchKeyword"
						placeholder="搜索设备名称或故障描述"
						clearable
						@input="handleSearch"
					>
						<template #prefix>
							<cl-icon name="cl-icon-search" size="16" color="#ccc" />
						</template>
					</cl-input>
				</view>

				<view class="filter-tabs">
					<view
						v-for="tab in statusTabs"
						:key="tab.value"
						class="tab-item"
						:class="{ active: activeStatus === tab.value }"
						@click="switchStatus(tab.value)"
					>
						{{ tab.label }}
						<text v-if="tab.count > 0" class="tab-badge">{{ tab.count }}</text>
					</view>
				</view>

				<view class="filter-controls">
					<cl-select
						v-model="selectedSeverity"
						placeholder="严重程度"
						@change="handleFilter"
						class="filter-select"
					>
						<cl-option value="" label="全部" />
						<cl-option value="critical" label="严重" />
						<cl-option value="high" label="高" />
						<cl-option value="medium" label="中" />
						<cl-option value="low" label="低" />
					</cl-select>

					<cl-select
						v-model="selectedType"
						placeholder="故障类型"
						@change="handleFilter"
						class="filter-select"
					>
						<cl-option value="" label="全部" />
						<cl-option value="hardware" label="硬件故障" />
						<cl-option value="software" label="软件故障" />
						<cl-option value="network" label="网络故障" />
						<cl-option value="power" label="电源故障" />
					</cl-select>
				</view>
			</view>

			<!-- 故障列表 -->
			<view class="fault-list">
				<view
					v-for="fault in filteredFaults"
					:key="fault.id"
					class="fault-item"
					:class="{ selected: selectedFaults.includes(fault.id) }"
					@click="viewFaultDetail(fault)"
				>
					<view class="fault-header">
						<view class="fault-select" v-if="showBatchActions" @click.stop>
							<cl-checkbox
								v-model="selectedFaults"
								:value="fault.id"
								@change="handleFaultSelect"
							/>
						</view>

						<view class="fault-title">
							<text class="device-name">{{ fault.deviceName }}</text>
							<view class="severity-badge" :class="fault.severity">
								<text class="severity-text">{{
									getSeverityText(fault.severity)
								}}</text>
							</view>
						</view>

						<view class="fault-status">
							<status-indicator
								:status="fault.status"
								:text="getStatusText(fault.status)"
							/>
						</view>
					</view>

					<view class="fault-content">
						<text class="fault-desc">{{ fault.description }}</text>
						<view class="fault-meta">
							<text class="fault-type">{{ getTypeText(fault.type) }}</text>
							<text class="fault-location">{{ fault.location }}</text>
							<text class="fault-time">{{ formatTime(fault.reportTime) }}</text>
						</view>
					</view>

					<view v-if="fault.status === 'pending'" class="fault-actions">
						<cl-button 
							size="small" 
							:type="fault.severity === 'critical' ? 'danger' : 'primary'" 
							@click.stop="handleFault(fault)"
						>
							{{ fault.severity === 'critical' ? '紧急处理' : '立即处理' }}
						</cl-button>
						<cl-button size="small" type="default" @click.stop="assignFault(fault)">
							分配
						</cl-button>
						<cl-button size="small" type="text" @click.stop="viewFaultDetail(fault)">
							详情
						</cl-button>
					</view>

					<view v-else-if="fault.status === 'processing'" class="fault-actions">
						<cl-button size="small" type="success" @click.stop="resolveFault(fault)">
							标记解决
						</cl-button>
						<cl-button size="small" type="default" @click.stop="updateProgress(fault)">
							更新进度
						</cl-button>
						<cl-button size="small" type="default" @click.stop="viewMaintenance(fault)">
							维护记录
						</cl-button>
					</view>

					<view v-else-if="fault.status === 'resolved'" class="fault-actions">
						<cl-button size="small" type="default" @click.stop="viewMaintenance(fault)">
							维护记录
						</cl-button>
						<cl-button
							size="small"
							type="primary"
							plain
							@click.stop="reopenFault(fault)"
						>
							重新打开
						</cl-button>
					</view>
				</view>

				<!-- 加载更多 -->
				<view v-if="hasMore" class="load-more">
					<cl-button type="default" plain @click="loadMore" :loading="loading">
						{{ loading ? "加载中..." : "加载更多" }}
					</cl-button>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-if="filteredFaults.length === 0 && !loading" class="empty-state">
				<cl-icon name="cl-icon-inbox" size="60" color="#ccc" />
				<text class="empty-text">暂无故障记录</text>
				<text class="empty-desc">系统运行正常，没有发现故障</text>
			</view>
		</view>

		<!-- 故障处理弹窗 -->
		<cl-popup v-model="showHandleModal" direction="bottom">
			<view class="handle-modal">
				<view class="modal-header">
					<text class="modal-title">处理故障</text>
					<cl-button text @click="showHandleModal = false">取消</cl-button>
				</view>

				<view class="modal-content">
					<view class="form-item">
						<text class="form-label">处理方案</text>
						<cl-textarea
							v-model="handleForm.solution"
							placeholder="请输入处理方案"
							maxlength="500"
							show-word-limit
						/>
					</view>

					<view class="form-item">
						<text class="form-label">预计完成时间</text>
						<cl-date-picker
							v-model="handleForm.estimatedTime"
							type="datetime"
							placeholder="选择预计完成时间"
						/>
					</view>

					<view class="form-item">
						<text class="form-label">处理人员</text>
						<cl-select v-model="handleForm.assignee" placeholder="选择处理人员">
							<cl-option
								v-for="user in technicians"
								:key="user.id"
								:value="user.id"
								:label="user.name"
							/>
						</cl-select>
					</view>
				</view>

				<view class="modal-footer">
					<cl-button type="primary" @click="confirmHandle" :loading="submitting">
						确认处理
					</cl-button>
				</view>
			</view>
		</cl-popup>

		<!-- 分配故障弹窗 -->
		<cl-popup v-model="showAssignModal" direction="bottom">
			<view class="assign-modal">
				<view class="modal-header">
					<text class="modal-title">分配故障</text>
					<cl-button text @click="showAssignModal = false">取消</cl-button>
				</view>

				<view class="modal-content">
					<view class="technician-list">
						<view
							v-for="tech in technicians"
							:key="tech.id"
							class="technician-item"
							:class="{ selected: selectedTechnician === tech.id }"
							@click="selectTechnician(tech.id)"
						>
							<view class="tech-avatar">
								<cl-avatar :src="tech.avatar" :name="tech.name" size="40" />
							</view>
							<view class="tech-info">
								<text class="tech-name">{{ tech.name }}</text>
								<text class="tech-role">{{ tech.role }}</text>
								<text class="tech-status">{{
									tech.status === "available" ? "空闲" : "忙碌"
								}}</text>
							</view>
							<view class="tech-stats">
								<text class="stat-text">处理中: {{ tech.processingCount }}</text>
								<text class="stat-text">完成率: {{ tech.completionRate }}%</text>
							</view>
						</view>
					</view>
				</view>

				<view class="modal-footer">
					<cl-button
						type="primary"
						@click="confirmAssign"
						:disabled="!selectedTechnician"
						:loading="submitting"
					>
						确认分配
					</cl-button>
				</view>
			</view>
		</cl-popup>

		<!-- 进度更新弹窗 -->
		<cl-popup v-model="showProgressModal" direction="bottom">
			<view class="progress-modal">
				<view class="modal-header">
					<text class="modal-title">更新处理进度</text>
					<cl-button text @click="showProgressModal = false">取消</cl-button>
				</view>

				<view class="modal-content">
					<view class="progress-steps">
						<view
							v-for="(step, index) in progressSteps"
							:key="index"
							class="step-item"
							:class="{
								active: index <= currentStep,
								completed: index < currentStep
							}"
							@click="setCurrentStep(index)"
						>
							<view class="step-icon">
								<cl-icon
									:name="index < currentStep ? 'cl-icon-check' : 'cl-icon-clock'"
									size="16"
								/>
							</view>
							<text class="step-text">{{ step.title }}</text>
						</view>
					</view>

					<view class="form-item">
						<text class="form-label">进度说明</text>
						<cl-textarea
							v-model="progressForm.description"
							placeholder="请输入当前进度说明"
							maxlength="200"
							show-word-limit
						/>
					</view>

					<view class="form-item">
						<text class="form-label">预计完成时间</text>
						<cl-date-picker
							v-model="progressForm.estimatedTime"
							type="datetime"
							placeholder="选择预计完成时间"
						/>
					</view>
				</view>

				<view class="modal-footer">
					<cl-button type="primary" @click="confirmProgress" :loading="submitting">
						更新进度
					</cl-button>
				</view>
			</view>
		</cl-popup>

		<!-- 维护记录弹窗 -->
		<cl-popup v-model="showMaintenanceModal" direction="bottom">
			<view class="maintenance-modal">
				<view class="modal-header">
					<text class="modal-title">维护记录</text>
					<cl-button text @click="showMaintenanceModal = false">关闭</cl-button>
				</view>

				<view class="modal-content">
					<view class="maintenance-list">
						<view
							v-for="record in maintenanceRecords"
							:key="record.id"
							class="maintenance-item"
						>
							<view class="record-header">
								<text class="record-type">{{ record.type }}</text>
								<text class="record-time">{{ formatTime(record.time) }}</text>
							</view>
							<text class="record-desc">{{ record.description }}</text>
							<text class="record-operator">操作人：{{ record.operator }}</text>
						</view>
					</view>

					<view class="add-record">
						<cl-button type="primary" size="small" @click="addMaintenanceRecord">
							添加记录
						</cl-button>
					</view>
				</view>
			</view>
		</cl-popup>

		<!-- 添加维护记录弹窗 -->
		<cl-popup v-model="showAddRecordModal" direction="bottom">
			<view class="add-record-modal">
				<view class="modal-header">
					<text class="modal-title">添加维护记录</text>
					<cl-button text @click="showAddRecordModal = false">取消</cl-button>
				</view>

				<view class="modal-content">
					<view class="form-item">
						<text class="form-label">维护类型</text>
						<cl-select v-model="recordForm.type" placeholder="选择维护类型">
							<cl-option value="inspection" label="巡检" />
							<cl-option value="repair" label="维修" />
							<cl-option value="replacement" label="更换" />
							<cl-option value="cleaning" label="清洁" />
							<cl-option value="calibration" label="校准" />
						</cl-select>
					</view>

					<view class="form-item">
						<text class="form-label">维护描述</text>
						<cl-textarea
							v-model="recordForm.description"
							placeholder="请输入维护详细描述"
							maxlength="300"
							show-word-limit
						/>
					</view>

					<view class="form-item">
						<text class="form-label">维护时间</text>
						<cl-date-picker
							v-model="recordForm.time"
							type="datetime"
							placeholder="选择维护时间"
						/>
					</view>
				</view>

				<view class="modal-footer">
					<cl-button type="primary" @click="confirmAddRecord" :loading="submitting">
						添加记录
					</cl-button>
				</view>
			</view>
		</cl-popup>

		<!-- 故障上报弹窗 -->
		<cl-popup v-model="showReportModal" direction="bottom">
			<view class="report-modal">
				<view class="modal-header">
					<text class="modal-title">故障上报</text>
					<cl-button text @click="showReportModal = false">取消</cl-button>
				</view>

				<view class="modal-content">
					<view class="form-item">
						<text class="form-label">故障设备</text>
						<cl-select v-model="reportForm.deviceId" placeholder="选择故障设备">
							<cl-option
								v-for="device in deviceList"
								:key="device.id"
								:value="device.id"
								:label="`${device.name} - ${device.location}`"
							/>
						</cl-select>
					</view>

					<view class="form-item">
						<text class="form-label">故障类型</text>
						<cl-select v-model="reportForm.type" placeholder="选择故障类型">
							<cl-option value="hardware" label="硬件故障" />
							<cl-option value="software" label="软件故障" />
							<cl-option value="network" label="网络故障" />
							<cl-option value="power" label="电源故障" />
						</cl-select>
					</view>

					<view class="form-item">
						<text class="form-label">严重程度</text>
						<cl-select v-model="reportForm.severity" placeholder="选择严重程度">
							<cl-option value="critical" label="严重" />
							<cl-option value="high" label="高" />
							<cl-option value="medium" label="中" />
							<cl-option value="low" label="低" />
						</cl-select>
					</view>

					<view class="form-item">
						<text class="form-label">故障描述</text>
						<cl-textarea
							v-model="reportForm.description"
							placeholder="请详细描述故障现象和影响"
							maxlength="500"
							show-word-limit
						/>
					</view>

					<view class="form-item">
						<text class="form-label">故障图片</text>
						<view class="image-upload">
							<view
								v-for="(image, index) in reportForm.images"
								:key="index"
								class="image-item"
							>
								<image :src="image" class="uploaded-image" />
								<view class="image-remove" @click="removeImage(index)">
									<cl-icon name="cl-icon-x" size="12" color="white" />
								</view>
							</view>
							<view v-if="reportForm.images.length < 3" class="image-add" @click="uploadImage">
								<cl-icon name="cl-icon-plus" size="24" color="#ccc" />
								<text class="add-text">添加图片</text>
							</view>
						</view>
					</view>
				</view>

				<view class="modal-footer">
					<cl-button type="primary" @click="confirmReport" :loading="submitting">
						提交上报
					</cl-button>
				</view>
			</view>
		</cl-popup>

		<!-- 维护计划弹窗 -->
		<cl-popup v-model="showMaintenancePlan" direction="bottom">
			<view class="maintenance-plan-modal">
				<view class="modal-header">
					<text class="modal-title">维护计划</text>
					<cl-button text @click="showMaintenancePlan = false">关闭</cl-button>
				</view>

				<view class="modal-content">
					<view class="plan-calendar">
						<view class="calendar-header">
							<cl-button text @click="prevMonth">
								<cl-icon name="cl-icon-chevron-left" size="16" />
							</cl-button>
							<text class="current-month">{{ currentMonth }}</text>
							<cl-button text @click="nextMonth">
								<cl-icon name="cl-icon-chevron-right" size="16" />
							</cl-button>
						</view>

						<view class="calendar-grid">
							<view class="weekdays">
								<text class="weekday">日</text>
								<text class="weekday">一</text>
								<text class="weekday">二</text>
								<text class="weekday">三</text>
								<text class="weekday">四</text>
								<text class="weekday">五</text>
								<text class="weekday">六</text>
							</view>

							<view class="calendar-days">
								<view
									v-for="day in calendarDays"
									:key="day.date"
									class="calendar-day"
									:class="{
										today: day.isToday,
										'has-plan': day.hasPlan,
										'other-month': day.isOtherMonth
									}"
									@click="selectDay(day)"
								>
									<text class="day-number">{{ day.day }}</text>
									<view v-if="day.hasPlan" class="plan-indicator"></view>
								</view>
							</view>
						</view>
					</view>

					<view class="plan-list">
						<view class="list-header">
							<text class="list-title">本月维护计划</text>
							<cl-button size="small" type="primary" @click="addMaintenancePlan">
								新增计划
							</cl-button>
						</view>

						<view class="plan-items">
							<view
								v-for="plan in maintenancePlans"
								:key="plan.id"
								class="plan-item"
							>
								<view class="plan-date">
									<text class="date-text">{{ formatDate(plan.date) }}</text>
									<text class="time-text">{{ plan.time }}</text>
								</view>
								<view class="plan-info">
									<text class="plan-title">{{ plan.title }}</text>
									<text class="plan-desc">{{ plan.description }}</text>
									<text class="plan-assignee">负责人：{{ plan.assignee }}</text>
								</view>
								<view class="plan-status">
									<text
										class="status-badge"
										:class="plan.status"
									>
										{{ getMaintenanceStatusText(plan.status) }}
									</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</cl-popup>

		<!-- 统计分析弹窗 -->
		<cl-popup v-model="showStatistics" direction="bottom">
			<view class="statistics-modal">
				<view class="modal-header">
					<text class="modal-title">故障统计分析</text>
					<cl-button text @click="showStatistics = false">关闭</cl-button>
				</view>

				<view class="modal-content">
					<view class="stats-tabs">
						<view
							v-for="tab in statsTabs"
							:key="tab.value"
							class="stats-tab"
							:class="{ active: activeStatsTab === tab.value }"
							@click="activeStatsTab = tab.value"
						>
							<text>{{ tab.label }}</text>
						</view>
					</view>

					<view v-if="activeStatsTab === 'overview'" class="stats-overview">
						<view class="kpi-cards">
							<view class="kpi-card">
								<text class="kpi-value">{{ mttr }}</text>
								<text class="kpi-label">平均修复时间(小时)</text>
							</view>
							<view class="kpi-card">
								<text class="kpi-value">{{ mtbf }}</text>
								<text class="kpi-label">平均故障间隔(天)</text>
							</view>
							<view class="kpi-card">
								<text class="kpi-value">{{ faultRate }}%</text>
								<text class="kpi-label">故障率</text>
							</view>
							<view class="kpi-card">
								<text class="kpi-value">{{ resolutionRate }}%</text>
								<text class="kpi-label">解决率</text>
							</view>
						</view>
					</view>

					<view v-if="activeStatsTab === 'trend'" class="stats-trend">
						<text class="section-title">故障趋势分析</text>
						<!-- 这里可以集成图表组件 -->
						<view class="chart-placeholder">
							<cl-icon name="cl-icon-bar-chart" size="60" color="#ccc" />
							<text class="placeholder-text">故障趋势图表</text>
						</view>
					</view>

					<view v-if="activeStatsTab === 'type'" class="stats-type">
						<text class="section-title">故障类型分布</text>
						<view class="type-stats">
							<view
								v-for="type in faultTypeStats"
								:key="type.type"
								class="type-item"
							>
								<view class="type-info">
									<text class="type-name">{{ getTypeText(type.type) }}</text>
									<text class="type-count">{{ type.count }}次</text>
								</view>
								<view class="type-bar">
									<view
										class="bar-fill"
										:style="{ width: type.percentage + '%' }"
									></view>
								</view>
								<text class="type-percentage">{{ type.percentage }}%</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</cl-popup>
	</cl-page>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import { router, debounce } from "@/cool";
import { energyDeviceStore } from "@/stores/energy-device";
import StatusIndicator from "@/components/business/status-indicator.uvue";

// 响应式数据
const searchKeyword = ref("");
const activeStatus = ref("all");
const selectedSeverity = ref("");
const selectedType = ref("");
const loading = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const showHandleModal = ref(false);
const showAssignModal = ref(false);
const showProgressModal = ref(false);
const showMaintenanceModal = ref(false);
const showAddRecordModal = ref(false);
const showReportModal = ref(false);
const showMaintenancePlan = ref(false);
const showStatistics = ref(false);
const showBatchActions = ref(false);
const submitting = ref(false);
const selectedTechnician = ref<number | null>(null);
const currentFault = ref<any>(null);
const selectedFaults = ref<number[]>([]);
const selectAll = ref(false);
const currentStep = ref(0);

// 故障列表
const faultList = ref<any[]>([]);

// 处理表单
const handleForm = reactive({
	solution: "",
	estimatedTime: "",
	assignee: null
});

// 进度表单
const progressForm = reactive({
	description: "",
	estimatedTime: ""
});

// 维护记录表单
const recordForm = reactive({
	type: "",
	description: "",
	time: ""
});

// 故障上报表单
const reportForm = reactive({
	deviceId: "",
	type: "",
	severity: "",
	description: "",
	images: []
});

// 设备列表
const deviceList = ref([
	{ id: "1", name: "LED灯-001", location: "办公区A" },
	{ id: "2", name: "LED灯-002", location: "办公区B" },
	{ id: "3", name: "智能开关-001", location: "会议室1" },
	{ id: "4", name: "传感器-001", location: "走廊" }
]);

// 维护计划相关
const currentMonth = ref("2024年1月");
const calendarDays = ref([]);
const maintenancePlans = ref([
	{
		id: "1",
		date: "2024-01-15",
		time: "09:00",
		title: "定期巡检",
		description: "办公区LED灯具巡检",
		assignee: "张三",
		status: "pending"
	},
	{
		id: "2",
		date: "2024-01-20",
		time: "14:00",
		title: "设备清洁",
		description: "会议室灯具清洁维护",
		assignee: "李四",
		status: "in_progress"
	}
]);

// 统计分析相关
const activeStatsTab = ref("overview");
const statsTabs = ref([
	{ value: "overview", label: "概览" },
	{ value: "trend", label: "趋势" },
	{ value: "type", label: "类型分布" }
]);
const mttr = ref(4.2);
const mtbf = ref(45);
const faultRate = ref(2.3);
const resolutionRate = ref(95.8);
const faultTypeStats = ref([
	{ type: "hardware", count: 15, percentage: 45 },
	{ type: "software", count: 8, percentage: 24 },
	{ type: "network", count: 6, percentage: 18 },
	{ type: "power", count: 4, percentage: 13 }
]);

// 进度步骤
const progressSteps = ref([
	{ title: "问题确认", description: "确认故障问题" },
	{ title: "方案制定", description: "制定解决方案" },
	{ title: "实施处理", description: "执行处理方案" },
	{ title: "测试验证", description: "验证处理效果" },
	{ title: "完成解决", description: "故障完全解决" }
]);

// 维护记录
const maintenanceRecords = ref([
	{
		id: 1,
		type: "巡检",
		description: "定期设备巡检，发现亮度异常",
		time: "2024-01-15T08:00:00Z",
		operator: "张工程师"
	},
	{
		id: 2,
		type: "维修",
		description: "更换LED驱动模块",
		time: "2024-01-15T10:30:00Z",
		operator: "李技师"
	}
]);

// 状态选项卡
const statusTabs = computed(() => [
	{ label: "全部", value: "all", count: faultList.value.length },
	{ label: "待处理", value: "pending", count: pendingFaults.value },
	{ label: "处理中", value: "processing", count: processingFaults.value },
	{ label: "已解决", value: "resolved", count: resolvedFaults.value }
]);

// 统计数据
const pendingFaults = computed(() => {
	return faultList.value.filter((fault) => fault.status === "pending").length;
});

const processingFaults = computed(() => {
	return faultList.value.filter((fault) => fault.status === "processing").length;
});

const resolvedFaults = computed(() => {
	return faultList.value.filter((fault) => fault.status === "resolved").length;
});

// 过滤后的故障列表
const filteredFaults = computed(() => {
	let filtered = faultList.value;

	// 状态筛选
	if (activeStatus.value !== "all") {
		filtered = filtered.filter((fault) => fault.status === activeStatus.value);
	}

	// 严重程度筛选
	if (selectedSeverity.value) {
		filtered = filtered.filter((fault) => fault.severity === selectedSeverity.value);
	}

	// 类型筛选
	if (selectedType.value) {
		filtered = filtered.filter((fault) => fault.type === selectedType.value);
	}

	// 关键词搜索
	if (searchKeyword.value) {
		const keyword = searchKeyword.value.toLowerCase();
		filtered = filtered.filter(
			(fault) =>
				fault.deviceName.toLowerCase().includes(keyword) ||
				fault.description.toLowerCase().includes(keyword)
		);
	}

	return filtered;
});

// 技术人员列表
const technicians = ref([
	{
		id: 1,
		name: "张工程师",
		role: "高级技术员",
		avatar: "",
		status: "available",
		processingCount: 2,
		completionRate: 95
	},
	{
		id: 2,
		name: "李技师",
		role: "维修技师",
		avatar: "",
		status: "busy",
		processingCount: 5,
		completionRate: 88
	},
	{
		id: 3,
		name: "王师傅",
		role: "电工",
		avatar: "",
		status: "available",
		processingCount: 1,
		completionRate: 92
	}
]);

// 搜索处理
const handleSearch = debounce(() => {
	// 搜索逻辑已在computed中处理
}, 300);

// 切换状态
const switchStatus = (status: string) => {
	activeStatus.value = status;
};

// 筛选处理
const handleFilter = () => {
	// 筛选逻辑已在computed中处理
};

// 获取严重程度文本
const getSeverityText = (severity: string) => {
	const map: Record<string, string> = {
		critical: "严重",
		high: "高",
		medium: "中",
		low: "低"
	};
	return map[severity] || severity;
};

// 获取状态文本
const getStatusText = (status: string) => {
	const map: Record<string, string> = {
		pending: "待处理",
		processing: "处理中",
		resolved: "已解决"
	};
	return map[status] || status;
};

// 获取类型文本
const getTypeText = (type: string) => {
	const map: Record<string, string> = {
		hardware: "硬件故障",
		software: "软件故障",
		network: "网络故障",
		power: "电源故障"
	};
	return map[type] || type;
};

// 格式化时间
const formatTime = (time: string) => {
	const date = new Date(time);
	const now = new Date();
	const diff = now.getTime() - date.getTime();
	const hours = Math.floor(diff / (1000 * 60 * 60));

	if (hours < 1) {
		const minutes = Math.floor(diff / (1000 * 60));
		return `${minutes}分钟前`;
	} else if (hours < 24) {
		return `${hours}小时前`;
	} else {
		const days = Math.floor(hours / 24);
		return `${days}天前`;
	}
};

// 查看故障详情
const viewFaultDetail = (fault: any) => {
	// 显示故障详情弹窗
	uni.showModal({
		title: `故障详情 - ${fault.title}`,
		content: `设备：${fault.deviceName}\n位置：${fault.location}\n故障类型：${fault.type}\n故障描述：${fault.description}\n报告时间：${fault.reportTime}\n状态：${fault.status}`,
		showCancel: false,
		confirmText: "关闭"
	});
};

// 处理故障
const handleFault = (fault: any) => {
	currentFault.value = fault;
	handleForm.solution = "";
	handleForm.estimatedTime = "";
	handleForm.assignee = null;
	showHandleModal.value = true;
};

// 分配故障
const assignFault = (fault: any) => {
	currentFault.value = fault;
	selectedTechnician.value = null;
	showAssignModal.value = true;
};

// 解决故障
const resolveFault = async (fault: any) => {
	try {
		uni.showModal({
			title: "确认解决",
			content: "确认将此故障标记为已解决？",
			success: async (res) => {
				if (res.confirm) {
					// 调用API标记为已解决
					fault.status = "resolved";
					fault.resolveTime = new Date().toISOString();

					uni.showToast({
						title: "故障已解决",
						icon: "success"
					});
				}
			}
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
	}
};

// 更新进度
const updateProgress = (fault: any) => {
	currentFault.value = fault;
	currentStep.value = fault.progressStep || 0;
	progressForm.description = "";
	progressForm.estimatedTime = "";
	showProgressModal.value = true;
};

// 查看维护记录
const viewMaintenance = (fault: any) => {
	currentFault.value = fault;
	showMaintenanceModal.value = true;
};

// 重新打开故障
const reopenFault = async (fault: any) => {
	try {
		uni.showModal({
			title: "重新打开",
			content: "确认重新打开此故障？",
			success: async (res) => {
				if (res.confirm) {
					fault.status = "pending";
					fault.reopenTime = new Date().toISOString();

					uni.showToast({
						title: "故障已重新打开",
						icon: "success"
					});
				}
			}
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
	}
};

// 选择技术人员
const selectTechnician = (techId: number) => {
	selectedTechnician.value = techId;
};

// 确认处理
const confirmHandle = async () => {
	if (!handleForm.solution.trim()) {
		uni.showToast({
			title: "请输入处理方案",
			icon: "none"
		});
		return;
	}

	submitting.value = true;
	try {
		// 调用API开始处理故障
		currentFault.value.status = "processing";
		currentFault.value.solution = handleForm.solution;
		currentFault.value.estimatedTime = handleForm.estimatedTime;
		currentFault.value.assignee = handleForm.assignee;
		currentFault.value.startTime = new Date().toISOString();

		showHandleModal.value = false;
		uni.showToast({
			title: "开始处理故障",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
	} finally {
		submitting.value = false;
	}
};

// 确认分配
const confirmAssign = async () => {
	if (!selectedTechnician.value) {
		uni.showToast({
			title: "请选择技术人员",
			icon: "none"
		});
		return;
	}

	submitting.value = true;
	try {
		// 调用API分配故障
		const technician = technicians.value.find((t) => t.id === selectedTechnician.value);
		currentFault.value.assignee = selectedTechnician.value;
		currentFault.value.assigneeName = technician?.name;
		currentFault.value.assignTime = new Date().toISOString();

		showAssignModal.value = false;
		uni.showToast({
			title: "分配成功",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "分配失败",
			icon: "none"
		});
	} finally {
		submitting.value = false;
	}
};

// 批量操作相关方法
const toggleBatchActions = () => {
	showBatchActions.value = !showBatchActions.value;
	if (!showBatchActions.value) {
		selectedFaults.value = [];
		selectAll.value = false;
	}
};

const toggleSelectAll = () => {
	selectAll.value = !selectAll.value;
	if (selectAll.value) {
		selectedFaults.value = filteredFaultList.value.map((fault) => fault.id);
	} else {
		selectedFaults.value = [];
	}
};

const toggleFaultSelection = (faultId: number) => {
	const index = selectedFaults.value.indexOf(faultId);
	if (index > -1) {
		selectedFaults.value.splice(index, 1);
	} else {
		selectedFaults.value.push(faultId);
	}

	// 更新全选状态
	selectAll.value = selectedFaults.value.length === filteredFaultList.value.length;
};

const batchAssign = () => {
	if (selectedFaults.value.length === 0) {
		uni.showToast({
			title: "请选择要分配的故障",
			icon: "none"
		});
		return;
	}

	showAssignModal.value = true;
};

const batchResolve = async () => {
	if (selectedFaults.value.length === 0) {
		uni.showToast({
			title: "请选择要解决的故障",
			icon: "none"
		});
		return;
	}

	uni.showModal({
		title: "批量解决",
		content: `确认将选中的 ${selectedFaults.value.length} 个故障标记为已解决？`,
		success: async (res) => {
			if (res.confirm) {
				try {
					// 批量更新故障状态
					selectedFaults.value.forEach((faultId) => {
						const fault = faultList.value.find((f) => f.id === faultId);
						if (fault) {
							fault.status = "resolved";
							fault.resolvedTime = new Date().toISOString();
						}
					});

					selectedFaults.value = [];
					selectAll.value = false;
					showBatchActions.value = false;

					uni.showToast({
						title: "批量解决成功",
						icon: "success"
					});
				} catch (error: any) {
					uni.showToast({
						title: error.message || "批量操作失败",
						icon: "none"
					});
				}
			}
		}
	});
};

const exportFaults = () => {
	if (selectedFaults.value.length === 0) {
		uni.showToast({
			title: "请选择要导出的故障",
			icon: "none"
		});
		return;
	}

	// 模拟导出功能
	uni.showToast({
		title: `正在导出 ${selectedFaults.value.length} 条记录`,
		icon: "success"
	});
};

// 设置当前步骤
const setCurrentStep = (step: number) => {
	currentStep.value = step;
};

// 确认进度更新
const confirmProgress = async () => {
	if (!progressForm.description.trim()) {
		uni.showToast({
			title: "请输入进度说明",
			icon: "none"
		});
		return;
	}

	submitting.value = true;
	try {
		// 更新故障进度
		currentFault.value.progressStep = currentStep.value;
		currentFault.value.progressDescription = progressForm.description;
		currentFault.value.estimatedTime = progressForm.estimatedTime;
		currentFault.value.updateTime = new Date().toISOString();

		showProgressModal.value = false;
		uni.showToast({
			title: "进度更新成功",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "更新失败",
			icon: "none"
		});
	} finally {
		submitting.value = false;
	}
};

// 添加维护记录
const addMaintenanceRecord = () => {
	recordForm.type = "";
	recordForm.description = "";
	recordForm.time = "";
	showAddRecordModal.value = true;
};

// 确认添加记录
const confirmAddRecord = async () => {
	if (!recordForm.type || !recordForm.description.trim()) {
		uni.showToast({
			title: "请填写完整信息",
			icon: "none"
		});
		return;
	}

	submitting.value = true;
	try {
		// 添加维护记录
		const newRecord = {
			id: Date.now(),
			type: recordForm.type,
			description: recordForm.description,
			time: recordForm.time || new Date().toISOString(),
			operator: "当前用户" // 实际应该从用户信息获取
		};

		maintenanceRecords.value.unshift(newRecord);

		showAddRecordModal.value = false;
		uni.showToast({
			title: "记录添加成功",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "添加失败",
			icon: "none"
		});
	} finally {
		submitting.value = false;
	}
};

// 加载更多
const loadMore = async () => {
	if (loading.value || !hasMore.value) return;

	loading.value = true;
	try {
		currentPage.value++;
		// 调用API加载更多数据
		// const newFaults = await faultService.getFaultList(currentPage.value);
		// faultList.value.push(...newFaults);
		// hasMore.value = newFaults.length === 20;
	} catch (error: any) {
		uni.showToast({
			title: error.message || "加载失败",
			icon: "none"
		});
	} finally {
		loading.value = false;
	}
};

// 故障上报
const showReportFault = () => {
	showReportModal.value = true;
};

// 确认故障上报
const confirmReport = async () => {
	if (!reportForm.deviceId || !reportForm.type || !reportForm.severity || !reportForm.description) {
		uni.showToast({
			title: "请填写完整信息",
			icon: "none"
		});
		return;
	}

	submitting.value = true;
	try {
		// 模拟API调用
		await new Promise(resolve => setTimeout(resolve, 1500));
		
		uni.showToast({
			title: "上报成功",
			icon: "success"
		});
		
		// 重置表单
		reportForm.deviceId = "";
		reportForm.type = "";
		reportForm.severity = "";
		reportForm.description = "";
		reportForm.images = [];
		
		showReportModal.value = false;
		
		// 刷新故障列表
		loadFaultList();
	} catch (error: any) {
		uni.showToast({
			title: error.message || "上报失败",
			icon: "none"
		});
	} finally {
		submitting.value = false;
	}
};

// 上传图片
const uploadImage = () => {
	uni.chooseImage({
		count: 3 - reportForm.images.length,
		sizeType: ['compressed'],
		sourceType: ['camera', 'album'],
		success: (res) => {
			reportForm.images.push(...res.tempFilePaths);
		}
	});
};

// 移除图片
const removeImage = (index: number) => {
	reportForm.images.splice(index, 1);
};

// 显示维护计划
const showMaintenancePlanModal = () => {
	generateCalendarDays();
	showMaintenancePlan.value = true;
};

// 生成日历数据
const generateCalendarDays = () => {
	const today = new Date();
	const year = today.getFullYear();
	const month = today.getMonth();
	
	// 获取当月第一天和最后一天
	const firstDay = new Date(year, month, 1);
	const lastDay = new Date(year, month + 1, 0);
	
	// 获取第一天是星期几
	const startWeekday = firstDay.getDay();
	
	const days: any[] = [];
	
	// 添加上个月的日期
	for (let i = startWeekday - 1; i >= 0; i--) {
		const date = new Date(year, month, -i);
		days.push({
			date: date.toISOString().split('T')[0],
			day: date.getDate(),
			isToday: false,
			hasPlan: false,
			isOtherMonth: true
		});
	}
	
	// 添加当月的日期
	for (let i = 1; i <= lastDay.getDate(); i++) {
		const date = new Date(year, month, i);
		const dateStr = date.toISOString().split('T')[0];
		const isToday = dateStr === today.toISOString().split('T')[0];
		const hasPlan = maintenancePlans.value.some(plan => plan.date === dateStr);
		
		days.push({
			date: dateStr,
			day: i,
			isToday,
			hasPlan,
			isOtherMonth: false
		});
	}
	
	// 补充下个月的日期，确保总共42天（6周）
	const remainingDays = 42 - days.length;
	for (let i = 1; i <= remainingDays; i++) {
		const date = new Date(year, month + 1, i);
		days.push({
			date: date.toISOString().split('T')[0],
			day: i,
			isToday: false,
			hasPlan: false,
			isOtherMonth: true
		});
	}
	
	calendarDays.value = days;
};

// 上一个月
const prevMonth = () => {
	// 实现月份切换逻辑
	console.log('上一个月');
};

// 下一个月
const nextMonth = () => {
	// 实现月份切换逻辑
	console.log('下一个月');
};

// 选择日期
const selectDay = (day: any) => {
	if (day.hasPlan) {
		// 显示当天的维护计划
		console.log('查看维护计划', day.date);
	}
};

// 添加维护计划
const addMaintenancePlan = () => {
	// 跳转到添加维护计划页面或显示添加弹窗
	console.log('添加维护计划');
};

// 获取维护状态文本
const getMaintenanceStatusText = (status: string) => {
	const statusMap: Record<string, string> = {
		pending: '待执行',
		in_progress: '执行中',
		completed: '已完成',
		overdue: '已逾期'
	};
	return statusMap[status] || status;
};

// 显示统计分析
const showStatisticsModal = () => {
	showStatistics.value = true;
};

// 加载故障列表
const loadFaultList = async () => {
	loading.value = true;
	try {
		// 模拟数据
		faultList.value = [
			{
				id: 1,
				deviceId: 1,
				deviceName: "LED灯具-001",
				location: "办公区A-101",
				type: "hardware",
				severity: "high",
				status: "pending",
				description: "灯具无法正常开启，疑似电路板故障",
				reportTime: "2024-01-15T10:30:00Z",
				reporter: "系统自动检测"
			},
			{
				id: 2,
				deviceId: 2,
				deviceName: "智能开关-002",
				location: "会议室B-201",
				type: "network",
				severity: "medium",
				status: "processing",
				description: "设备离线，无法远程控制",
				reportTime: "2024-01-15T09:15:00Z",
				reporter: "张三",
				assignee: 1,
				assigneeName: "张工程师",
				startTime: "2024-01-15T09:30:00Z"
			},
			{
				id: 3,
				deviceId: 3,
				deviceName: "调光器-003",
				location: "走廊C-301",
				type: "software",
				severity: "low",
				status: "resolved",
				description: "亮度调节异常，已通过固件更新解决",
				reportTime: "2024-01-14T16:20:00Z",
				reporter: "李四",
				assignee: 2,
				assigneeName: "李技师",
				resolveTime: "2024-01-14T17:45:00Z"
			}
		];
	} catch (error: any) {
		uni.showToast({
			title: error.message || "加载失败",
			icon: "none"
		});
	} finally {
		loading.value = false;
	}
};

onMounted(() => {
	loadFaultList();
});
</script>

<style scoped>
.fault-manage {
	padding: 20rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.fault-stats {
	display: flex;
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.stat-item {
	flex: 1;
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx 20rpx;
	display: flex;
	align-items: center;
	gap: 15rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.stat-icon {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.stat-icon.pending {
	background-color: #fff7e6;
}

.stat-icon.processing {
	background-color: #e6f7ff;
}

.stat-icon.resolved {
	background-color: #f6ffed;
}

.stat-info {
	flex: 1;
}

.stat-number {
	font-size: 20px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.stat-label {
	font-size: 12px;
	color: #666;
	display: block;
}

.filter-section {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-bar {
	margin-bottom: 20rpx;
}

.filter-tabs {
	display: flex;
	gap: 10rpx;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
}

.tab-item {
	position: relative;
	padding: 15rpx 25rpx;
	border-radius: 20rpx;
	background-color: #f0f0f0;
	color: #666;
	font-size: 14px;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.tab-item.active {
	background-color: #1890ff;
	color: white;
}

.tab-badge {
	background-color: #ff4d4f;
	color: white;
	font-size: 10px;
	padding: 2rpx 8rpx;
	border-radius: 10rpx;
	min-width: 20rpx;
	height: 20rpx;
	line-height: 16rpx;
	text-align: center;
}

.tab-item.active .tab-badge {
	background-color: rgba(255, 255, 255, 0.3);
}

.filter-controls {
	display: flex;
	gap: 20rpx;
}

.filter-select {
	flex: 1;
}

.fault-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	margin-bottom: 20rpx;
}

.fault-item {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.fault-item:active {
	transform: scale(0.98);
}

.fault-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 15rpx;
}

.fault-title {
	display: flex;
	align-items: center;
	gap: 15rpx;
	flex: 1;
}

.device-name {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.severity-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 10px;
}

.severity-badge.critical {
	background-color: #ff4d4f;
	color: white;
}

.severity-badge.high {
	background-color: #fa8c16;
	color: white;
}

.severity-badge.medium {
	background-color: #fadb14;
	color: #333;
}

.severity-badge.low {
	background-color: #52c41a;
	color: white;
}

.severity-text {
	font-size: 10px;
}

.fault-content {
	margin-bottom: 20rpx;
}

.fault-desc {
	font-size: 14px;
	color: #333;
	line-height: 1.5;
	display: block;
	margin-bottom: 15rpx;
}

.fault-meta {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.fault-type,
.fault-location,
.fault-time {
	font-size: 12px;
	color: #666;
}

.fault-actions {
	display: flex;
	gap: 15rpx;
	justify-content: flex-end;
}

.load-more {
	text-align: center;
	padding: 30rpx;
}

.empty-state {
	text-align: center;
	padding: 100rpx 30rpx;
}

.empty-text {
	font-size: 16px;
	color: #666;
	display: block;
	margin: 20rpx 0 10rpx;
}

.empty-desc {
	font-size: 14px;
	color: #999;
	display: block;
}

.handle-modal,
.assign-modal {
	background-color: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 30rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.modal-title {
	font-size: 18px;
	font-weight: 500;
	color: #333;
}

.modal-content {
	margin-bottom: 30rpx;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 14px;
	color: #333;
	display: block;
	margin-bottom: 15rpx;
}

.modal-footer {
	text-align: center;
}

.technician-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.technician-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.technician-item.selected {
	background-color: #e6f7ff;
	border-color: #1890ff;
}

.tech-avatar {
	flex-shrink: 0;
}

.tech-info {
	flex: 1;
}

.tech-name {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.tech-role {
	font-size: 12px;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

.tech-status {
	font-size: 12px;
	color: #52c41a;
	display: block;
}

.tech-stats {
	text-align: right;
}

.stat-text {
	font-size: 12px;
	color: #666;
	display: block;
	margin-bottom: 5rpx;
}

/* 批量操作栏样式 */
.batch-actions {
	background-color: #f8f9fa;
	border-bottom: 1px solid #e9ecef;
	padding: 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.batch-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.selected-count {
	font-size: 14px;
	color: #666;
}

.batch-buttons {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

/* 故障项选中状态 */
.fault-item.selected {
	background-color: #f0f8ff;
	border: 2rpx solid #1890ff;
}

.fault-select {
	margin-right: 15rpx;
}

/* 进度步骤样式 */
.progress-steps {
	padding: 30rpx 0;
}

.step-item {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
	padding: 20rpx;
	border-radius: 8rpx;
	transition: all 0.3s ease;
	cursor: pointer;
}

.step-item.active {
	background-color: #e6f7ff;
	border: 2rpx solid #1890ff;
}

.step-item.completed {
	background-color: #f6ffed;
	border: 2rpx solid #52c41a;
}

.step-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background-color: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.step-item.active .step-icon {
	background-color: #1890ff;
	color: white;
}

.step-item.completed .step-icon {
	background-color: #52c41a;
	color: white;
}

.step-text {
	font-size: 14px;
	color: #333;
}

/* 维护记录样式 */
.maintenance-list {
	max-height: 600rpx;
	overflow-y: auto;
}

.maintenance-item {
	padding: 25rpx;
	border-bottom: 1px solid #f0f0f0;
}

.maintenance-item:last-child {
	border-bottom: none;
}

.record-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.record-type {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.record-time {
	font-size: 12px;
	color: #666;
}

.record-desc {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
	margin-bottom: 10rpx;
	display: block;
}

.record-operator {
	font-size: 12px;
	color: #999;
	display: block;
}

.add-record {
	text-align: center;
	padding: 30rpx;
	border-top: 1px solid #f0f0f0;
	margin-top: 20rpx;
}

/* 弹窗样式补充 */
.progress-modal,
.maintenance-modal,
.add-record-modal {
	background-color: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 30rpx;
	max-height: 80vh;
	overflow-y: auto;
}

/* 故障上报弹窗样式 */
.report-modal {
	background-color: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 30rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.device-select {
	border: 1px solid #e0e0e0;
	border-radius: 8rpx;
	padding: 24rpx;
	background: #fff;
}

.image-upload {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	margin-top: 20rpx;
}

.image-item {
	position: relative;
	width: 160rpx;
	height: 160rpx;
	border-radius: 16rpx;
	overflow: hidden;
}

.image-item image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.image-remove {
	position: absolute;
	top: -10rpx;
	right: -10rpx;
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: #ff4757;
	color: #fff;
	font-size: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.upload-btn {
	width: 160rpx;
	height: 160rpx;
	border: 4rpx dashed #ddd;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fafafa;
	color: #999;
	font-size: 48rpx;
}

/* 维护计划弹窗样式 */
.maintenance-plan-modal {
	background-color: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 30rpx;
	max-height: 85vh;
	overflow-y: auto;
}

.calendar-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 0;
	border-bottom: 1px solid #f0f0f0;
	margin-bottom: 30rpx;
}

.month-nav {
	display: flex;
	align-items: center;
	gap: 30rpx;
}

.nav-btn {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #666;
}

.current-month {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.calendar-grid {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	gap: 2rpx;
	background: #f0f0f0;
	border-radius: 16rpx;
	overflow: hidden;
	margin-bottom: 40rpx;
}

.calendar-header-cell {
	padding: 20rpx;
	text-align: center;
	background: #f8f9fa;
	font-weight: 600;
	color: #666;
	font-size: 28rpx;
}

.calendar-day {
	padding: 16rpx;
	text-align: center;
	background: #fff;
	min-height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	cursor: pointer;
}

.calendar-day.other-month {
	color: #ccc;
	background: #fafafa;
}

.calendar-day.today {
	background: #e3f2fd;
	color: #1976d2;
	font-weight: 600;
}

.calendar-day.has-plan {
	background: #fff3e0;
	color: #f57c00;
}

.calendar-day.has-plan::after {
	content: '';
	position: absolute;
	bottom: 4rpx;
	right: 4rpx;
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: #ff9800;
}

.plan-list {
	max-height: 400rpx;
	overflow-y: auto;
}

.plan-item {
	padding: 24rpx;
	border: 1px solid #e0e0e0;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	background: #fff;
}

.plan-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.plan-title {
	font-weight: 600;
	color: #333;
}

.plan-status {
	padding: 4rpx 16rpx;
	border-radius: 24rpx;
	font-size: 24rpx;
	color: #fff;
}

.plan-status.pending {
	background: #ff9800;
}

.plan-status.in_progress {
	background: #2196f3;
}

.plan-status.completed {
	background: #4caf50;
}

.plan-status.overdue {
	background: #f44336;
}

.plan-desc {
	color: #666;
	font-size: 28rpx;
	margin-bottom: 10rpx;
}

.plan-time {
	color: #999;
	font-size: 24rpx;
}

/* 统计分析弹窗样式 */
.statistics-modal {
	background-color: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 30rpx;
	max-height: 85vh;
	overflow-y: auto;
}

.stats-tabs {
	display: flex;
	border-bottom: 1px solid #e0e0e0;
	margin-bottom: 40rpx;
}

.stats-tab {
	padding: 24rpx 40rpx;
	border-bottom: 4rpx solid transparent;
	color: #666;
	cursor: pointer;
	transition: all 0.3s;
}

.stats-tab.active {
	color: #1976d2;
	border-bottom-color: #1976d2;
}

.stats-overview {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
	margin-bottom: 40rpx;
}

.overview-item {
	padding: 40rpx;
	border: 1px solid #e0e0e0;
	border-radius: 16rpx;
	text-align: center;
	background: #fff;
}

.overview-value {
	font-size: 48rpx;
	font-weight: 600;
	color: #1976d2;
	margin-bottom: 10rpx;
}

.overview-label {
	color: #666;
	font-size: 28rpx;
}

.chart-placeholder {
	height: 600rpx;
	border: 4rpx dashed #ddd;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999;
	background: #fafafa;
}

.type-stats {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
}

.type-item {
	padding: 30rpx;
	border: 1px solid #e0e0e0;
	border-radius: 16rpx;
	background: #fff;
}

.type-name {
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
}

.type-count {
	font-size: 40rpx;
	color: #1976d2;
	margin-bottom: 10rpx;
}

.type-rate {
	color: #666;
	font-size: 24rpx;
}

/* 快速操作栏样式 */
.quick-actions {
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;
	padding: 0 30rpx;
}

.quick-btn {
	flex: 1;
	padding: 24rpx;
	border-radius: 16rpx;
	background: #fff;
	border: 1px solid #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	color: #333;
	font-size: 28rpx;
	transition: all 0.3s;
}

.quick-btn:active {
	background: #f5f5f5;
	transform: scale(0.98);
}

.quick-btn .icon {
	font-size: 32rpx;
}

.quick-btn.primary {
	background: #1976d2;
	color: #fff;
	border-color: #1976d2;
}

.quick-btn.primary:active {
	background: #1565c0;
}
</style>