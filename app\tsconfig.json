{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"ignoreDeprecations": "5.0", "verbatimModuleSyntax": true, "experimentalDecorators": true, "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "noImplicitAny": false, "types": ["@dcloudio/types", "vue", "./uni_modules/cool-ui/index.d.ts"], "lib": ["esnext", "dom"], "outDir": "esbuild", "paths": {"@/*": ["./*"], "$/*": ["./uni_modules/*"]}}, "vueCompilerOptions": {"extensions": [".vue", ".uvue"]}, "include": ["**/*.uts", "**/*.ts", "main.ts", "**/*.u<PERSON>", "**/*.vue", "App.uvue", "**/*.d.ts"], "exclude": ["node_modules", "dist"]}