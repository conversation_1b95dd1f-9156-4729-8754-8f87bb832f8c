<template>
  <view class="cl-config" v-show="false">
    <!-- 配置组件，不显示内容，仅用于全局配置 -->
  </view>
</template>

<script lang="ts" setup>
import { onMounted, watch } from 'vue'

// 定义props
interface Props {
  theme?: 'light' | 'dark' | 'auto'
  navigationStyle?: 'default' | 'custom'
  tabBar?: string | object
  primaryColor?: string
  backgroundColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'light',
  navigationStyle: 'default',
  tabBar: '',
  primaryColor: '#3a86ff',
  backgroundColor: '#f5f5f5'
})

// 应用主题配置
const applyTheme = () => {
  const root = document.documentElement || document.body
  if (root) {
    root.style.setProperty('--primary-color', props.primaryColor)
    root.style.setProperty('--background-color', props.backgroundColor)
    
    // 设置主题类
    root.classList.remove('theme-light', 'theme-dark')
    if (props.theme === 'auto') {
      // 自动检测系统主题
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
      root.classList.add(prefersDark ? 'theme-dark' : 'theme-light')
    } else {
      root.classList.add(`theme-${props.theme}`)
    }
  }
}

// 应用导航样式配置
const applyNavigationStyle = () => {
  if (props.navigationStyle === 'custom') {
    // 自定义导航栏样式
    uni.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: props.primaryColor
    })
  }
}

// 应用TabBar配置
const applyTabBarConfig = () => {
  if (props.tabBar) {
    try {
      let tabBarConfig
      if (typeof props.tabBar === 'string') {
        tabBarConfig = JSON.parse(props.tabBar)
      } else {
        tabBarConfig = props.tabBar
      }
      
      if (tabBarConfig && tabBarConfig.list) {
        // 这里可以动态设置TabBar配置
        console.log('TabBar配置:', tabBarConfig)
      }
    } catch (error) {
      console.error('TabBar配置解析失败:', error)
    }
  }
}

// 监听配置变化
watch(() => props.theme, applyTheme)
watch(() => props.primaryColor, applyTheme)
watch(() => props.backgroundColor, applyTheme)
watch(() => props.navigationStyle, applyNavigationStyle)
watch(() => props.tabBar, applyTabBarConfig)

// 组件挂载时应用配置
onMounted(() => {
  applyTheme()
  applyNavigationStyle()
  applyTabBarConfig()
})
</script>

<style>
.cl-config {
  display: none;
}

/* 主题变量定义 */
:root {
  --primary-color: #3a86ff;
  --success-color: #07c160;
  --warning-color: #ff9500;
  --danger-color: #fa5151;
  --info-color: #10aeff;
  --background-color: #f5f5f5;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --text-color-placeholder: #999999;
  --border-color: #ebeef5;
  --border-color-light: #f0f0f0;
  --box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  --border-radius: 8rpx;
  --border-radius-lg: 12rpx;
  --status-bar-height: 44px;
  --nav-bar-height: 44px;
  --safe-area-height: 34px;
}

/* 浅色主题 */
.theme-light {
  --background-color: #f5f5f5;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --text-color-placeholder: #999999;
  --border-color: #ebeef5;
  --card-background: #ffffff;
}

/* 深色主题 */
.theme-dark {
  --background-color: #1a1a1a;
  --text-color: #ffffff;
  --text-color-secondary: #cccccc;
  --text-color-placeholder: #999999;
  --border-color: #333333;
  --card-background: #2a2a2a;
}

/* 全局样式应用主题变量 */
page {
  background-color: var(--background-color);
  color: var(--text-color);
}

.card {
  background-color: var(--card-background);
  border: 1rpx solid var(--border-color);
}

.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
</style>
