<template>
	<cl-page>
		<cl-topbar fixed :show-back="false" safe-area-top :height="isMp() ? null : 100">
			<view
				class="flex flex-row items-center p-3 flex-1 w-full"
				:class="{
					'pt-0': isMp()
				}"
			>
				<view class="bg-primary-500 rounded-lg p-[12rpx]">
					<cl-image
						src="/static/logo.png"
						:width="32"
						:height="32"
						:show-loading="false"
					></cl-image>
				</view>

				<cl-text
				color="primary"
				:pt="{
					className: '!text-xl mr-auto ml-2 flex-1'
				}"
			>
				节能灯管理系统
			</cl-text>

				<view
					class="bg-surface-500 h-8 w-8 rounded-full flex flex-row items-center justify-center mr-3"
					@tap="setSize"
				>
					<cl-icon name="font-size" color="white"></cl-icon>
				</view>

				<view
					class="bg-primary-500 h-8 w-8 rounded-full flex flex-row items-center justify-center"
					:class="{
						'mr-24': isMp()
					}"
					@tap="setLocale"
				>
					<cl-icon name="translate" color="white"></cl-icon>
				</view>
			</view>
		</cl-topbar>

		<view class="p-3">
			<view class="group" v-for="item in data" :key="item.label">
				<cl-text :pt="{ className: '!text-sm !text-surface-400 mb-2 ml-2' }">{{
					item.label
				}}</cl-text>

				<view class="list">
					<cl-row :gutter="10">
						<cl-col :span="6" v-for="child in item.children" :key="child.label">
							<view
								class="item dark:!bg-surface-800"
								hover-class="opacity-80"
								:hover-stay-time="50"
								@tap="toPath(child)"
							>
								<cl-icon :name="child.icon" :size="36"></cl-icon>
								<cl-text
									:pt="{
										className: 'mt-1 !text-xs text-center'
									}"
									>{{ child.label }}</cl-text
								>
							</view>
						</cl-col>
					</cl-row>
				</view>
			</view>
		</view>

		<!-- 自定义底部导航栏 -->
		<tabbar></tabbar>

		<!-- 主题设置 -->
		<locale-set :ref="refs.set('localeSet')"></locale-set>

		<!-- 字体大小设置 -->
		<size-set :ref="refs.set('sizeSet')"></size-set>
	</cl-page>
</template>

<script lang="ts" setup>
import { isMp, router, useRefs } from "@/cool";
import { t } from "@/locale";
import { computed } from "vue";
import { useUi } from "@/uni_modules/cool-ui";
import { config } from "@/config";
import LocaleSet from "@/components/locale-set.uvue";
import SizeSet from "@/components/size-set.uvue";
import Tabbar from "@/components/tabbar.uvue";

const ui = useUi();
const refs = useRefs();

type Item = {
	label: string;
	icon?: string;
	path?: string;
	disabled?: boolean;
	children?: Item[];
};

const data = computed<Item[]>(() => {
	return [
		{
			label: "设备管理",
			children: [
				{
					label: "设备列表",
					icon: "device-line",
					path: "/pages/energy/device-list"
				},
				{
					label: "设备监控",
					icon: "dashboard-line",
					path: "/pages/energy/device-monitor"
				},
				{
					label: "设备分组",
					icon: "group-line",
					path: "/pages/energy/device-group"
				},
				{
					label: "设备统计",
					icon: "bar-chart-line",
					path: "/pages/energy/device-stats"
				}
			]
		},
		{
			label: "照明控制",
			children: [
				{
					label: "智能调光",
					icon: "lightbulb-line",
					path: "/pages/energy/lighting-control"
				},
				{
					label: "场景模式",
					icon: "palette-line",
					path: "/pages/energy/scene-mode"
				},
				{
					label: "定时控制",
					icon: "timer-line",
					path: "/pages/energy/timer-control"
				},
				{
					label: "批量控制",
					icon: "command-line",
					path: "/pages/energy/batch-control"
				}
			]
		},
		{
			label: "能耗监控",
			children: [
				{
					label: "实时监控",
					icon: "line-chart-line",
					path: "/pages/energy/energy-monitor"
				},
				{
					label: "能耗统计",
					icon: "pie-chart-line",
					path: "/pages/energy/energy-stats"
				},
				{
					label: "节能分析",
					icon: "leaf-line",
					path: "/pages/energy/saving-analysis"
				},
				{
					label: "用电报告",
					icon: "file-chart-line",
					path: "/pages/energy/power-report"
				}
			]
		},
		{
			label: "故障管理",
			children: [
				{
					label: "故障管理",
					icon: "error-warning-line",
					path: "/pages/energy/fault-manage"
				},
				{
					label: "故障诊断",
					icon: "stethoscope-line",
					path: "/pages/energy/fault-diagnosis"
				},
				{
					label: "维修记录",
					icon: "tools-line",
					path: "/pages/energy/repair-record"
				},
				{
					label: "预警设置",
					icon: "alarm-warning-line",
					path: "/pages/energy/alert-settings"
				}
			]
		},
		{
			label: "系统设置",
			children: [
				{
					label: "用户管理",
					icon: "user-settings-line",
					path: "/pages/set/index"
				},
				{
					label: "系统配置",
					icon: "settings-3-line",
					path: "/pages/energy/system-config"
				},
				{
					label: "数据备份",
					icon: "database-2-line",
					path: "/pages/energy/data-backup"
				},
				{
					label: "关于系统",
					icon: "information-line",
					path: "/pages/set/about"
				}
			]
		}
	];
});

function toPath(item: Item) {
	if (item.disabled == true) {
		return ui.showToast({
			message: t("该功能正在开发中")
		});
	}

	router.to(item.path!);
}

function setLocale() {
	refs.open("localeSet");
}

function setSize() {
	refs.open("sizeSet");
}
</script>

<style lang="scss" scoped>
.group {
	@apply mb-10;

	.list {
		.item {
			@apply flex flex-col items-center;
			@apply rounded-xl bg-white px-2;
			height: 140rpx;
			margin-bottom: 10rpx;
			padding-top: 36rpx;
		}
	}
}
</style>
