<template>
	<div ref="containerRef" class="energy-device-container device-management mobile-device-control responsive-table mobile-safe-area">
		<el-card shadow="hover" :body-style="{ padding: 5 }">
			<el-form :model="state.queryParams" ref="queryForm" :inline="true">
				<el-form-item label="设备名称">
					<el-input v-model="state.queryParams.deviceName" placeholder="设备名称" clearable />
				</el-form-item>
				<el-form-item label="设备编号">
					<el-input v-model="state.queryParams.deviceCode" placeholder="设备编号" clearable />
				</el-form-item>
				<el-form-item label="设备类型">
					<el-select v-model="state.queryParams.deviceType" placeholder="请选择设备类型" clearable>
						<el-option label="LED灯" value="LED" />
						<el-option label="节能灯" value="CFL" />
						<el-option label="智能灯" value="SMART" />
					</el-select>
				</el-form-item>
				<el-form-item label="设备状态">
					<el-select v-model="state.queryParams.status" placeholder="请选择设备状态" clearable>
						<el-option label="在线" :value="1" />
						<el-option label="离线" :value="0" />
						<el-option label="故障" :value="2" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Search" @click="handleQuery"> 查询 </el-button>
						<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="primary" icon="ele-Plus" @click="openAddDevice"> 新增设备 </el-button>
						<el-button type="success" icon="ele-Upload" @click="openDeviceImport"> 批量导入 </el-button>
					</el-button-group>
				</el-form-item>
				<el-form-item>
					<el-button-group>
						<el-button type="warning" icon="ele-Collection" @click="openDeviceGroup"> 分组管理 </el-button>
						<el-button type="info" icon="ele-Location" @click="openDeviceMap"> 地图定位 </el-button>
					</el-button-group>
				</el-form-item>
			</el-form>
		</el-card>

		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table ref="tableRef" :data="state.deviceData" style="width: 100%" v-loading="state.loading" border>
				<el-table-column type="index" label="序号" width="55" align="center" fixed />
				<el-table-column prop="deviceName" label="设备名称" width="150" align="center" show-overflow-tooltip />
				<el-table-column prop="deviceCode" label="设备编号" width="150" align="center" show-overflow-tooltip />
				<el-table-column prop="deviceType" label="设备类型" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.deviceType === 'LED'" type="success">LED灯</el-tag>
						<el-tag v-else-if="scope.row.deviceType === 'CFL'" type="warning">节能灯</el-tag>
						<el-tag v-else-if="scope.row.deviceType === 'SMART'" type="primary">智能灯</el-tag>
						<el-tag v-else type="info">{{ scope.row.deviceType }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="location" label="安装位置" width="150" align="center" show-overflow-tooltip />
				<el-table-column prop="power" label="功率(W)" width="100" align="center" show-overflow-tooltip />
				<el-table-column prop="brightness" label="亮度(%)" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-progress :percentage="scope.row.brightness" :show-text="false" :stroke-width="8" />
						<span style="margin-left: 10px">{{ scope.row.brightness }}%</span>
					</template>
				</el-table-column>
				<el-table-column label="设备状态" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-tag v-if="scope.row.status === 1" type="success">在线</el-tag>
						<el-tag v-else-if="scope.row.status === 0" type="danger">离线</el-tag>
						<el-tag v-else-if="scope.row.status === 2" type="warning">故障</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="开关状态" width="100" align="center" show-overflow-tooltip>
					<template #default="scope">
						<el-switch 
							v-model="scope.row.isOn" 
							:active-value="true" 
							:inactive-value="false" 
							size="small"
							:disabled="scope.row.status !== 1"
							@change="changeDeviceStatus(scope.row)" 
						/>
					</template>
				</el-table-column>
				<el-table-column prop="installDate" label="安装日期" width="120" align="center" show-overflow-tooltip>
					<template #default="scope">
						{{ formatDate(scope.row.installDate) }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="200" align="center" fixed="right" show-overflow-tooltip>
					<template #default="scope">
						<el-tooltip content="编辑" placement="top">
							<el-button icon="ele-Edit" text type="primary" @click="openEditDevice(scope.row)"> </el-button>
						</el-tooltip>
						<el-tooltip content="控制" placement="top">
							<el-button icon="ele-Setting" text type="warning" :disabled="scope.row.status !== 1" @click="openControlDevice(scope.row)"> </el-button>
						</el-tooltip>
						<el-tooltip content="删除" placement="top">
							<el-button icon="ele-Delete" text type="danger" @click="delDevice(scope.row)"> </el-button>
						</el-tooltip>
						<el-dropdown>
							<el-button icon="ele-MoreFilled" size="small" text type="primary" style="padding-left: 12px" />
							<template #dropdown>
								<el-dropdown-menu>
									<el-dropdown-item icon="ele-View" @click="viewDeviceDetail(scope.row)">查看详情</el-dropdown-item>
									<el-dropdown-item icon="ele-DataAnalysis" @click="viewEnergyConsumption(scope.row)">能耗统计</el-dropdown-item>
									<el-dropdown-item icon="ele-Warning" @click="viewFaultHistory(scope.row)">故障历史</el-dropdown-item>
								</el-dropdown-menu>
							</template>
						</el-dropdown>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination 
				v-model:currentPage="state.tableParams.page" 
				v-model:page-size="state.tableParams.pageSize"
				@current-change="handleCurrentChange" 
				@size-change="handleSizeChange"
				:total="state.tableParams.total"
				layout="total, sizes, prev, pager, next, jumper" 
			/>
		</el-card>

		<!-- 设备编辑对话框 -->
		<EditDevice ref="editDeviceRef" :title="state.editDeviceTitle" @handleQuery="handleQuery" />
		
		<!-- 设备控制对话框 -->
		<ControlDevice ref="controlDeviceRef" @handleQuery="handleQuery" />
		
		<!-- 设备分组管理对话框 -->
		<DeviceGroup ref="deviceGroupRef" @refresh="handleQuery" />
		
		<!-- 设备批量导入对话框 -->
		<DeviceImport ref="deviceImportRef" @refresh="handleQuery" @success="handleImportSuccess" />
		
		<!-- 设备地图定位对话框 -->
		<DeviceMap ref="deviceMapRef" @refresh="handleQuery" />
	</div>
</template>

<script lang="ts" setup name="energyDevice">
import { onMounted, reactive, ref } from 'vue';
import { ElMessageBox } from 'element-plus';
import EditDevice from './component/editDevice.vue';
import ControlDevice from './component/controlDevice.vue';
import DeviceGroup from './component/deviceGroup.vue';
import DeviceImport from './component/deviceImport.vue';
import DeviceMap from './component/deviceMap.vue';
import { getAPI } from '/@/utils/axios-utils';
import { useDeviceApi } from '/@/api-services/device';
import { formatDate } from '/@/utils/formatTime';
import { useDeviceDetection, usePullToRefresh, useMobileTable } from '@/composables/useMobile';
import { handleApiError } from '@/utils/errorHandler';

const editDeviceRef = ref();
const controlDeviceRef = ref();
const deviceGroupRef = ref();
const deviceImportRef = ref();
const deviceMapRef = ref();
const containerRef = ref(null);
const tableRef = ref(null);

// 移动端适配
const { deviceInfo } = useDeviceDetection();
const { isRefreshing } = usePullToRefresh(containerRef, async () => {
	await handleQuery();
});
const { optimizeTable } = useMobileTable(tableRef);

const state = reactive({
	loading: false,
	deviceData: [] as Array<any>,
	queryParams: {
		deviceName: undefined,
		deviceCode: undefined,
		deviceType: undefined,
		status: undefined
	},
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0 as any,
	},
	editDeviceTitle: '',
});

onMounted(async () => {
	await handleQuery();
});

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	try {
		let params = Object.assign(state.queryParams, state.tableParams);
		const deviceApi = useDeviceApi();
		let res = await deviceApi.getPage(params);
		state.deviceData = res.data.result?.items ?? [];
		state.tableParams.total = res.data.result?.total;
	} catch (error) {
      handleApiError(error, '查询设备列表', '查询设备列表失败');
    } finally {
		state.loading = false;
	}
};

// 重置操作
const resetQuery = async () => {
	state.queryParams.deviceName = undefined;
	state.queryParams.deviceCode = undefined;
	state.queryParams.deviceType = undefined;
	state.queryParams.status = undefined;
	await handleQuery();
};

// 打开新增页面
const openAddDevice = () => {
	state.editDeviceTitle = '添加设备';
	editDeviceRef.value?.openDialog({ 
		id: undefined, 
		power: 10, 
		brightness: 100, 
		status: 1,
		isOn: false,
		installDate: new Date().toISOString().split('T')[0]
	});
};

// 打开编辑页面
const openEditDevice = (row: any) => {
	state.editDeviceTitle = '编辑设备';
	editDeviceRef.value?.openDialog(row);
};

// 打开设备控制页面
const openControlDevice = (row: any) => {
	controlDeviceRef.value?.openDialog(row);
};

// 删除设备
const delDevice = (row: any) => {
	ElMessageBox.confirm(`确定删除设备：【${row.deviceName}】?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			try {
				const deviceApi = useDeviceApi();
			await deviceApi.delete({ id: row.id });
				await handleQuery();
				ElMessage.success('删除成功');
			} catch (error) {
				handleApiError(error, '删除设备', '删除设备失败');
			}
		})
		.catch(() => { });
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	state.tableParams.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = async (val: number) => {
	state.tableParams.page = val;
	await handleQuery();
};

// 修改设备开关状态
const changeDeviceStatus = async (row: any) => {
	try {
		const deviceApi = useDeviceApi();
		await deviceApi.control({
			deviceId: row.id,
			action: row.isOn ? 'ON' : 'OFF',
			brightness: row.brightness
		});
		ElMessage.success(`设备${row.isOn ? '开启' : '关闭'}成功`);
	} catch (error) {
		handleApiError(error, '控制设备', '控制设备失败');
		// 恢复原状态
		row.isOn = !row.isOn;
	}
};

// 查看设备详情
const viewDeviceDetail = (row: any) => {
	// 跳转到设备详情页面
	window.open(`/energy/device/detail/${row.id}`, '_blank');
};

// 查看能耗统计
const viewEnergyConsumption = (row: any) => {
	// 跳转到能耗统计页面，传递设备ID参数
	window.open(`/energy/consumption?deviceId=${row.id}&deviceName=${encodeURIComponent(row.deviceName)}`, '_blank');
};

// 查看故障历史
const viewFaultHistory = (row: any) => {
	// 跳转到故障历史页面，传递设备ID参数
	window.open(`/energy/fault?deviceId=${row.id}&deviceName=${encodeURIComponent(row.deviceName)}`, '_blank');
};

// 打开设备分组管理
const openDeviceGroup = () => {
	deviceGroupRef.value?.openDialog();
};

// 打开设备批量导入
const openDeviceImport = () => {
	deviceImportRef.value?.openDialog();
};

// 打开设备地图定位
const openDeviceMap = () => {
	deviceMapRef.value?.openDialog();
};

// 处理导入成功
const handleImportSuccess = (importedCount: number) => {
	ElMessage.success(`成功导入 ${importedCount} 个设备`);
	handleQuery();
};
</script>

<style scoped lang="scss">
.energy-device-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.full-table {
	flex: 1;
	display: flex;
	flex-direction: column;
	
	:deep(.el-card__body) {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	:deep(.el-table) {
		flex: 1;
	}
}

.el-form--inline .el-form-item,
.el-form-item:last-of-type {
	margin: 5px 15px;
}
</style>