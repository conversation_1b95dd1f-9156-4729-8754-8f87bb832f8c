/**
 * 错误处理工具类
 * 统一处理应用中的各种错误和异常情况
 */

export interface ErrorInfo {
	code: string | number
	message: string
	details?: any
	timestamp?: number
}

export interface NetworkError extends ErrorInfo {
	status?: number
	statusText?: string
	url?: string
}

export interface BusinessError extends ErrorInfo {
	businessCode?: string
	context?: any
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
	// 网络错误
	NETWORK_ERROR = 'NETWORK_ERROR',
	TIMEOUT_ERROR = 'TIMEOUT_ERROR',
	CONNECTION_ERROR = 'CONNECTION_ERROR',
	
	// 业务错误
	BUSINESS_ERROR = 'BUSINESS_ERROR',
	VALIDATION_ERROR = 'VALIDATION_ERROR',
	PERMISSION_ERROR = 'PERMISSION_ERROR',
	
	// 系统错误
	SYSTEM_ERROR = 'SYSTEM_ERROR',
	UNKNOWN_ERROR = 'UNKNOWN_ERROR',
	
	// 设备错误
	DEVICE_OFFLINE = 'DEVICE_OFFLINE',
	DEVICE_ERROR = 'DEVICE_ERROR'
}

/**
 * 错误处理器类
 */
class ErrorHandler {
	private static instance: ErrorHandler
	private errorCallbacks: Map<ErrorType, Function[]> = new Map()
	
	static getInstance(): ErrorHandler {
		if (!ErrorHandler.instance) {
			ErrorHandler.instance = new ErrorHandler()
		}
		return ErrorHandler.instance
	}
	
	/**
	 * 注册错误回调
	 */
	onError(type: ErrorType, callback: Function) {
		if (!this.errorCallbacks.has(type)) {
			this.errorCallbacks.set(type, [])
		}
		this.errorCallbacks.get(type)!.push(callback)
	}
	
	/**
	 * 处理错误
	 */
	handle(error: any, context?: string): ErrorInfo {
		const errorInfo = this.parseError(error, context)
		
		// 执行注册的回调
		const callbacks = this.errorCallbacks.get(errorInfo.code as ErrorType)
		if (callbacks) {
			callbacks.forEach(callback => {
				try {
					callback(errorInfo)
				} catch (e) {
					console.error('Error callback failed:', e)
				}
			})
		}
		
		// 记录错误日志
		this.logError(errorInfo, context)
		
		return errorInfo
	}
	
	/**
	 * 解析错误信息
	 */
	private parseError(error: any, context?: string): ErrorInfo {
		const timestamp = Date.now()
		
		// 网络错误
		if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
			return {
				code: ErrorType.NETWORK_ERROR,
				message: '网络连接异常，请检查网络设置',
				details: error,
				timestamp
			}
		}
		
		// 超时错误
		if (error.name === 'TimeoutError' || error.code === 'TIMEOUT') {
			return {
				code: ErrorType.TIMEOUT_ERROR,
				message: '请求超时，请稍后重试',
				details: error,
				timestamp
			}
		}
		
		// HTTP 状态码错误
		if (error.status) {
			return this.parseHttpError(error, timestamp)
		}
		
		// 业务错误
		if (error.businessCode || error.code) {
			return {
				code: ErrorType.BUSINESS_ERROR,
				message: error.message || '业务处理异常',
				details: error,
				timestamp
			}
		}
		
		// 设备离线错误
		if (error.message && error.message.includes('设备离线')) {
			return {
				code: ErrorType.DEVICE_OFFLINE,
				message: '设备当前离线，无法执行操作',
				details: error,
				timestamp
			}
		}
		
		// 权限错误
		if (error.message && (error.message.includes('权限') || error.message.includes('unauthorized'))) {
			return {
				code: ErrorType.PERMISSION_ERROR,
				message: '权限不足，请联系管理员',
				details: error,
				timestamp
			}
		}
		
		// 默认未知错误
		return {
			code: ErrorType.UNKNOWN_ERROR,
			message: error.message || '未知错误，请稍后重试',
			details: error,
			timestamp
		}
	}
	
	/**
	 * 解析 HTTP 错误
	 */
	private parseHttpError(error: any, timestamp: number): NetworkError {
		const status = error.status || error.statusCode
		let message = '网络请求失败'
		let code = ErrorType.NETWORK_ERROR
		
		switch (status) {
			case 400:
				message = '请求参数错误'
				code = ErrorType.VALIDATION_ERROR
				break
			case 401:
				message = '身份验证失败，请重新登录'
				code = ErrorType.PERMISSION_ERROR
				break
			case 403:
				message = '权限不足，无法访问'
				code = ErrorType.PERMISSION_ERROR
				break
			case 404:
				message = '请求的资源不存在'
				break
			case 500:
				message = '服务器内部错误'
				code = ErrorType.SYSTEM_ERROR
				break
			case 502:
			case 503:
			case 504:
				message = '服务暂时不可用，请稍后重试'
				code = ErrorType.SYSTEM_ERROR
				break
			default:
				message = `网络错误 (${status})`
		}
		
		return {
			code,
			message,
			status,
			statusText: error.statusText,
			url: error.url,
			details: error,
			timestamp
		}
	}
	
	/**
	 * 记录错误日志
	 */
	private logError(errorInfo: ErrorInfo, context?: string) {
		const logData = {
			...errorInfo,
			context,
			userAgent: navigator.userAgent,
			url: window.location?.href || 'unknown'
		}
		
		console.error('[ErrorHandler]', logData)
		
		// 这里可以添加错误上报逻辑
		// this.reportError(logData)
	}
	
	/**
	 * 上报错误（可选实现）
	 */
	private reportError(errorData: any) {
		// 实现错误上报逻辑
		// 例如发送到错误监控服务
	}
	
	/**
	 * 获取用户友好的错误消息
	 */
	getFriendlyMessage(error: any): string {
		const errorInfo = this.parseError(error)
		return errorInfo.message
	}
	
	/**
	 * 检查是否为网络错误
	 */
	isNetworkError(error: any): boolean {
		const errorInfo = this.parseError(error)
		return [ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR, ErrorType.CONNECTION_ERROR].includes(errorInfo.code as ErrorType)
	}
	
	/**
	 * 检查是否为权限错误
	 */
	isPermissionError(error: any): boolean {
		const errorInfo = this.parseError(error)
		return errorInfo.code === ErrorType.PERMISSION_ERROR
	}
	
	/**
	 * 检查是否为设备离线错误
	 */
	isDeviceOfflineError(error: any): boolean {
		const errorInfo = this.parseError(error)
		return errorInfo.code === ErrorType.DEVICE_OFFLINE
	}
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance()

// 导出便捷方法
export const handleError = (error: any, context?: string) => {
	return errorHandler.handle(error, context)
}

export const getFriendlyMessage = (error: any) => {
	return errorHandler.getFriendlyMessage(error)
}

export const isNetworkError = (error: any) => {
	return errorHandler.isNetworkError(error)
}

export const isPermissionError = (error: any) => {
	return errorHandler.isPermissionError(error)
}

export const isDeviceOfflineError = (error: any) => {
	return errorHandler.isDeviceOfflineError(error)
}

// 全局错误处理
if (typeof window !== 'undefined') {
	// 捕获未处理的 Promise 错误
	window.addEventListener('unhandledrejection', (event) => {
		const error = event.reason
		errorHandler.handle(error, 'unhandledrejection')
		// 阻止默认的控制台错误输出
		event.preventDefault()
	})
	
	// 捕获 JavaScript 运行时错误
	window.addEventListener('error', (event) => {
		const error = {
			message: event.message,
			filename: event.filename,
			lineno: event.lineno,
			colno: event.colno,
			error: event.error
		}
		errorHandler.handle(error, 'javascript-error')
	})
}