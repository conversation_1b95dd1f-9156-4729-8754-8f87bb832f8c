<template>
  <cl-app>
    <cl-config
      theme="auto"
      navigationStyle="custom"
      tabBar="{color: '#999', selectedColor: '#007aff', backgroundColor: '#ffffff', list: [{pagePath: '/pages/index/home', text: '首页', iconPath: '/static/icons/home.png', selectedIconPath: '/static/icons/home_selected.png'}, {pagePath: '/pages/energy/device-list', text: '设备', iconPath: '/static/icons/device.png', selectedIconPath: '/static/icons/device_selected.png'}, {pagePath: '/pages/energy/energy-monitor', text: '能耗', iconPath: '/static/icons/energy.png', selectedIconPath: '/static/icons/energy_selected.png'}, {pagePath: '/pages/index/my', text: '我的', iconPath: '/static/icons/my.png', selectedIconPath: '/static/icons/my_selected.png'}]}"
    />
  </cl-app>
</template>

<script lang="ts" setup>
import { onLaunch, onShow, onHide } from 'vue'
import { useUserStore } from '@/stores/user'
import { useDictStore } from '@/stores/dict'
import './styles/common.css'

// 应用生命周期
onLaunch(() => {
  console.log('App launched')
  const userStore = useUserStore()
  const dictStore = useDictStore()
  // 初始化用户信息
  userStore.initUserInfo()
  // 初始化字典数据
  dictStore.initDictData()
})

onShow(() => {
  console.log('App shown')
})

onHide(() => {
  console.log('App hidden')
})
</script>

<style>
/* 全局样式 */
page {
  --status-bar-height: 25px;
  --window-bottom: 0px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f5f5f5;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
</style>