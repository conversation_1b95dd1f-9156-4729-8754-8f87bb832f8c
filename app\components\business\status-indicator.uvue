<template>
	<view class="status-indicator" :class="[`status-${status}`, `size-${size}`]">
		<!-- 状态图标 -->
		<view class="status-icon">
			<cl-icon 
				:name="statusConfig.icon" 
				:size="iconSize" 
				:color="statusConfig.color" 
			/>
			
			<!-- 动画效果 -->
			<view v-if="showAnimation" class="status-animation" :class="animationClass"></view>
		</view>
		
		<!-- 状态文本 -->
		<view v-if="showText" class="status-text">
			<text class="status-label" :style="{ color: statusConfig.color }">{{ statusConfig.text }}</text>
			<text v-if="subText" class="status-sub">{{ subText }}</text>
		</view>
		
		<!-- 状态点 -->
		<view v-if="showDot" class="status-dot" :style="{ backgroundColor: statusConfig.color }">
			<view v-if="showPulse" class="dot-pulse" :style="{ backgroundColor: statusConfig.color }"></view>
		</view>
		
		<!-- 进度条 (用于加载状态) -->
		<view v-if="status === 'loading' && showProgress" class="status-progress">
			<view class="progress-bar">
				<view 
					class="progress-fill" 
					:style="{ 
						width: progress + '%',
						backgroundColor: statusConfig.color 
					}"
				></view>
			</view>
			<text class="progress-text">{{ progress }}%</text>
		</view>
		
		<!-- 详细信息 -->
		<view v-if="showDetail && detailInfo" class="status-detail">
			<view class="detail-item" v-for="(item, key) in detailInfo" :key="key">
				<text class="detail-label">{{ key }}:</text>
				<text class="detail-value">{{ item }}</text>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view v-if="showAction && actionConfig" class="status-action">
			<cl-button 
				:type="actionConfig.type || 'default'"
				:size="actionConfig.size || 'small'"
				@click="handleAction"
			>
				{{ actionConfig.text }}
			</cl-button>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";

// 状态类型定义
type StatusType = 
	| 'online' 
	| 'offline' 
	| 'fault' 
	| 'warning' 
	| 'loading' 
	| 'success' 
	| 'error' 
	| 'maintenance' 
	| 'standby'
	| 'active'
	| 'inactive';

type SizeType = 'small' | 'medium' | 'large';

// Props
interface StatusIndicatorProps {
	status: StatusType;
	size?: SizeType;
	showText?: boolean;
	showDot?: boolean;
	showPulse?: boolean;
	showAnimation?: boolean;
	showProgress?: boolean;
	showDetail?: boolean;
	showAction?: boolean;
	subText?: string;
	progress?: number;
	detailInfo?: Record<string, string | number>;
	actionConfig?: {
		text: string;
		type?: 'primary' | 'default' | 'success' | 'warning' | 'error';
		size?: 'small' | 'medium' | 'large';
	};
	customConfig?: {
		icon?: string;
		color?: string;
		text?: string;
	};
}

const props = withDefaults(defineProps<StatusIndicatorProps>(), {
	size: 'medium',
	showText: true,
	showDot: false,
	showPulse: false,
	showAnimation: false,
	showProgress: false,
	showDetail: false,
	showAction: false,
	progress: 0
});

// Emits
const emit = defineEmits<{
	action: [status: StatusType];
	statusChange: [oldStatus: StatusType, newStatus: StatusType];
}>();

// 响应式数据
const animationTimer = ref<number | null>(null);

// 状态配置
const statusConfigs = {
	online: {
		icon: 'cl-icon-wifi',
		color: '#52c41a',
		text: '在线',
		animation: 'pulse'
	},
	offline: {
		icon: 'cl-icon-wifi-off',
		color: '#ff4d4f',
		text: '离线',
		animation: 'none'
	},
	fault: {
		icon: 'cl-icon-alert-triangle',
		color: '#ff4d4f',
		text: '故障',
		animation: 'shake'
	},
	warning: {
		icon: 'cl-icon-alert-circle',
		color: '#faad14',
		text: '警告',
		animation: 'blink'
	},
	loading: {
		icon: 'cl-icon-loading',
		color: '#1890ff',
		text: '加载中',
		animation: 'spin'
	},
	success: {
		icon: 'cl-icon-check-circle',
		color: '#52c41a',
		text: '成功',
		animation: 'bounce'
	},
	error: {
		icon: 'cl-icon-x-circle',
		color: '#ff4d4f',
		text: '错误',
		animation: 'shake'
	},
	maintenance: {
		icon: 'cl-icon-tool',
		color: '#722ed1',
		text: '维护中',
		animation: 'pulse'
	},
	standby: {
		icon: 'cl-icon-pause-circle',
		color: '#d9d9d9',
		text: '待机',
		animation: 'none'
	},
	active: {
		icon: 'cl-icon-play-circle',
		color: '#1890ff',
		text: '运行中',
		animation: 'pulse'
	},
	inactive: {
		icon: 'cl-icon-stop-circle',
		color: '#8c8c8c',
		text: '未激活',
		animation: 'none'
	}
};

// 尺寸配置
const sizeConfigs = {
	small: {
		iconSize: 16,
		dotSize: 8,
		fontSize: 12
	},
	medium: {
		iconSize: 20,
		dotSize: 10,
		fontSize: 14
	},
	large: {
		iconSize: 24,
		dotSize: 12,
		fontSize: 16
	}
};

// 计算属性
const statusConfig = computed(() => {
	const baseConfig = statusConfigs[props.status];
	if (props.customConfig) {
		return {
			...baseConfig,
			...props.customConfig
		};
	}
	return baseConfig;
});

const sizeConfig = computed(() => sizeConfigs[props.size]);

const iconSize = computed(() => sizeConfig.value.iconSize);

const animationClass = computed(() => {
	if (!props.showAnimation) return '';
	return `animation-${statusConfig.value.animation}`;
});

// 处理操作按钮点击
const handleAction = () => {
	emit('action', props.status);
};

// 监听状态变化
watch(() => props.status, (newStatus, oldStatus) => {
	if (oldStatus) {
		emit('statusChange', oldStatus, newStatus);
	}
});

// 清理定时器
const clearAnimationTimer = () => {
	if (animationTimer.value) {
		clearInterval(animationTimer.value);
		animationTimer.value = null;
	}
};

onMounted(() => {
	// 组件挂载后的初始化逻辑
});

onUnmounted(() => {
	clearAnimationTimer();
});
</script>

<style scoped>
.status-indicator {
	display: flex;
	align-items: center;
	gap: 10rpx;
	position: relative;
}

/* 尺寸样式 */
.size-small {
	gap: 8rpx;
}

.size-medium {
	gap: 10rpx;
}

.size-large {
	gap: 12rpx;
}

/* 状态图标 */
.status-icon {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 状态动画 */
.status-animation {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
	border-radius: 50%;
	pointer-events: none;
}

/* 动画效果 */
.animation-pulse {
	animation: pulse 2s infinite;
}

.animation-spin {
	animation: spin 1s linear infinite;
}

.animation-shake {
	animation: shake 0.5s ease-in-out infinite;
}

.animation-blink {
	animation: blink 1s ease-in-out infinite;
}

.animation-bounce {
	animation: bounce 0.6s ease-in-out;
}

@keyframes pulse {
	0% {
		opacity: 1;
		transform: translate(-50%, -50%) scale(1);
	}
	50% {
		opacity: 0.5;
		transform: translate(-50%, -50%) scale(1.1);
	}
	100% {
		opacity: 1;
		transform: translate(-50%, -50%) scale(1);
	}
}

@keyframes spin {
	0% {
		transform: translate(-50%, -50%) rotate(0deg);
	}
	100% {
		transform: translate(-50%, -50%) rotate(360deg);
	}
}

@keyframes shake {
	0%, 100% {
		transform: translate(-50%, -50%) translateX(0);
	}
	25% {
		transform: translate(-50%, -50%) translateX(-2px);
	}
	75% {
		transform: translate(-50%, -50%) translateX(2px);
	}
}

@keyframes blink {
	0%, 100% {
		opacity: 1;
	}
	50% {
		opacity: 0.3;
	}
}

@keyframes bounce {
	0%, 20%, 53%, 80%, 100% {
		transform: translate(-50%, -50%) translateY(0);
	}
	40%, 43% {
		transform: translate(-50%, -50%) translateY(-8px);
	}
	70% {
		transform: translate(-50%, -50%) translateY(-4px);
	}
	90% {
		transform: translate(-50%, -50%) translateY(-2px);
	}
}

/* 状态文本 */
.status-text {
	display: flex;
	flex-direction: column;
	gap: 2rpx;
}

.status-label {
	font-weight: 500;
	line-height: 1.2;
}

.size-small .status-label {
	font-size: 12px;
}

.size-medium .status-label {
	font-size: 14px;
}

.size-large .status-label {
	font-size: 16px;
}

.status-sub {
	font-size: 10px;
	color: #999;
	line-height: 1.2;
}

.size-medium .status-sub {
	font-size: 11px;
}

.size-large .status-sub {
	font-size: 12px;
}

/* 状态点 */
.status-dot {
	border-radius: 50%;
	position: relative;
	flex-shrink: 0;
}

.size-small .status-dot {
	width: 8rpx;
	height: 8rpx;
}

.size-medium .status-dot {
	width: 10rpx;
	height: 10rpx;
}

.size-large .status-dot {
	width: 12rpx;
	height: 12rpx;
}

/* 点脉冲效果 */
.dot-pulse {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
	border-radius: 50%;
	opacity: 0.6;
	animation: dotPulse 2s infinite;
}

@keyframes dotPulse {
	0% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 0.6;
	}
	100% {
		transform: translate(-50%, -50%) scale(2);
		opacity: 0;
	}
}

/* 进度条 */
.status-progress {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	flex: 1;
	min-width: 100rpx;
}

.progress-bar {
	height: 6rpx;
	background-color: #f0f0f0;
	border-radius: 3rpx;
	overflow: hidden;
	position: relative;
}

.progress-fill {
	height: 100%;
	border-radius: 3rpx;
	transition: width 0.3s ease;
	position: relative;
}

.progress-fill::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(
		90deg,
		transparent,
		rgba(255, 255, 255, 0.3),
		transparent
	);
	animation: progressShine 2s infinite;
}

@keyframes progressShine {
	0% {
		transform: translateX(-100%);
	}
	100% {
		transform: translateX(100%);
	}
}

.progress-text {
	font-size: 10px;
	color: #666;
	text-align: center;
}

.size-medium .progress-text {
	font-size: 11px;
}

.size-large .progress-text {
	font-size: 12px;
}

/* 详细信息 */
.status-detail {
	display: flex;
	flex-direction: column;
	gap: 5rpx;
	padding: 10rpx;
	background-color: #f9f9f9;
	border-radius: 6rpx;
	margin-top: 10rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.detail-label {
	font-size: 11px;
	color: #666;
}

.detail-value {
	font-size: 11px;
	color: #333;
	font-weight: 500;
}

.size-medium .detail-label,
.size-medium .detail-value {
	font-size: 12px;
}

.size-large .detail-label,
.size-large .detail-value {
	font-size: 13px;
}

/* 操作按钮 */
.status-action {
	margin-left: 10rpx;
}

/* 状态特定样式 */
.status-online {
	/* 在线状态特定样式 */
}

.status-offline {
	/* 离线状态特定样式 */
}

.status-fault {
	/* 故障状态特定样式 */
}

.status-warning {
	/* 警告状态特定样式 */
}

.status-loading {
	/* 加载状态特定样式 */
}

.status-success {
	/* 成功状态特定样式 */
}

.status-error {
	/* 错误状态特定样式 */
}

.status-maintenance {
	/* 维护状态特定样式 */
}

.status-standby {
	/* 待机状态特定样式 */
}

.status-active {
	/* 激活状态特定样式 */
}

.status-inactive {
	/* 未激活状态特定样式 */
}
</style>