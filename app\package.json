{"name": "cool-unix", "version": "8.0.11", "license": "MIT", "scripts": {"dev": "uni", "build": "uni build", "build:h5": "uni build --platform h5", "build:mp-weixin": "uni build --platform mp-weixin", "build-ui": "node ./uni_modules/cool-ui/scripts/generate-types.js", "build-icon": "node ./.cool/scripts/icon.js"}, "dependencies": {"@dcloudio/uni-cli-i18n": "2.0.2-4070520250711001", "@dcloudio/uni-components": "3.0.0-alpha-3000020210521001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-4070720250804001", "@dcloudio/uni-shared": "3.0.0-alpha-3000020210521001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4070720250804001", "@dcloudio/vue-cli-plugin-uni": "2.0.2-4070520250711001", "@vue/devtools-api": "^8.0.0", "@vue/reactivity": "3.5.18", "@vue/runtime-core": "3.5.18", "@vue/server-renderer": "^3.5.18", "@vue/shared": "3.5.18", "hammer-touchemulator": "^0.0.2", "pinia": "^3.0.3", "semver": "^7.7.2", "vue": "3.5.18"}, "devDependencies": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.6", "@cool-vue/ai": "^1.1.5", "@cool-vue/vite-plugin": "^8.2.6", "@dcloudio/types": "^3.4.16", "@dcloudio/uni-app": "2.0.2-4070520250711001", "@dcloudio/uni-cli-shared": "2.0.2-4070520250711001", "@dcloudio/uni-h5": "2.0.2-4070520250711001", "@types/node": "^24.0.15", "@vue/compiler-sfc": "^3.5.16", "@vue/devtools-kit": "^8.0.0", "@vue/devtools-shared": "^8.0.0", "@vue/runtime-dom": "^3.5.18", "@vue/tsconfig": "^0.7.0", "adm-zip": "^0.5.16", "autoprefixer": "^10.4.21", "birpc": "^2.5.0", "hookable": "^5.5.3", "perfect-debounce": "^1.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "sass-embedded": "^1.90.0", "tailwindcss": "3.4.17", "tsx": "^4.20.4", "typescript": "^5.9.2", "vite": "^5.4.0", "webpack": "^5.101.3", "webpack-cli": "^6.0.1"}}