<template>
	<view class="empty-state" :class="[`type-${type}`, `size-${size}`]">
		<!-- 图标或图片 -->
		<view class="empty-icon">
			<image 
				v-if="image" 
				:src="image" 
				class="empty-image" 
				mode="aspectFit"
			/>
			<cl-icon 
				v-else 
				:name="iconName" 
				:size="iconSize" 
				:color="iconColor"
			/>
		</view>
		
		<!-- 标题 -->
		<text v-if="title" class="empty-title">{{ title }}</text>
		
		<!-- 描述 -->
		<text v-if="description" class="empty-description">{{ description }}</text>
		
		<!-- 操作按钮 -->
		<view v-if="showAction" class="empty-actions">
			<cl-button 
				v-if="actionText" 
				:type="actionType" 
				:size="actionSize"
				@click="handleAction"
			>
				{{ actionText }}
			</cl-button>
			
			<cl-button 
				v-if="secondaryActionText" 
				type="text" 
				:size="actionSize"
				@click="handleSecondaryAction"
			>
				{{ secondaryActionText }}
			</cl-button>
		</view>
		
		<!-- 自定义内容插槽 -->
		<view v-if="$slots.default" class="empty-custom">
			<slot></slot>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { computed } from "vue";

// 空状态类型
type EmptyType = 'no-data' | 'no-network' | 'no-search' | 'no-permission' | 'error' | 'maintenance' | 'custom';
type EmptySize = 'small' | 'medium' | 'large';

// Props
interface EmptyStateProps {
	type?: EmptyType;
	size?: EmptySize;
	title?: string;
	description?: string;
	image?: string;
	icon?: string;
	iconColor?: string;
	actionText?: string;
	actionType?: 'primary' | 'success' | 'warning' | 'error' | 'default';
	secondaryActionText?: string;
	showAction?: boolean;
}

const props = withDefaults(defineProps<EmptyStateProps>(), {
	type: 'no-data',
	size: 'medium',
	showAction: true,
	actionType: 'primary'
});

// Emits
const emit = defineEmits<{
	action: [];
	secondaryAction: [];
}>();

// 预设配置
const typeConfigs = {
	'no-data': {
		icon: 'cl-icon-inbox',
		title: '暂无数据',
		description: '当前没有可显示的内容',
		actionText: '刷新',
		iconColor: '#d9d9d9'
	},
	'no-network': {
		icon: 'cl-icon-wifi-off',
		title: '网络连接失败',
		description: '请检查网络设置后重试',
		actionText: '重新连接',
		iconColor: '#ff7875'
	},
	'no-search': {
		icon: 'cl-icon-search',
		title: '无搜索结果',
		description: '试试其他关键词或筛选条件',
		actionText: '重新搜索',
		iconColor: '#faad14'
	},
	'no-permission': {
		icon: 'cl-icon-lock',
		title: '暂无权限',
		description: '您没有访问此内容的权限',
		actionText: '申请权限',
		iconColor: '#ff9c6e'
	},
	'error': {
		icon: 'cl-icon-alert-circle',
		title: '出错了',
		description: '页面加载失败，请稍后重试',
		actionText: '重新加载',
		iconColor: '#ff4d4f'
	},
	'maintenance': {
		icon: 'cl-icon-tool',
		title: '系统维护中',
		description: '系统正在维护升级，请稍后访问',
		actionText: '了解详情',
		iconColor: '#722ed1'
	},
	'custom': {
		icon: 'cl-icon-help-circle',
		title: '',
		description: '',
		actionText: '',
		iconColor: '#d9d9d9'
	}
};

// 尺寸配置
const sizeConfigs = {
	small: {
		iconSize: 48,
		actionSize: 'small' as const
	},
	medium: {
		iconSize: 64,
		actionSize: 'medium' as const
	},
	large: {
		iconSize: 80,
		actionSize: 'large' as const
	}
};

// 计算属性
const config = computed(() => typeConfigs[props.type]);

const iconName = computed(() => {
	return props.icon || config.value.icon;
});

const iconColor = computed(() => {
	return props.iconColor || config.value.iconColor;
});

const iconSize = computed(() => {
	return sizeConfigs[props.size].iconSize;
});

const actionSize = computed(() => {
	return sizeConfigs[props.size].actionSize;
});

const finalTitle = computed(() => {
	return props.title !== undefined ? props.title : config.value.title;
});

const finalDescription = computed(() => {
	return props.description !== undefined ? props.description : config.value.description;
});

const finalActionText = computed(() => {
	return props.actionText !== undefined ? props.actionText : config.value.actionText;
});

// 方法
const handleAction = () => {
	emit('action');
};

const handleSecondaryAction = () => {
	emit('secondaryAction');
};
</script>

<style scoped>
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 40rpx;
	text-align: center;
	min-height: 300rpx;
}

/* 尺寸变体 */
.size-small {
	padding: 40rpx 30rpx;
	min-height: 200rpx;
}

.size-large {
	padding: 80rpx 50rpx;
	min-height: 400rpx;
}

/* 图标容器 */
.empty-icon {
	margin-bottom: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.size-small .empty-icon {
	margin-bottom: 20rpx;
}

.size-large .empty-icon {
	margin-bottom: 32rpx;
}

/* 图片 */
.empty-image {
	max-width: 200rpx;
	max-height: 200rpx;
}

.size-small .empty-image {
	max-width: 150rpx;
	max-height: 150rpx;
}

.size-large .empty-image {
	max-width: 250rpx;
	max-height: 250rpx;
}

/* 标题 */
.empty-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin-bottom: 12rpx;
	line-height: 1.4;
}

.size-small .empty-title {
	font-size: 14px;
	margin-bottom: 10rpx;
}

.size-large .empty-title {
	font-size: 18px;
	margin-bottom: 16rpx;
}

/* 描述 */
.empty-description {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
	margin-bottom: 32rpx;
	max-width: 400rpx;
}

.size-small .empty-description {
	font-size: 12px;
	margin-bottom: 24rpx;
	max-width: 300rpx;
}

.size-large .empty-description {
	font-size: 16px;
	margin-bottom: 40rpx;
	max-width: 500rpx;
}

/* 操作按钮 */
.empty-actions {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
	align-items: center;
}

.size-small .empty-actions {
	gap: 12rpx;
}

.size-large .empty-actions {
	gap: 20rpx;
}

/* 自定义内容 */
.empty-custom {
	margin-top: 24rpx;
	width: 100%;
}

.size-small .empty-custom {
	margin-top: 20rpx;
}

.size-large .empty-custom {
	margin-top: 32rpx;
}

/* 类型特定样式 */
.type-no-data {
	/* 无数据状态特定样式 */
}

.type-no-network {
	/* 无网络状态特定样式 */
}

.type-no-search {
	/* 无搜索结果状态特定样式 */
}

.type-no-permission {
	/* 无权限状态特定样式 */
}

.type-error {
	/* 错误状态特定样式 */
}

.type-maintenance {
	/* 维护状态特定样式 */
}

.type-custom {
	/* 自定义状态特定样式 */
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.empty-state {
		padding: 40rpx 20rpx;
	}
	
	.empty-description {
		max-width: 90%;
	}
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
	.empty-title {
		color: #fff;
	}
	
	.empty-description {
		color: #ccc;
	}
}

/* 动画效果 */
.empty-icon {
	animation: fadeInUp 0.6s ease-out;
}

.empty-title {
	animation: fadeInUp 0.6s ease-out 0.1s both;
}

.empty-description {
	animation: fadeInUp 0.6s ease-out 0.2s both;
}

.empty-actions {
	animation: fadeInUp 0.6s ease-out 0.3s both;
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20rpx);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 无障碍访问 */
.empty-state {
	/* 确保有足够的对比度 */
	/* 支持屏幕阅读器 */
}

/* 交互状态 */
.empty-actions .cl-button:active {
	transform: scale(0.98);
}

/* 加载状态 */
.empty-state.loading .empty-icon {
	opacity: 0.6;
	animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
	0%, 100% {
		opacity: 0.6;
	}
	50% {
		opacity: 1;
	}
}
</style>