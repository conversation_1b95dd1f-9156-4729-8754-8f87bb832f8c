import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 系统管理API接口集合
 * @method config 系统配置相关API
 * @method dict 字典管理相关API
 * @method role 角色管理相关API
 * @method org 机构管理相关API
 * @method log 日志管理相关API
 * @method file 文件管理相关API
 */
export function useSystemApi() {
	return {
		// 系统配置
		config: {
			...useBaseApi('sysConfig'),
			// 批量更新配置
			batchUpdate: (data: any) => {
				return request({
					url: '/api/sysConfig/batchEdit',
					method: 'post',
					data,
				});
			},
			// 获取系统信息
			getSysInfo: () => {
				return request({
					url: '/api/sysConfig/sysInfo',
					method: 'get',
				});
			},
			// 保存系统信息
			saveSysInfo: (data: any) => {
				return request({
					url: '/api/sysConfig/saveSysInfo',
					method: 'post',
					data,
				});
			},
		},
		// 字典管理
		dict: {
			// 字典类型
			type: {
				...useBaseApi('sysDictType'),
				// 获取所有字典类型
				getAllList: () => {
					return request({
						url: '/api/sysDictType/allDictList',
						method: 'get',
					});
				},
			},
			// 字典数据
			data: {
				...useBaseApi('sysDictData'),
				// 根据字典类型获取数据
				getDataList: (dictType: string) => {
					return request({
						url: `/api/sysDictData/dataList/${dictType}`,
						method: 'get',
					});
				},
			},
			// 获取所有字典列表
			getAllDictList: () => {
				return request({
					url: '/api/sysDictType/allDictList',
					method: 'get',
				});
			},
		},
		// 角色管理
		role: {
			...useBaseApi('sysRole'),
			// 获取角色菜单
			getRoleMenus: (roleId: any) => {
				return request({
					url: '/api/sysRole/ownMenu',
					method: 'get',
					params: { roleId },
				});
			},
			// 设置角色菜单
			setRoleMenus: (data: any) => {
				return request({
					url: '/api/sysRole/grantMenu',
					method: 'post',
					data,
				});
			},
		},
		// 机构管理
		org: {
			...useBaseApi('sysOrg'),
			// 获取机构树
			getOrgTree: () => {
				return request({
					url: '/api/sysOrg/tree',
					method: 'get',
				});
			},
		},
		// 日志管理
		log: {
			// 操作日志
			op: useBaseApi('sysLogOp'),
			// 异常日志
			ex: useBaseApi('sysLogEx'),
			// 访问日志
			vis: useBaseApi('sysLogVis'),
			// 差异日志
			diff: useBaseApi('sysLogDiff'),
		},
		// 文件管理
		file: {
			...useBaseApi('sysFile'),
			// 上传文件
			uploadFile: (file: any) => {
				const formData = new FormData();
				formData.append('file', file);
				return request({
					url: '/api/sysFile/uploadFile',
					method: 'post',
					data: formData,
					headers: {
						'Content-Type': 'multipart/form-data',
					},
				});
			},
			// 上传头像
			uploadAvatar: (file: any) => {
				const formData = new FormData();
				formData.append('file', file);
				return request({
					url: '/api/sysFile/uploadAvatar',
					method: 'post',
					data: formData,
					headers: {
						'Content-Type': 'multipart/form-data',
					},
				});
			},
		},
		// 语言管理
		lang: {
			...useBaseApi('sysLang'),
			// 获取下拉数据
			getDropdownData: () => {
				return request({
					url: '/api/sysLang/dropdownData',
					method: 'post',
				});
			},
		},
		// 常量管理
		const: {
			// 获取所有常量列表
			getList: () => {
				return request({
					url: '/api/sysConst/list',
					method: 'get',
				});
			},
			// 根据类名获取常量数据
			getData: (typeName: string) => {
				return request({
					url: '/api/sysConst/data',
					method: 'get',
					params: { typeName },
				});
			},
		},
		// 用户菜单管理
		userMenu: {
			// 获取用户收藏菜单列表
			getUserMenuList: () => {
				return request({
					url: '/api/sysUserMenu/userMenuList',
					method: 'get',
				});
			},
			// 添加收藏菜单
			add: (data: any) => {
				return request({
					url: '/api/sysUserMenu/add',
					method: 'post',
					data,
				});
			},
			// 删除收藏菜单
			delete: (data: any) => {
				return request({
					url: '/api/sysUserMenu/deleteUserMenu',
					method: 'post',
					data,
				});
			},
		},
		// 日程管理
		schedule: {
			...useBaseApi('sysSchedule'),
			// 获取日程分页列表
			page: (data: any) => {
				return request({
					url: '/api/sysSchedule/page',
					method: 'post',
					data,
				});
			},
			// 获取日程详情
			getDetail: (id: number) => {
				return request({
					url: `/api/sysSchedule/detail/${id}`,
					method: 'get',
				});
			},
		},
		// 通知管理
		notice: {
			...useBaseApi('sysNotice'),
			// 获取接收的通知分页列表
			pageReceived: (data: any) => {
				return request({
					url: '/api/sysNotice/pageReceived',
					method: 'post',
					data,
				});
			},
			// 设置已读状态
			setRead: (data: any) => {
				return request({
					url: '/api/sysNotice/setRead',
					method: 'post',
					data,
				});
			},
			// 发布通知
			public: (data: any) => {
				return request({
					url: '/api/sysNotice/public',
					method: 'post',
					data,
				});
			},
			// 获取未读通知列表
			getUnReadList: () => {
				return request({
					url: '/api/sysNotice/unReadList',
					method: 'get',
				});
			},
		},
		// 租户管理
		tenant: {
			...useBaseApi('sysTenant'),
			// 获取租户列表
			getList: () => {
				return request({
					url: '/api/sysTenant/list',
					method: 'get',
				});
			},
			// 切换租户
			changeTenant: (data: any) => {
				return request({
					url: '/api/sysTenant/changeTenant',
					method: 'post',
					data,
				});
			},
			// 获取租户菜单
			getTenantMenuList: (tenantId: any) => {
				return request({
					url: '/api/sysTenant/tenantMenuList',
					method: 'get',
					params: { tenantId },
				});
			},
			// 授权菜单
			grantMenu: (data: any) => {
				return request({
					url: '/api/sysTenant/grantMenu',
					method: 'post',
					data,
				});
			},
			// 同步授权菜单
			syncGrantMenu: (data: any) => {
				return request({
					url: '/api/sysTenant/syncGrantMenu',
					method: 'post',
					data,
				});
			},
			// 创建数据库
			createDb: (data: any) => {
				return request({
					url: '/api/sysTenant/createDb',
					method: 'post',
					data,
				});
			},
			// 获取租户用户列表
			getUserList: (data: any) => {
				return request({
					url: '/api/sysTenant/userList',
					method: 'post',
					data,
				});
			},
		},
		// 能耗监控管理
		energyConsumption: {
			page: (data: any) => {
				return request({
					url: '/api/energyConsumption/page',
					method: 'post',
					data,
				});
			},
			getStat: (data: any) => {
				return request({
					url: '/api/energyConsumption/stat',
					method: 'post',
					data,
				});
			},
			getTrend: (data: any) => {
				return request({
					url: '/api/energyConsumption/trend',
					method: 'post',
					data,
				});
			},
			getRank: (data: any) => {
				return request({
					url: '/api/energyConsumption/rank',
					method: 'post',
					data,
				});
			},
			add: (data: any) => {
				return request({
					url: '/api/energyConsumption/add',
					method: 'post',
					data,
				});
			},
			update: (data: any) => {
				return request({
					url: '/api/energyConsumption/update',
					method: 'post',
					data,
				});
			},
			delete: (data: any) => {
				return request({
					url: '/api/energyConsumption/delete',
					method: 'post',
					data,
				});
			},
			getEnergySavingAnalysis: (data: any) => {
				return request({
					url: '/api/energyConsumption/energySavingAnalysis',
					method: 'post',
					data,
				});
			},
		},
	};
}