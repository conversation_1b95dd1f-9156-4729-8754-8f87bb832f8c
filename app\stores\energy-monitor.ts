import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { request } from '@/cool/service';

// 能耗数据类型定义
export interface EnergyData {
	id: string;
	deviceId: string;
	deviceName: string;
	timestamp: string;
	power: number; // 瞬时功率 W
	energy: number; // 累计能耗 kWh
	voltage?: number; // 电压 V
	current?: number; // 电流 A
	powerFactor?: number; // 功率因数
	frequency?: number; // 频率 Hz
	temperature?: number; // 温度 °C
	humidity?: number; // 湿度 %
	brightness?: number; // 亮度 %
	workingTime: number; // 工作时长 分钟
	location?: string;
	room?: string;
	floor?: string;
	building?: string;
}

// 能耗统计数据
export interface EnergyStats {
	timeRange: string;
	totalEnergy: number; // 总能耗 kWh
	totalCost: number; // 总费用 元
	averagepower: number; // 平均功率 W
	peakPower: number; // 峰值功率 W
	peakTime: string; // 峰值时间
	workingHours: number; // 总工作时长 小时
	deviceCount: number; // 设备数量
	efficiency: number; // 能效比 %
	carbonEmission: number; // 碳排放 kg
	byDevice: {
		deviceId: string;
		deviceName: string;
		energy: number;
		cost: number;
		percentage: number;
	}[];
	byTime: {
		time: string;
		energy: number;
		power: number;
		cost: number;
	}[];
	byLocation: {
		location: string;
		energy: number;
		cost: number;
		deviceCount: number;
		percentage: number;
	}[];
	trend: {
		date: string;
		energy: number;
		cost: number;
		change: number; // 变化率 %
	}[];
}

// 能耗报告数据
export interface EnergyReport {
	id: string;
	name: string;
	type: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom';
	startDate: string;
	endDate: string;
	createTime: string;
	status: 'generating' | 'completed' | 'failed';
	fileUrl?: string;
	fileSize?: number;
	summary: {
		totalEnergy: number;
		totalCost: number;
		deviceCount: number;
		workingHours: number;
		efficiency: number;
		carbonEmission: number;
		topDevices: {
			deviceId: string;
			deviceName: string;
			energy: number;
			percentage: number;
		}[];
		recommendations: string[];
	};
}

// 节能建议
export interface EnergySuggestion {
	id: string;
	type: 'device_optimization' | 'schedule_adjustment' | 'replacement_suggestion' | 'maintenance_reminder';
	title: string;
	description: string;
	deviceId?: string;
	deviceName?: string;
	estimatedSaving: number; // 预计节省 kWh/月
	estimatedCost: number; // 预计节省费用 元/月
	priority: 'low' | 'medium' | 'high';
	status: 'pending' | 'applied' | 'ignored';
	createTime: string;
	actions?: {
		id: string;
		name: string;
		description: string;
		type: 'auto' | 'manual';
	}[];
}

// 查询参数
export interface EnergyQueryParams {
	startDate: string;
	endDate: string;
	deviceIds?: string[];
	location?: string;
	room?: string;
	floor?: string;
	building?: string;
	granularity: 'minute' | 'hour' | 'day' | 'week' | 'month';
	metrics?: ('power' | 'energy' | 'cost' | 'efficiency')[];
	groupBy?: ('device' | 'location' | 'time')[];
}

// 实时监控数据
export interface RealTimeData {
	deviceId: string;
	deviceName: string;
	status: 'online' | 'offline' | 'fault';
	power: number;
	energy: number;
	brightness: number;
	temperature?: number;
	lastUpdate: string;
	alerts?: {
		type: 'high_power' | 'temperature' | 'efficiency';
		message: string;
		level: 'warning' | 'error';
	}[];
}

export const useEnergyMonitorStore = defineStore('energyMonitor', () => {
	// 状态数据
	const energyData = ref<EnergyData[]>([]);
	const energyStats = ref<EnergyStats | null>(null);
	const realTimeData = ref<RealTimeData[]>([]);
	const reports = ref<EnergyReport[]>([]);
	const suggestions = ref<EnergySuggestion[]>([]);
	const loading = ref(false);
	const realTimeLoading = ref(false);
	const queryParams = ref<EnergyQueryParams>({
		startDate: '',
		endDate: '',
		granularity: 'hour'
	});
	const selectedDevices = ref<string[]>([]);
	const selectedMetrics = ref<string[]>(['power', 'energy']);
	const chartType = ref<'line' | 'bar' | 'area'>('line');
	const timeRange = ref<'1h' | '6h' | '24h' | '7d' | '30d' | 'custom'>('24h');
	const autoRefresh = ref(false);
	const refreshInterval = ref(30000); // 30秒
	const refreshTimer = ref<NodeJS.Timeout | null>(null);

	// 计算属性
	const filteredEnergyData = computed(() => {
		let result = energyData.value;

		// 设备筛选
		if (selectedDevices.value.length > 0) {
			result = result.filter(data => 
				selectedDevices.value.includes(data.deviceId)
			);
		}

		// 时间范围筛选
		if (queryParams.value.startDate && queryParams.value.endDate) {
			const startTime = new Date(queryParams.value.startDate).getTime();
			const endTime = new Date(queryParams.value.endDate).getTime();
			result = result.filter(data => {
				const dataTime = new Date(data.timestamp).getTime();
				return dataTime >= startTime && dataTime <= endTime;
			});
		}

		return result;
	});

	const chartData = computed(() => {
		const data = filteredEnergyData.value;
		if (data.length === 0) return [];

		// 按时间分组
		const groupedData = new Map<string, EnergyData[]>();
		data.forEach(item => {
			const timeKey = formatTimeByGranularity(item.timestamp, queryParams.value.granularity);
			if (!groupedData.has(timeKey)) {
				groupedData.set(timeKey, []);
			}
			groupedData.get(timeKey)!.push(item);
		});

		// 转换为图表数据格式
		const chartData = Array.from(groupedData.entries()).map(([time, items]) => {
			const totalPower = items.reduce((sum, item) => sum + item.power, 0);
			const totalEnergy = items.reduce((sum, item) => sum + item.energy, 0);
			const avgBrightness = items.reduce((sum, item) => sum + (item.brightness || 0), 0) / items.length;
			const avgTemperature = items.reduce((sum, item) => sum + (item.temperature || 0), 0) / items.length;

			return {
				time,
				power: totalPower,
				energy: totalEnergy,
				cost: totalEnergy * 0.6, // 假设电价0.6元/kWh
				brightness: avgBrightness,
				temperature: avgTemperature,
				deviceCount: items.length
			};
		});

		return chartData.sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime());
	});

	const totalEnergyConsumption = computed(() => {
		return filteredEnergyData.value.reduce((sum, data) => sum + data.energy, 0);
	});

	const totalCost = computed(() => {
		return totalEnergyConsumption.value * 0.6; // 假设电价0.6元/kWh
	});

	const averagePower = computed(() => {
		const data = filteredEnergyData.value;
		if (data.length === 0) return 0;
		return data.reduce((sum, item) => sum + item.power, 0) / data.length;
	});

	const peakPower = computed(() => {
		const data = filteredEnergyData.value;
		if (data.length === 0) return { power: 0, time: '' };
		const peak = data.reduce((max, item) => 
			item.power > max.power ? item : max
		);
		return { power: peak.power, time: peak.timestamp };
	});

	const onlineDeviceCount = computed(() => {
		return realTimeData.value.filter(device => device.status === 'online').length;
	});

	const faultDeviceCount = computed(() => {
		return realTimeData.value.filter(device => device.status === 'fault').length;
	});

	const totalAlerts = computed(() => {
		return realTimeData.value.reduce((sum, device) => 
			sum + (device.alerts?.length || 0), 0
		);
	});

	const pendingSuggestions = computed(() => {
		return suggestions.value.filter(s => s.status === 'pending');
	});

	// Actions
	const fetchEnergyData = async (params?: Partial<EnergyQueryParams>) => {
		loading.value = true;
		try {
			const queryData = { ...queryParams.value, ...params };
			
			const res = await request({
				url: '/energy/monitor/data',
				method: 'GET',
				params: queryData
			});

			energyData.value = res.data.list || [];
			energyStats.value = res.data.stats || null;

			return res.data;
		} catch (error) {
			console.error('获取能耗数据失败:', error);
			throw error;
		} finally {
			loading.value = false;
		}
	};

	const fetchRealTimeData = async () => {
		realTimeLoading.value = true;
		try {
			const res = await request({
				url: '/energy/monitor/realtime',
				method: 'GET'
			});

			realTimeData.value = res.data || [];
			return res.data;
		} catch (error) {
			console.error('获取实时数据失败:', error);
			throw error;
		} finally {
			realTimeLoading.value = false;
		}
	};

	const fetchReports = async () => {
		try {
			const res = await request({
				url: '/energy/monitor/reports',
				method: 'GET'
			});

			reports.value = res.data || [];
			return res.data;
		} catch (error) {
			console.error('获取报告列表失败:', error);
			throw error;
		}
	};

	const generateReport = async (reportData: {
		name: string;
		type: EnergyReport['type'];
		startDate: string;
		endDate: string;
		deviceIds?: string[];
		location?: string;
	}) => {
		try {
			const res = await request({
				url: '/energy/monitor/reports',
				method: 'POST',
				data: reportData
			});

			reports.value.unshift(res.data);
			return res.data;
		} catch (error) {
			console.error('生成报告失败:', error);
			throw error;
		}
	};

	const downloadReport = async (reportId: string) => {
		try {
			const res = await request({
				url: `/energy/monitor/reports/${reportId}/download`,
				method: 'GET'
			});

			// 创建下载链接
			const blob = new Blob([res.data]);
			const url = window.URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.href = url;
			link.download = `energy-report-${reportId}.xlsx`;
			link.click();
			window.URL.revokeObjectURL(url);

			return res.data;
		} catch (error) {
			console.error('下载报告失败:', error);
			throw error;
		}
	};

	const fetchSuggestions = async () => {
		try {
			const res = await request({
				url: '/energy/monitor/suggestions',
				method: 'GET'
			});

			suggestions.value = res.data || [];
			return res.data;
		} catch (error) {
			console.error('获取节能建议失败:', error);
			throw error;
		}
	};

	const applySuggestion = async (suggestionId: string, actionId?: string) => {
		try {
			const res = await request({
				url: `/energy/monitor/suggestions/${suggestionId}/apply`,
				method: 'POST',
				data: { actionId }
			});

			// 更新建议状态
			const suggestion = suggestions.value.find(s => s.id === suggestionId);
			if (suggestion) {
				suggestion.status = 'applied';
			}

			return res.data;
		} catch (error) {
			console.error('应用建议失败:', error);
			throw error;
		}
	};

	const ignoreSuggestion = async (suggestionId: string) => {
		try {
			await request({
				url: `/energy/monitor/suggestions/${suggestionId}/ignore`,
				method: 'POST'
			});

			// 更新建议状态
			const suggestion = suggestions.value.find(s => s.id === suggestionId);
			if (suggestion) {
				suggestion.status = 'ignored';
			}
		} catch (error) {
			console.error('忽略建议失败:', error);
			throw error;
		}
	};

	// 工具方法
	const setTimeRange = (range: typeof timeRange.value) => {
		timeRange.value = range;
		
		const now = new Date();
		let startDate: Date;

		switch (range) {
			case '1h':
				startDate = new Date(now.getTime() - 60 * 60 * 1000);
				queryParams.value.granularity = 'minute';
				break;
			case '6h':
				startDate = new Date(now.getTime() - 6 * 60 * 60 * 1000);
				queryParams.value.granularity = 'minute';
				break;
			case '24h':
				startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
				queryParams.value.granularity = 'hour';
				break;
			case '7d':
				startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
				queryParams.value.granularity = 'hour';
				break;
			case '30d':
				startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
				queryParams.value.granularity = 'day';
				break;
			default:
				return; // custom 不自动设置时间
		}

		queryParams.value.startDate = startDate.toISOString();
		queryParams.value.endDate = now.toISOString();
	};

	const setCustomTimeRange = (startDate: string, endDate: string) => {
		timeRange.value = 'custom';
		queryParams.value.startDate = startDate;
		queryParams.value.endDate = endDate;

		// 根据时间范围自动设置粒度
		const start = new Date(startDate).getTime();
		const end = new Date(endDate).getTime();
		const diffHours = (end - start) / (1000 * 60 * 60);

		if (diffHours <= 6) {
			queryParams.value.granularity = 'minute';
		} else if (diffHours <= 48) {
			queryParams.value.granularity = 'hour';
		} else if (diffHours <= 24 * 30) {
			queryParams.value.granularity = 'day';
		} else {
			queryParams.value.granularity = 'week';
		}
	};

	const startAutoRefresh = () => {
		if (refreshTimer.value) {
			clearInterval(refreshTimer.value);
		}

		autoRefresh.value = true;
		refreshTimer.value = setInterval(() => {
			fetchRealTimeData();
		}, refreshInterval.value);
	};

	const stopAutoRefresh = () => {
		if (refreshTimer.value) {
			clearInterval(refreshTimer.value);
			refreshTimer.value = null;
		}
		autoRefresh.value = false;
	};

	const exportChartData = () => {
		const data = chartData.value;
		if (data.length === 0) return;

		// 转换为CSV格式
		const headers = ['时间', '功率(W)', '能耗(kWh)', '费用(元)'];
		const csvContent = [
			headers.join(','),
			...data.map(item => [
				item.time,
				item.power.toFixed(2),
				item.energy.toFixed(3),
				item.cost.toFixed(2)
			].join(','))
		].join('\n');

		// 创建下载链接
		const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
		const url = window.URL.createObjectURL(blob);
		const link = document.createElement('a');
		link.href = url;
		link.download = `energy-data-${new Date().toISOString().split('T')[0]}.csv`;
		link.click();
		window.URL.revokeObjectURL(url);
	};

	// 辅助函数
	const formatTimeByGranularity = (timestamp: string, granularity: string): string => {
		const date = new Date(timestamp);
		
		switch (granularity) {
			case 'minute':
				return date.toISOString().slice(0, 16); // YYYY-MM-DDTHH:mm
			case 'hour':
				return date.toISOString().slice(0, 13) + ':00'; // YYYY-MM-DDTHH:00
			case 'day':
				return date.toISOString().slice(0, 10); // YYYY-MM-DD
			case 'week':
				const weekStart = new Date(date);
				weekStart.setDate(date.getDate() - date.getDay());
				return weekStart.toISOString().slice(0, 10);
			case 'month':
				return date.toISOString().slice(0, 7); // YYYY-MM
			default:
				return timestamp;
		}
	};

	return {
		// 状态
		energyData,
		energyStats,
		realTimeData,
		reports,
		suggestions,
		loading,
		realTimeLoading,
		queryParams,
		selectedDevices,
		selectedMetrics,
		chartType,
		timeRange,
		autoRefresh,
		refreshInterval,

		// 计算属性
		filteredEnergyData,
		chartData,
		totalEnergyConsumption,
		totalCost,
		averagePower,
		peakPower,
		onlineDeviceCount,
		faultDeviceCount,
		totalAlerts,
		pendingSuggestions,

		// 方法
		fetchEnergyData,
		fetchRealTimeData,
		fetchReports,
		generateReport,
		downloadReport,
		fetchSuggestions,
		applySuggestion,
		ignoreSuggestion,
		setTimeRange,
		setCustomTimeRange,
		startAutoRefresh,
		stopAutoRefresh,
		exportChartData
	};
});