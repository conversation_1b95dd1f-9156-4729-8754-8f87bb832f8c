// API服务层
import httpClient from './request'
import type { 
  Device, 
  DeviceStats, 
  DeviceControlParams,
  EnergyData, 
  EnergyOverview, 
  EnergyStats,
  UserInfo,
  PageParams,
  PageResult,
  DictData,
  Notification
} from '@/types'

// 用户相关API
export const userApi = {
  // 登录
  login(username: string, password: string): Promise<{ token: string; userInfo: UserInfo }> {
    return httpClient.post('/api/user/login', { username, password })
  },

  // 退出登录
  logout(): Promise<void> {
    return httpClient.post('/api/user/logout')
  },

  // 获取用户信息
  getUserInfo(): Promise<UserInfo> {
    return httpClient.get('/api/user/info')
  },

  // 更新用户信息
  updateUserInfo(userInfo: Partial<UserInfo>): Promise<UserInfo> {
    return httpClient.put('/api/user/info', userInfo)
  },

  // 修改密码
  changePassword(oldPassword: string, newPassword: string): Promise<void> {
    return httpClient.post('/api/user/change-password', { oldPassword, newPassword })
  }
}

// 设备相关API
export const deviceApi = {
  // 获取设备列表
  getDevices(params: PageParams & { keyword?: string }): Promise<PageResult<Device>> {
    return httpClient.get('/api/devices', params)
  },

  // 获取设备详情
  getDeviceDetail(deviceId: string): Promise<Device> {
    return httpClient.get(`/api/devices/${deviceId}`)
  },

  // 获取设备统计
  getDeviceStats(): Promise<DeviceStats> {
    return httpClient.get('/api/devices/stats')
  },

  // 获取设备状态
  getDeviceStatus(deviceId: string): Promise<any> {
    return httpClient.get(`/api/devices/${deviceId}/status`)
  },

  // 控制设备
  controlDevice(deviceId: string, params: DeviceControlParams): Promise<void> {
    return httpClient.post(`/api/devices/${deviceId}/control`, params)
  },

  // 批量控制设备
  batchControlDevices(deviceIds: string[], params: DeviceControlParams): Promise<void> {
    return httpClient.post('/api/devices/batch-control', { deviceIds, ...params })
  },

  // 添加设备
  addDevice(device: Partial<Device>): Promise<Device> {
    return httpClient.post('/api/devices', device)
  },

  // 更新设备
  updateDevice(deviceId: string, device: Partial<Device>): Promise<Device> {
    return httpClient.put(`/api/devices/${deviceId}`, device)
  },

  // 删除设备
  deleteDevice(deviceId: string): Promise<void> {
    return httpClient.delete(`/api/devices/${deviceId}`)
  }
}

// 能耗相关API
export const energyApi = {
  // 获取今日能耗
  getTodayEnergy(): Promise<number> {
    return httpClient.get('/api/energy/today')
  },

  // 获取能耗概览
  getEnergyOverview(deviceId?: string, dateRange?: string): Promise<EnergyOverview> {
    return httpClient.get('/api/energy/overview', { deviceId, dateRange })
  },

  // 获取能耗趋势
  getEnergyTrend(params: { deviceId?: string; dateRange?: string; days?: number }): Promise<EnergyData[]> {
    return httpClient.get('/api/energy/trend', params)
  },

  // 获取能耗明细
  getEnergyDetails(params: PageParams & { deviceId?: string; dateRange?: string }): Promise<PageResult<EnergyData>> {
    return httpClient.get('/api/energy/details', params)
  },

  // 获取能耗统计
  getEnergyStats(params: { deviceId?: string; dateRange?: string }): Promise<EnergyStats> {
    return httpClient.get('/api/energy/stats', params)
  },

  // 生成能耗报表
  generateEnergyReport(params: { deviceIds?: string[]; dateRange: string; format?: 'pdf' | 'excel' }): Promise<string> {
    return httpClient.post('/api/energy/report', params)
  },

  // 导出能耗数据
  exportEnergyData(params: { deviceId?: string; dateRange: string }): Promise<string> {
    return httpClient.get('/api/energy/export', params)
  }
}

// 字典相关API
export const dictApi = {
  // 获取所有字典数据
  getAllDictData(): Promise<DictData> {
    return httpClient.get('/api/dict/all')
  },

  // 根据类型获取字典数据
  getDictDataByType(type: string): Promise<any[]> {
    return httpClient.get(`/api/dict/${type}`)
  },

  // 刷新字典缓存
  refreshDictCache(): Promise<void> {
    return httpClient.post('/api/dict/refresh')
  }
}

// 通知相关API
export const notificationApi = {
  // 获取通知列表
  getNotifications(params: PageParams): Promise<PageResult<Notification>> {
    return httpClient.get('/api/notifications', params)
  },

  // 标记通知为已读
  markAsRead(notificationId: string): Promise<void> {
    return httpClient.post(`/api/notifications/${notificationId}/read`)
  },

  // 批量标记为已读
  batchMarkAsRead(notificationIds: string[]): Promise<void> {
    return httpClient.post('/api/notifications/batch-read', { notificationIds })
  },

  // 删除通知
  deleteNotification(notificationId: string): Promise<void> {
    return httpClient.delete(`/api/notifications/${notificationId}`)
  },

  // 获取未读通知数量
  getUnreadCount(): Promise<number> {
    return httpClient.get('/api/notifications/unread-count')
  }
}

// 系统相关API
export const systemApi = {
  // 获取系统信息
  getSystemInfo(): Promise<any> {
    return httpClient.get('/api/system/info')
  },

  // 检查更新
  checkUpdate(): Promise<{ hasUpdate: boolean; version?: string; downloadUrl?: string }> {
    return httpClient.get('/api/system/check-update')
  },

  // 上传日志
  uploadLog(logData: any): Promise<void> {
    return httpClient.post('/api/system/upload-log', logData)
  },

  // 反馈问题
  submitFeedback(feedback: { type: string; content: string; contact?: string }): Promise<void> {
    return httpClient.post('/api/system/feedback', feedback)
  }
}

// 文件相关API
export const fileApi = {
  // 上传图片
  uploadImage(filePath: string): Promise<{ url: string; filename: string }> {
    return httpClient.upload('/api/file/upload/image', filePath)
  },

  // 上传文件
  uploadFile(filePath: string, type?: string): Promise<{ url: string; filename: string }> {
    return httpClient.upload('/api/file/upload', filePath, { type })
  },

  // 下载文件
  downloadFile(url: string): Promise<string> {
    return httpClient.download(url)
  }
}

// 统计相关API
export const statisticsApi = {
  // 获取首页统计数据
  getHomeStatistics(): Promise<{
    deviceStats: DeviceStats
    todayEnergy: number
    energyTrend: EnergyData[]
    recentAlerts: Notification[]
  }> {
    return httpClient.get('/api/statistics/home')
  },

  // 获取设备使用统计
  getDeviceUsageStats(params: { dateRange: string; deviceIds?: string[] }): Promise<any> {
    return httpClient.get('/api/statistics/device-usage', params)
  },

  // 获取能耗分析数据
  getEnergyAnalysis(params: { dateRange: string; groupBy?: 'device' | 'location' | 'time' }): Promise<any> {
    return httpClient.get('/api/statistics/energy-analysis', params)
  }
}

// 导出所有API
export default {
  user: userApi,
  device: deviceApi,
  energy: energyApi,
  dict: dictApi,
  notification: notificationApi,
  system: systemApi,
  file: fileApi,
  statistics: statisticsApi
}
