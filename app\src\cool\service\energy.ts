/**
 * 节能灯管理系统 - API服务层
 * Energy Light Management System - API Service Layer
 */

import { request } from "./index";
import type {
	// 设备管理相关类型
	DeviceInfo,
	DeviceControlRequest,
	BatchDeviceControlRequest,
	DeviceListParams,
	DeviceListResponse,
	// 能耗监控相关类型
	EnergyData,
	EnergyStats,
	EnergyQueryParams,
	SavingAnalysis,
	// 照明控制相关类型
	LightingScene,
	TimerTask,
	// 故障管理相关类型
	FaultInfo,
	FaultHandleRequest,
	MaintenanceRecord,
	// 通用类型
	ApiResponse,
	PageResponse,
	PageParams
} from "@/types/energy";

/**
 * 节能灯业务API服务
 */
export const energy = {
	// ==================== 设备管理接口 ====================

	/**
	 * 设备管理相关接口
	 */
	device: {
		/**
		 * 获取设备列表（分页）
		 * @param params 查询参数
		 * @returns 设备列表响应
		 */
		page(params: DeviceListParams): Promise<DeviceListResponse> {
			return request<DeviceListResponse>({
				url: "/api/energy/device/page",
				method: "GET",
				data: params
			});
		},

		/**
		 * 获取所有设备列表（不分页）
		 * @param params 查询参数
		 * @returns 设备列表
		 */
		list(params?: Partial<DeviceListParams>): Promise<DeviceInfo[]> {
			return request<DeviceInfo[]>({
				url: "/api/energy/device/list",
				method: "GET",
				data: params
			});
		},

		/**
		 * 获取设备详情
		 * @param deviceId 设备ID
		 * @returns 设备详情
		 */
		detail(deviceId: number): Promise<DeviceInfo> {
			return request<DeviceInfo>({
				url: `/api/energy/device/detail/${deviceId}`,
				method: "GET"
			});
		},

		/**
		 * 添加设备
		 * @param device 设备信息
		 * @returns 添加结果
		 */
		add(device: Partial<DeviceInfo>): Promise<{ id: number }> {
			return request<{ id: number }>({
				url: "/api/energy/device/add",
				method: "POST",
				data: device
			});
		},

		/**
		 * 更新设备信息
		 * @param device 设备信息
		 * @returns 更新结果
		 */
		update(device: Partial<DeviceInfo> & { id: number }): Promise<boolean> {
			return request<boolean>({
				url: "/api/energy/device/update",
				method: "PUT",
				data: device
			});
		},

		/**
		 * 删除设备
		 * @param deviceId 设备ID
		 * @returns 删除结果
		 */
		delete(deviceId: number): Promise<boolean> {
			return request<boolean>({
				url: `/api/energy/device/delete/${deviceId}`,
				method: "DELETE"
			});
		},

		/**
		 * 批量删除设备
		 * @param deviceIds 设备ID列表
		 * @returns 删除结果
		 */
		batchDelete(deviceIds: number[]): Promise<boolean> {
			return request<boolean>({
				url: "/api/energy/device/batch-delete",
				method: "DELETE",
				data: { deviceIds }
			});
		},

		/**
		 * 单个设备控制
		 * @param controlRequest 控制请求
		 * @returns 控制结果
		 */
		control(controlRequest: DeviceControlRequest): Promise<boolean> {
			return request<boolean>({
				url: "/api/energy/device/control",
				method: "POST",
				data: controlRequest
			});
		},

		/**
		 * 批量设备控制
		 * @param batchRequest 批量控制请求
		 * @returns 控制结果
		 */
		batchControl(batchRequest: BatchDeviceControlRequest): Promise<boolean> {
			return request<boolean>({
				url: "/api/energy/device/batch-control",
				method: "POST",
				data: batchRequest
			});
		},

		/**
		 * 获取设备状态统计
		 * @returns 状态统计
		 */
		stats(): Promise<{
			total: number;
			online: number;
			offline: number;
			fault: number;
			maintenance: number;
		}> {
			return request({
				url: "/api/energy/device/stats",
				method: "GET"
			});
		},

		/**
		 * 设备重启
		 * @param deviceId 设备ID
		 * @returns 重启结果
		 */
		restart(deviceId: number): Promise<boolean> {
			return request<boolean>({
				url: `/api/energy/device/restart/${deviceId}`,
				method: "POST"
			});
		},

		/**
		 * 设备固件升级
		 * @param deviceId 设备ID
		 * @param firmwareUrl 固件下载地址
		 * @returns 升级结果
		 */
		upgrade(deviceId: number, firmwareUrl: string): Promise<boolean> {
			return request<boolean>({
				url: `/api/energy/device/upgrade/${deviceId}`,
				method: "POST",
				data: { firmwareUrl }
			});
		}
	},

	// ==================== 能耗监控接口 ====================

	/**
	 * 能耗监控相关接口
	 */
	energy: {
		/**
		 * 获取实时能耗数据
		 * @param deviceId 设备ID（可选，不传则获取所有设备）
		 * @returns 实时能耗数据
		 */
		realtime(deviceId?: number): Promise<EnergyData[]> {
			return request<EnergyData[]>({
				url: "/api/energy/realtime",
				method: "GET",
				data: deviceId ? { deviceId } : {}
			});
		},

		/**
		 * 获取历史能耗数据
		 * @param params 查询参数
		 * @returns 历史能耗数据
		 */
		history(params: EnergyQueryParams): Promise<EnergyData[]> {
			return request<EnergyData[]>({
				url: "/api/energy/history",
				method: "GET",
				data: params
			});
		},

		/**
		 * 获取能耗统计数据
		 * @param params 查询参数
		 * @returns 统计数据
		 */
		stats(params: {
			deviceId?: number;
			period: "today" | "week" | "month" | "year";
			startTime?: string;
			endTime?: string;
		}): Promise<EnergyStats> {
			return request<EnergyStats>({
				url: "/api/energy/stats",
				method: "GET",
				data: params
			});
		},

		/**
		 * 获取节能分析报告
		 * @param params 分析参数
		 * @returns 节能分析结果
		 */
		analysis(params: {
			deviceId?: number;
			period: "week" | "month" | "quarter" | "year";
		}): Promise<SavingAnalysis> {
			return request<SavingAnalysis>({
				url: "/api/energy/analysis",
				method: "GET",
				data: params
			});
		},

		/**
		 * 导出能耗报表
		 * @param params 导出参数
		 * @returns 导出文件URL
		 */
		export(params: EnergyQueryParams & {
			format: "excel" | "pdf" | "csv";
		}): Promise<{ fileUrl: string }> {
			return request<{ fileUrl: string }>({
				url: "/api/energy/export",
				method: "POST",
				data: params
			});
		},

		/**
		 * 获取能耗排行榜
		 * @param params 查询参数
		 * @returns 排行榜数据
		 */
		ranking(params: {
			period: "today" | "week" | "month";
			limit?: number;
			orderBy: "consumption" | "power" | "cost";
		}): Promise<Array<{
			deviceId: number;
			deviceName: string;
			value: number;
			rank: number;
		}>> {
			return request({
				url: "/api/energy/ranking",
				method: "GET",
				data: params
			});
		}
	},

	// ==================== 照明控制接口 ====================

	/**
	 * 照明控制相关接口
	 */
	lighting: {
		/**
		 * 获取照明场景列表
		 * @returns 场景列表
		 */
		scenes(): Promise<LightingScene[]> {
			return request<LightingScene[]>({
				url: "/api/energy/lighting/scenes",
				method: "GET"
			});
		},

		/**
		 * 创建照明场景
		 * @param scene 场景信息
		 * @returns 创建结果
		 */
		createScene(scene: Omit<LightingScene, "id" | "createTime">): Promise<{ id: number }> {
			return request<{ id: number }>({
				url: "/api/energy/lighting/scene",
				method: "POST",
				data: scene
			});
		},

		/**
		 * 更新照明场景
		 * @param scene 场景信息
		 * @returns 更新结果
		 */
		updateScene(scene: LightingScene): Promise<boolean> {
			return request<boolean>({
				url: "/api/energy/lighting/scene",
				method: "PUT",
				data: scene
			});
		},

		/**
		 * 删除照明场景
		 * @param sceneId 场景ID
		 * @returns 删除结果
		 */
		deleteScene(sceneId: number): Promise<boolean> {
			return request<boolean>({
				url: `/api/energy/lighting/scene/${sceneId}`,
				method: "DELETE"
			});
		},

		/**
		 * 应用照明场景
		 * @param sceneId 场景ID
		 * @param deviceIds 设备ID列表（可选，不传则应用到所有设备）
		 * @returns 应用结果
		 */
		applyScene(sceneId: number, deviceIds?: number[]): Promise<boolean> {
			return request<boolean>({
				url: `/api/energy/lighting/apply-scene/${sceneId}`,
				method: "POST",
				data: deviceIds ? { deviceIds } : {}
			});
		},

		/**
		 * 获取定时任务列表
		 * @param params 查询参数
		 * @returns 定时任务列表
		 */
		timers(params?: PageParams & { deviceId?: number }): Promise<PageResponse<TimerTask>> {
			return request<PageResponse<TimerTask>>({
				url: "/api/energy/lighting/timers",
				method: "GET",
				data: params
			});
		},

		/**
		 * 创建定时任务
		 * @param timer 定时任务信息
		 * @returns 创建结果
		 */
		createTimer(timer: Omit<TimerTask, "id" | "createTime">): Promise<{ id: number }> {
			return request<{ id: number }>({
				url: "/api/energy/lighting/timer",
				method: "POST",
				data: timer
			});
		},

		/**
		 * 更新定时任务
		 * @param timer 定时任务信息
		 * @returns 更新结果
		 */
		updateTimer(timer: TimerTask): Promise<boolean> {
			return request<boolean>({
				url: "/api/energy/lighting/timer",
				method: "PUT",
				data: timer
			});
		},

		/**
		 * 删除定时任务
		 * @param timerId 任务ID
		 * @returns 删除结果
		 */
		deleteTimer(timerId: number): Promise<boolean> {
			return request<boolean>({
				url: `/api/energy/lighting/timer/${timerId}`,
				method: "DELETE"
			});
		},

		/**
		 * 启用/禁用定时任务
		 * @param timerId 任务ID
		 * @param enabled 是否启用
		 * @returns 操作结果
		 */
		toggleTimer(timerId: number, enabled: boolean): Promise<boolean> {
			return request<boolean>({
				url: `/api/energy/lighting/timer/${timerId}/toggle`,
				method: "PUT",
				data: { enabled }
			});
		}
	},

	// ==================== 故障管理接口 ====================

	/**
	 * 故障管理相关接口
	 */
	fault: {
		/**
		 * 获取故障列表（分页）
		 * @param params 查询参数
		 * @returns 故障列表
		 */
		page(params?: PageParams & {
			deviceId?: number;
			faultType?: string;
			faultLevel?: string;
			status?: string;
			startTime?: string;
			endTime?: string;
		}): Promise<PageResponse<FaultInfo>> {
			return request<PageResponse<FaultInfo>>({
				url: "/api/energy/fault/page",
				method: "GET",
				data: params
			});
		},

		/**
		 * 获取故障详情
		 * @param faultId 故障ID
		 * @returns 故障详情
		 */
		detail(faultId: number): Promise<FaultInfo> {
			return request<FaultInfo>({
				url: `/api/energy/fault/detail/${faultId}`,
				method: "GET"
			});
		},

		/**
		 * 处理故障
		 * @param handleRequest 处理请求
		 * @returns 处理结果
		 */
		handle(handleRequest: FaultHandleRequest): Promise<boolean> {
			return request<boolean>({
				url: "/api/energy/fault/handle",
				method: "POST",
				data: handleRequest
			});
		},

		/**
		 * 获取故障统计
		 * @param params 统计参数
		 * @returns 统计结果
		 */
		stats(params?: {
			period: "today" | "week" | "month" | "year";
			deviceId?: number;
		}): Promise<{
			total: number;
			pending: number;
			handling: number;
			resolved: number;
			closed: number;
			byType: Record<string, number>;
			byLevel: Record<string, number>;
		}> {
			return request({
				url: "/api/energy/fault/stats",
				method: "GET",
				data: params
			});
		},

		/**
		 * 创建故障报告
		 * @param fault 故障信息
		 * @returns 创建结果
		 */
		report(fault: Omit<FaultInfo, "id" | "occurTime" | "status">): Promise<{ id: number }> {
			return request<{ id: number }>({
				url: "/api/energy/fault/report",
				method: "POST",
				data: fault
			});
		}
	},

	// ==================== 维护管理接口 ====================

	/**
	 * 维护管理相关接口
	 */
	maintenance: {
		/**
		 * 获取维护记录列表（分页）
		 * @param params 查询参数
		 * @returns 维护记录列表
		 */
		page(params?: PageParams & {
			deviceId?: number;
			maintenanceType?: string;
			maintainer?: string;
			startTime?: string;
			endTime?: string;
		}): Promise<PageResponse<MaintenanceRecord>> {
			return request<PageResponse<MaintenanceRecord>>({
				url: "/api/energy/maintenance/page",
				method: "GET",
				data: params
			});
		},

		/**
		 * 创建维护记录
		 * @param record 维护记录
		 * @returns 创建结果
		 */
		create(record: Omit<MaintenanceRecord, "id" | "maintenanceTime">): Promise<{ id: number }> {
			return request<{ id: number }>({
				url: "/api/energy/maintenance/create",
				method: "POST",
				data: record
			});
		},

		/**
		 * 获取维护计划
		 * @param params 查询参数
		 * @returns 维护计划列表
		 */
		plan(params?: {
			deviceId?: number;
			startTime: string;
			endTime: string;
		}): Promise<Array<{
			deviceId: number;
			deviceName: string;
			nextMaintenanceTime: string;
			maintenanceType: string;
			priority: "low" | "medium" | "high";
		}>> {
			return request({
				url: "/api/energy/maintenance/plan",
				method: "GET",
				data: params
			});
		},

		/**
		 * 获取维护统计
		 * @param params 统计参数
		 * @returns 统计结果
		 */
		stats(params?: {
			period: "month" | "quarter" | "year";
		}): Promise<{
			total: number;
			byType: Record<string, number>;
			byResult: Record<string, number>;
			totalCost: number;
			averageCost: number;
		}> {
			return request({
				url: "/api/energy/maintenance/stats",
				method: "GET",
				data: params
			});
		}
	},

	// ==================== 系统管理接口 ====================

	/**
	 * 系统管理相关接口
	 */
	system: {
		/**
		 * 获取系统概览数据
		 * @returns 系统概览
		 */
	overview(): Promise<{
			deviceStats: {
				total: number;
				online: number;
				offline: number;
				fault: number;
			};
			energyStats: {
				todayConsumption: number;
				monthConsumption: number;
				todayCost: number;
				monthCost: number;
			};
			faultStats: {
				todayFaults: number;
				pendingFaults: number;
				resolvedRate: number;
			};
		}> {
			return request({
				url: "/api/energy/system/overview",
				method: "GET"
			});
		},

		/**
		 * 获取系统配置
		 * @returns 系统配置
		 */
		config(): Promise<{
			electricityPrice: number;
			carbonFactor: number;
			alertThresholds: {
				powerThreshold: number;
				temperatureThreshold: number;
				voltageThreshold: { min: number; max: number };
			};
		}> {
			return request({
				url: "/api/energy/system/config",
				method: "GET"
			});
		},

		/**
		 * 更新系统配置
		 * @param config 配置信息
		 * @returns 更新结果
		 */
		updateConfig(config: any): Promise<boolean> {
			return request<boolean>({
				url: "/api/energy/system/config",
				method: "PUT",
				data: config
			});
		},

		/**
		 * 获取操作日志
		 * @param params 查询参数
		 * @returns 操作日志列表
		 */
		logs(params?: PageParams & {
			operator?: string;
			action?: string;
			startTime?: string;
			endTime?: string;
		}): Promise<PageResponse<{
			id: number;
			operator: string;
			action: string;
			target: string;
			detail: string;
			operateTime: string;
			ip: string;
		}>> {
			return request({
				url: "/api/energy/system/logs",
				method: "GET",
				data: params
			});
		}
	}
};