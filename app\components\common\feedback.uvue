<template>
	<!-- Toast 提示 -->
	<cl-toast ref="toastRef" />
	
	<!-- 加载状态 -->
	<cl-popup v-model="showLoading" :mask-closable="false" :show-close="false">
		<view class="loading-container">
			<cl-loading :size="40" color="#007AFF" />
			<text class="loading-text">{{ loadingText }}</text>
		</view>
	</cl-popup>
	
	<!-- 确认对话框 -->
	<cl-popup v-model="showConfirm" :mask-closable="false">
		<view class="confirm-container">
			<view class="confirm-header">
				<cl-icon :name="confirmIcon" :size="48" :color="confirmIconColor" />
			</view>
			<view class="confirm-content">
				<text class="confirm-title">{{ confirmTitle }}</text>
				<text class="confirm-message">{{ confirmMessage }}</text>
			</view>
			<view class="confirm-actions">
				<cl-button 
					type="info" 
					size="large" 
					plain 
					@tap="handleCancel"
					class="confirm-btn"
				>
					{{ cancelText }}
				</cl-button>
				<cl-button 
					:type="confirmType" 
					size="large" 
					@tap="handleConfirm"
					class="confirm-btn"
				>
					{{ confirmText }}
				</cl-button>
			</view>
		</view>
	</cl-popup>
	
	<!-- 骨架屏 -->
	<view v-if="showSkeleton" class="skeleton-container">
		<view v-for="item in skeletonRows" :key="item" class="skeleton-item">
			<view class="skeleton-avatar"></view>
			<view class="skeleton-content">
				<view class="skeleton-line skeleton-line-title"></view>
				<view class="skeleton-line skeleton-line-subtitle"></view>
			</view>
		</view>
	</view>
	
	<!-- 空状态 -->
	<view v-if="showEmpty" class="empty-container">
		<cl-icon :name="emptyIcon" :size="80" color="#C8C9CC" />
		<text class="empty-title">{{ emptyTitle }}</text>
		<text class="empty-description">{{ emptyDescription }}</text>
		<cl-button 
			v-if="showEmptyAction" 
			type="primary" 
			size="small" 
			@tap="handleEmptyAction"
			class="empty-action"
		>
			{{ emptyActionText }}
		</cl-button>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Props
interface Props {
	// Toast 相关
	toastMessage?: string
	toastType?: 'success' | 'error' | 'warning' | 'info'
	
	// 加载状态
	showLoading?: boolean
	loadingText?: string
	
	// 确认对话框
	showConfirm?: boolean
	confirmTitle?: string
	confirmMessage?: string
	confirmText?: string
	cancelText?: string
	confirmType?: 'primary' | 'success' | 'warning' | 'error'
	
	// 骨架屏
	showSkeleton?: boolean
	skeletonRows?: number
	
	// 空状态
	showEmpty?: boolean
	emptyIcon?: string
	emptyTitle?: string
	emptyDescription?: string
	showEmptyAction?: boolean
	emptyActionText?: string
}

const props = withDefaults(defineProps<Props>(), {
	toastType: 'info',
	loadingText: '加载中...',
	confirmTitle: '提示',
	confirmMessage: '确定要执行此操作吗？',
	confirmText: '确定',
	cancelText: '取消',
	confirmType: 'primary',
	skeletonRows: 3,
	emptyIcon: 'cl-icon-empty',
	emptyTitle: '暂无数据',
	emptyDescription: '暂时没有相关数据',
	emptyActionText: '重新加载'
})

// Emits
const emit = defineEmits<{
	confirm: []
	cancel: []
	emptyAction: []
}>()

// Refs
const toastRef = ref()

// Computed
const confirmIcon = computed(() => {
	switch (props.confirmType) {
		case 'success':
			return 'cl-icon-check-circle'
		case 'warning':
			return 'cl-icon-warning'
		case 'error':
			return 'cl-icon-close-circle'
		default:
			return 'cl-icon-info-circle'
	}
})

const confirmIconColor = computed(() => {
	switch (props.confirmType) {
		case 'success':
			return '#52C41A'
		case 'warning':
			return '#FAAD14'
		case 'error':
			return '#FF4D4F'
		default:
			return '#1890FF'
	}
})

// Methods
const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
	toastRef.value?.open({
		message,
		type
	})
}

const handleConfirm = () => {
	emit('confirm')
}

const handleCancel = () => {
	emit('cancel')
}

const handleEmptyAction = () => {
	emit('emptyAction')
}

// 暴露方法
defineExpose({
	showToast
})
</script>

<style scoped>
/* 加载状态样式 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx;
	background: #fff;
	border-radius: 16rpx;
}

.loading-text {
	margin-top: 20rpx;
	font-size: 28rpx;
	color: #666;
}

/* 确认对话框样式 */
.confirm-container {
	background: #fff;
	border-radius: 16rpx;
	padding: 40rpx;
	min-width: 560rpx;
}

.confirm-header {
	display: flex;
	justify-content: center;
	margin-bottom: 24rpx;
}

.confirm-content {
	text-align: center;
	margin-bottom: 40rpx;
}

.confirm-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}

.confirm-message {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	display: block;
}

.confirm-actions {
	display: flex;
	gap: 20rpx;
}

.confirm-btn {
	flex: 1;
}

/* 骨架屏样式 */
.skeleton-container {
	padding: 20rpx;
}

.skeleton-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.skeleton-item:last-child {
	border-bottom: none;
}

.skeleton-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(90deg, #f0f0f0 25%, #e6e6e6 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	margin-right: 20rpx;
}

.skeleton-content {
	flex: 1;
}

.skeleton-line {
	height: 24rpx;
	background: linear-gradient(90deg, #f0f0f0 25%, #e6e6e6 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: skeleton-loading 1.5s infinite;
	border-radius: 4rpx;
	margin-bottom: 16rpx;
}

.skeleton-line-title {
	width: 60%;
}

.skeleton-line-subtitle {
	width: 80%;
	margin-bottom: 0;
}

@keyframes skeleton-loading {
	0% {
		background-position: -200% 0;
	}
	100% {
		background-position: 200% 0;
	}
}

/* 空状态样式 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin: 24rpx 0 16rpx;
}

.empty-description {
	font-size: 28rpx;
	color: #999;
	line-height: 1.5;
	margin-bottom: 40rpx;
}

.empty-action {
	min-width: 200rpx;
}
</style>