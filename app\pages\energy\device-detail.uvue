<template>
	<cl-page>
		<cl-topbar :title="device?.deviceName || '设备详情'">
			<template #right>
				<cl-button size="small" @click="showSettings = true">
					<cl-icon name="cl-icon-setting" />
				</cl-button>
			</template>
		</cl-topbar>

		<view v-if="device" class="device-detail">
			<!-- 设备基本信息 -->
			<view class="device-info-card">
				<view class="device-header">
					<view class="device-icon">
						<cl-icon name="cl-icon-bulb" size="60" color="#1890ff" />
						<status-indicator :status="device.status" class="status-badge" />
					</view>
					<view class="device-basic">
						<text class="device-name">{{ device.deviceName }}</text>
						<text class="device-code">{{ device.deviceCode }}</text>
						<text class="device-location">{{ device.location }}</text>
						<text class="device-type">{{ device.deviceType }}</text>
					</view>
					<view class="device-status">
						<text class="status-text" :class="getStatusClass(device.status)">
							{{ getStatusText(device.status) }}
						</text>
						<text class="last-update">{{ formatTime(device.lastUpdateTime) }}</text>
					</view>
				</view>

				<view class="device-controls">
					<cl-switch
						v-model="device.isOn"
						@change="handleToggle"
						size="large"
						:disabled="device.status === 2 || device.status === 3"
					/>
					<text class="control-label">{{ device.isOn ? "开启" : "关闭" }}</text>
				</view>

				<!-- 设备详细信息 -->
				<view class="device-details">
					<view class="detail-item">
						<text class="detail-label">安装时间:</text>
						<text class="detail-value">{{ formatTime(device.installTime) }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">运行时长:</text>
						<text class="detail-value">{{ device.runningHours }}小时</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">维护周期:</text>
						<text class="detail-value">{{ device.maintenanceCycle }}天</text>
					</view>
				</view>
			</view>

			<!-- 照明控制面板 -->
			<view class="control-panel">
				<lighting-panel
					:device="device"
					@brightness-change="handleBrightnessChange"
					@color-temp-change="handleColorTempChange"
					@scene-change="handleSceneChange"
				/>
			</view>

			<!-- 实时数据 -->
			<view class="realtime-data">
				<view class="section-title">
					<cl-icon name="cl-icon-chart" />
					<text>实时数据</text>
					<view class="refresh-btn" @click="loadRealtimeData">
						<cl-icon
							name="cl-icon-refresh"
							size="16"
							:class="{ rotating: refreshing }"
						/>
					</view>
				</view>

				<view class="data-grid">
					<view class="data-item">
						<cl-icon name="cl-icon-flash" color="#ff6b35" />
						<text class="data-label">功率</text>
						<text class="data-value">{{ realtimeData.power }}W</text>
						<text class="data-trend" :class="getTrendClass(realtimeData.powerTrend)">
							{{ getTrendText(realtimeData.powerTrend) }}
						</text>
					</view>
					<view class="data-item">
						<cl-icon name="cl-icon-zap" color="#4caf50" />
						<text class="data-label">电压</text>
						<text class="data-value">{{ realtimeData.voltage }}V</text>
						<text class="data-trend" :class="getTrendClass(realtimeData.voltageTrend)">
							{{ getTrendText(realtimeData.voltageTrend) }}
						</text>
					</view>
					<view class="data-item">
						<cl-icon name="cl-icon-activity" color="#2196f3" />
						<text class="data-label">电流</text>
						<text class="data-value">{{ realtimeData.current }}A</text>
						<text class="data-trend" :class="getTrendClass(realtimeData.currentTrend)">
							{{ getTrendText(realtimeData.currentTrend) }}
						</text>
					</view>
					<view class="data-item">
						<cl-icon name="cl-icon-battery" color="#9c27b0" />
						<text class="data-label">今日能耗</text>
						<text class="data-value">{{ realtimeData.consumption }}kWh</text>
						<text
							class="data-trend"
							:class="getTrendClass(realtimeData.consumptionTrend)"
						>
							{{ getTrendText(realtimeData.consumptionTrend) }}
						</text>
					</view>
				</view>
			</view>

			<!-- 历史记录 -->
			<view class="history-section">
				<view class="section-title">
					<cl-icon name="cl-icon-history" />
					<text>操作历史</text>
					<cl-button size="mini" @click="loadMoreHistory">查看更多</cl-button>
				</view>

				<view v-if="historyRecords.length > 0" class="history-list">
					<view v-for="record in historyRecords" :key="record.id" class="history-item">
						<view class="history-icon">
							<cl-icon
								:name="getHistoryIcon(record.type)"
								:color="getHistoryColor(record.type)"
							/>
						</view>
						<view class="history-content">
							<text class="history-action">{{ record.action }}</text>
							<text class="history-desc">{{ record.description }}</text>
							<text class="history-time">{{ formatTime(record.createTime) }}</text>
						</view>
					</view>
				</view>

				<view v-else class="empty-history">
					<cl-icon name="cl-icon-file-text" size="40" color="#ccc" />
					<text class="empty-text">暂无操作记录</text>
				</view>
			</view>

			<!-- 底部操作按钮 -->
			<view class="bottom-actions">
				<cl-button type="primary" @click="goToEnergyMonitor" class="action-btn">
					能耗监控
				</cl-button>
				<cl-button type="warning" @click="goToFaultManage" class="action-btn">
					故障管理
				</cl-button>
			</view>
		</view>

		<!-- 设备设置弹窗 -->
		<cl-popup v-model="showSettings" title="设备设置" size="large">
			<view class="settings-content">
				<view class="setting-item">
					<text class="setting-label">设备名称</text>
					<cl-input v-model="settingsForm.deviceName" placeholder="请输入设备名称" />
				</view>
				<view class="setting-item">
					<text class="setting-label">安装位置</text>
					<cl-input v-model="settingsForm.location" placeholder="请输入安装位置" />
				</view>
				<view class="setting-item">
					<text class="setting-label">维护周期(天)</text>
					<cl-input-number v-model="settingsForm.maintenanceCycle" :min="1" :max="365" />
				</view>
				<view class="setting-item">
					<text class="setting-label">自动模式</text>
					<cl-switch v-model="settingsForm.autoMode" />
				</view>
			</view>

			<template #footer>
				<cl-button @click="showSettings = false">取消</cl-button>
				<cl-button type="primary" @click="saveSettings">保存</cl-button>
			</template>
		</cl-popup>

		<!-- 加载状态 -->
		<cl-loading-mask v-else :loading="true" text="加载中..." />
	</cl-page>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { router } from "@/cool";
import { energyDeviceStore } from "@/stores/energy-device";
import { energyMonitorStore } from "@/stores/energy-monitor";
import LightingPanel from "@/components/business/lighting-panel.uvue";
import StatusIndicator from "@/components/business/status-indicator.uvue";
import { formatTime } from "@/cool/utils";

// 获取路由参数
const { id } = router.query;
const deviceId = parseInt(id as string);

// 响应式数据
const device = ref<any>(null);
const realtimeData = reactive({
	power: 0,
	voltage: 0,
	current: 0,
	consumption: 0,
	powerTrend: 0, // 1: 上升, 0: 平稳, -1: 下降
	voltageTrend: 0,
	currentTrend: 0,
	consumptionTrend: 0
});
const historyRecords = ref<any[]>([]);
const loading = ref(false);
const refreshing = ref(false);
const showSettings = ref(false);
const settingsForm = reactive({
	deviceName: "",
	location: "",
	maintenanceCycle: 30,
	autoMode: false
});

// 设备控制
const handleToggle = async (value: boolean) => {
	try {
		await energyDeviceStore.controlDevice(deviceId, "toggle", value);
		uni.showToast({
			title: value ? "设备已开启" : "设备已关闭",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
		// 恢复原状态
		device.value.isOn = !value;
	}
};

const handleBrightnessChange = async (brightness: number) => {
	try {
		await energyDeviceStore.controlDevice(deviceId, "brightness", brightness);
		device.value.brightness = brightness;
	} catch (error: any) {
		uni.showToast({
			title: error.message || "亮度调节失败",
			icon: "none"
		});
	}
};

const handleColorTempChange = async (colorTemp: number) => {
	try {
		await energyDeviceStore.controlDevice(deviceId, "colorTemp", colorTemp);
		device.value.colorTemp = colorTemp;
	} catch (error: any) {
		uni.showToast({
			title: error.message || "色温调节失败",
			icon: "none"
		});
	}
};

const handleSceneChange = async (scene: string) => {
	try {
		await energyDeviceStore.controlDevice(deviceId, "scene", scene);
		uni.showToast({
			title: "场景切换成功",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "场景切换失败",
			icon: "none"
		});
	}
};

// 页面跳转
const goToEnergyMonitor = () => {
	router.push("/pages/energy/energy-monitor", {
		deviceId: deviceId
	});
};

const goToFaultManage = () => {
	router.push("/pages/energy/fault-manage", {
		deviceId: deviceId
	});
};

// 加载设备详情
const loadDeviceDetail = async () => {
	try {
		loading.value = true;
		device.value = await energyDeviceStore.getDeviceDetail(deviceId);
	} finally {
		loading.value = false;
	}
};

// 加载实时数据
const loadRealtimeData = async () => {
	try {
		refreshing.value = true;
		const data = await energyMonitorStore.getRealtimeData(deviceId);
		if (data && data.length > 0) {
			const latest = data[data.length - 1];
			const previous = data.length > 1 ? data[data.length - 2] : latest;

			// 计算趋势
			const powerTrend =
				latest.power > previous.power ? 1 : latest.power < previous.power ? -1 : 0;
			const voltageTrend =
				latest.voltage > previous.voltage ? 1 : latest.voltage < previous.voltage ? -1 : 0;
			const currentTrend =
				latest.current > previous.current ? 1 : latest.current < previous.current ? -1 : 0;
			const consumptionTrend =
				latest.consumption > previous.consumption
					? 1
					: latest.consumption < previous.consumption
						? -1
						: 0;

			Object.assign(realtimeData, {
				power: latest.power,
				voltage: latest.voltage,
				current: latest.current,
				consumption: latest.consumption,
				powerTrend,
				voltageTrend,
				currentTrend,
				consumptionTrend
			});
		}
	} catch (error) {
		console.error("加载实时数据失败:", error);
		uni.showToast({
			title: "数据加载失败",
			icon: "none"
		});
	} finally {
		refreshing.value = false;
	}
};

// 加载操作历史
const loadHistoryRecords = async () => {
	try {
		// 模拟历史记录数据
		historyRecords.value = [
			{
				id: 1,
				type: "power",
				action: "设备开启",
				description: "用户手动开启设备",
				createTime: "2024-01-15 14:30:00"
			},
			{
				id: 2,
				type: "brightness",
				action: "亮度调节至80%",
				description: "通过移动端调节亮度",
				createTime: "2024-01-15 14:25:00"
			},
			{
				id: 3,
				type: "color",
				action: "色温调节至4000K",
				description: "自动调节为日光模式",
				createTime: "2024-01-15 14:20:00"
			},
			{
				id: 4,
				type: "scene",
				action: "切换至工作模式",
				description: "定时任务自动切换",
				createTime: "2024-01-15 14:15:00"
			},
			{
				id: 5,
				type: "maintenance",
				action: "设备维护检查",
				description: "系统自动检测设备状态",
				createTime: "2024-01-15 10:00:00"
			}
		];
	} catch (error) {
		console.error("加载历史记录失败:", error);
	}
};

// 加载更多历史记录
const loadMoreHistory = () => {
	// 显示历史记录弹窗
	uni.showModal({
		title: "设备历史记录",
		content: "功能开发中，敬请期待！\n\n您可以在操作历史区域查看最近的设备操作记录。",
		showCancel: false,
		confirmText: "知道了"
	});
};

// 保存设备设置
const saveSettings = async () => {
	try {
		await energyDeviceStore.updateDevice(deviceId, settingsForm);
		uni.showToast({
			title: "设置保存成功",
			icon: "success"
		});
		showSettings.value = false;
		// 重新加载设备信息
		await loadDeviceDetail();
	} catch (error: any) {
		uni.showToast({
			title: error.message || "保存失败",
			icon: "none"
		});
	}
};

// 获取状态文本
const getStatusText = (status: number) => {
	const statusMap = {
		1: "在线",
		2: "故障",
		3: "离线",
		4: "维护"
	};
	return statusMap[status] || "未知";
};

// 获取状态样式类
const getStatusClass = (status: number) => {
	const classMap = {
		1: "status-online",
		2: "status-fault",
		3: "status-offline",
		4: "status-maintenance"
	};
	return classMap[status] || "";
};

// 获取趋势文本
const getTrendText = (trend: number) => {
	if (trend > 0) return "↗";
	if (trend < 0) return "↘";
	return "→";
};

// 获取趋势样式类
const getTrendClass = (trend: number) => {
	if (trend > 0) return "trend-up";
	if (trend < 0) return "trend-down";
	return "trend-stable";
};

// 获取历史记录图标
const getHistoryIcon = (type: string) => {
	const iconMap = {
		power: "cl-icon-power",
		brightness: "cl-icon-sun",
		color: "cl-icon-droplet",
		scene: "cl-icon-settings",
		maintenance: "cl-icon-tool"
	};
	return iconMap[type] || "cl-icon-info";
};

// 获取历史记录颜色
const getHistoryColor = (type: string) => {
	const colorMap = {
		power: "#52c41a",
		brightness: "#faad14",
		color: "#1890ff",
		scene: "#722ed1",
		maintenance: "#fa541c"
	};
	return colorMap[type] || "#666";
};

// 定时刷新实时数据
let realtimeTimer: number | null = null;

const startRealtimeUpdate = () => {
	realtimeTimer = setInterval(() => {
		loadRealtimeData();
	}, 5000); // 每5秒刷新一次
};

const stopRealtimeUpdate = () => {
	if (realtimeTimer) {
		clearInterval(realtimeTimer);
		realtimeTimer = null;
	}
};

onMounted(async () => {
	await loadDeviceDetail();
	if (device.value) {
		// 初始化设置表单
		Object.assign(settingsForm, {
			deviceName: device.value.deviceName,
			location: device.value.location,
			maintenanceCycle: device.value.maintenanceCycle || 30,
			autoMode: device.value.autoMode || false
		});
	}
	await loadRealtimeData();
	await loadHistoryRecords();
	startRealtimeUpdate();
});

// 页面卸载时清理定时器
uni.$on("onUnload", () => {
	stopRealtimeUpdate();
});
</script>

<style scoped>
.device-detail {
	padding: 20rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.device-info-card {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.device-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 30rpx;
}

.device-icon {
	position: relative;
	margin-right: 30rpx;
}

.status-badge {
	position: absolute;
	top: -5rpx;
	right: -5rpx;
}

.device-basic {
	flex: 1;
}

.device-name {
	font-size: 20px;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.device-code {
	font-size: 14px;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.device-location {
	font-size: 14px;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}

.device-type {
	font-size: 12px;
	color: #1890ff;
	background: #e6f7ff;
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
	display: inline-block;
}

.device-status {
	margin-left: 20rpx;
	text-align: right;
}

.status-text {
	font-size: 14px;
	font-weight: 500;
	display: block;
	margin-bottom: 4rpx;
}

.status-online {
	color: #52c41a;
}
.status-fault {
	color: #ff4d4f;
}
.status-offline {
	color: #d9d9d9;
}
.status-maintenance {
	color: #faad14;
}

.last-update {
	font-size: 12px;
	color: #999;
	display: block;
}

.device-controls {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 20rpx;
	padding: 20rpx 0;
	border-top: 1px solid #f0f0f0;
	margin-bottom: 20rpx;
}

.control-label {
	font-size: 16px;
	color: #333;
	font-weight: 500;
}

.device-details {
	border-top: 1px solid #f0f0f0;
	padding-top: 20rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8rpx 0;
}

.detail-label {
	font-size: 14px;
	color: #666;
}

.detail-value {
	font-size: 14px;
	color: #333;
	font-weight: 500;
}

.control-panel {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.realtime-data {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 10rpx;
	margin-bottom: 20rpx;
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.refresh-btn {
	padding: 4rpx;
	border-radius: 50%;
	background: #f5f5f5;
	transition: all 0.3s;
}

.refresh-btn:active {
	background: #e6f7ff;
}

.rotating {
	animation: rotate 1s linear infinite;
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

.data-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.data-item {
	text-align: center;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	position: relative;
}

.data-item cl-icon {
	margin-bottom: 8rpx;
}

.data-label {
	font-size: 12px;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.data-value {
	font-size: 18px;
	font-weight: bold;
	color: #1890ff;
	display: block;
	margin-bottom: 4rpx;
}

.data-trend {
	font-size: 12px;
	font-weight: 500;
	display: block;
}

.trend-up {
	color: #52c41a;
}
.trend-down {
	color: #ff4d4f;
}
.trend-stable {
	color: #999;
}

.history-section {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 100rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.history-list {
	max-height: 400rpx;
	overflow-y: auto;
}

.history-item {
	display: flex;
	align-items: flex-start;
	padding: 15rpx 0;
	border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
	border-bottom: none;
}

.history-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 15rpx;
	flex-shrink: 0;
}

.history-content {
	flex: 1;
}

.history-action {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 4rpx;
}

.history-desc {
	font-size: 13px;
	color: #666;
	display: block;
	margin-bottom: 6rpx;
}

.history-time {
	font-size: 12px;
	color: #999;
	display: block;
}

.empty-history {
	text-align: center;
	padding: 40rpx 20rpx;
}

.empty-text {
	font-size: 14px;
	color: #999;
	display: block;
	margin-top: 10rpx;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: white;
	padding: 20rpx;
	border-top: 1px solid #e8e8e8;
	display: flex;
	gap: 20rpx;
	box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-btn {
	flex: 1;
}

/* 设置弹窗样式 */
.settings-content {
	padding: 20rpx;
}

.setting-item {
	margin-bottom: 30rpx;
}

.setting-label {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 8rpx;
}
</style>
