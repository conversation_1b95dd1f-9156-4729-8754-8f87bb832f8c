<template>
	<view class="device-card" :class="{ offline: !device.online }" @click="handleClick">
		<!-- 设备状态指示器 -->
		<view class="status-indicator">
			<status-indicator 
				:status="device.online ? 'online' : 'offline'"
				:text="device.online ? '在线' : '离线'"
			/>
		</view>
		
		<!-- 设备信息 -->
		<view class="device-info">
			<view class="device-header">
				<text class="device-name">{{ device.name }}</text>
				<view class="device-type">
					<cl-icon :name="getDeviceIcon(device.type)" size="16" color="#666" />
					<text class="type-text">{{ getDeviceTypeText(device.type) }}</text>
				</view>
			</view>
			
			<view class="device-location">
				<cl-icon name="cl-icon-location" size="12" color="#999" />
				<text class="location-text">{{ device.location }}</text>
			</view>
			
			<view class="device-stats">
				<view class="stat-item">
					<text class="stat-label">功率</text>
					<text class="stat-value">{{ device.power }}W</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">亮度</text>
					<text class="stat-value">{{ device.brightness }}%</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">温度</text>
					<text class="stat-value">{{ device.temperature }}°C</text>
				</view>
			</view>
		</view>
		
		<!-- 控制按钮 -->
		<view class="device-controls">
			<view class="control-item" @click.stop="togglePower">
				<cl-icon 
					:name="device.isOn ? 'cl-icon-bulb-on' : 'cl-icon-bulb-off'"
					:color="device.isOn ? '#52c41a' : '#ccc'"
					size="20"
				/>
			</view>
			
			<view v-if="showBrightnessControl" class="control-item" @click.stop="showBrightnessSlider">
				<cl-icon name="cl-icon-brightness" color="#1890ff" size="20" />
			</view>
			
			<view v-if="showColorControl" class="control-item" @click.stop="showColorPicker">
				<cl-icon name="cl-icon-palette" color="#fa8c16" size="20" />
			</view>
			
			<view class="control-item" @click.stop="showMoreActions">
				<cl-icon name="cl-icon-more" color="#666" size="20" />
			</view>
		</view>
		
		<!-- 故障指示 -->
		<view v-if="device.hasFault" class="fault-indicator">
			<cl-icon name="cl-icon-warning" color="#ff4d4f" size="16" />
			<text class="fault-text">故障</text>
		</view>
		
		<!-- 能耗信息 -->
		<view class="energy-info">
			<view class="energy-item">
				<text class="energy-label">今日耗电</text>
				<text class="energy-value">{{ device.todayConsumption }}kWh</text>
			</view>
			<view class="energy-item">
				<text class="energy-label">运行时长</text>
				<text class="energy-value">{{ formatRuntime(device.runtime) }}</text>
			</view>
		</view>
		
		<!-- 亮度滑块弹窗 -->
		<cl-popup v-model="showBrightnessModal" direction="bottom">
			<view class="brightness-modal">
				<view class="modal-header">
					<text class="modal-title">调节亮度</text>
					<cl-button text @click="showBrightnessModal = false">完成</cl-button>
				</view>
				<view class="brightness-control">
					<cl-icon name="cl-icon-brightness-low" color="#ccc" size="20" />
					<cl-slider 
						v-model="brightnessValue"
						:min="0"
						:max="100"
						@change="handleBrightnessChange"
						class="brightness-slider"
					/>
					<cl-icon name="cl-icon-brightness-high" color="#1890ff" size="20" />
				</view>
				<view class="brightness-value">
					<text class="value-text">{{ brightnessValue }}%</text>
				</view>
			</view>
		</cl-popup>
		
		<!-- 颜色选择弹窗 -->
		<cl-popup v-model="showColorModal" direction="bottom">
			<view class="color-modal">
				<view class="modal-header">
					<text class="modal-title">选择颜色</text>
					<cl-button text @click="showColorModal = false">完成</cl-button>
				</view>
				<view class="color-presets">
					<view 
						v-for="color in colorPresets" 
						:key="color.name"
						class="color-item"
						:class="{ active: selectedColor === color.value }"
						:style="{ backgroundColor: color.value }"
						@click="selectColor(color.value)"
					>
						<cl-icon v-if="selectedColor === color.value" name="cl-icon-check" color="white" size="16" />
					</view>
				</view>
				<view class="color-name">
					<text class="name-text">{{ getColorName(selectedColor) }}</text>
				</view>
			</view>
		</cl-popup>
		
		<!-- 更多操作弹窗 -->
		<cl-popup v-model="showActionsModal" direction="bottom">
			<view class="actions-modal">
				<view class="modal-header">
					<text class="modal-title">设备操作</text>
					<cl-button text @click="showActionsModal = false">取消</cl-button>
				</view>
				<view class="action-list">
					<view class="action-item" @click="viewDetail">
						<cl-icon name="cl-icon-info" color="#1890ff" size="20" />
						<text class="action-text">查看详情</text>
					</view>
					<view class="action-item" @click="editDevice">
						<cl-icon name="cl-icon-edit" color="#52c41a" size="20" />
						<text class="action-text">编辑设备</text>
					</view>
					<view class="action-item" @click="viewHistory">
						<cl-icon name="cl-icon-history" color="#fa8c16" size="20" />
						<text class="action-text">操作历史</text>
					</view>
					<view class="action-item" @click="resetDevice">
						<cl-icon name="cl-icon-refresh" color="#722ed1" size="20" />
						<text class="action-text">重启设备</text>
					</view>
					<view class="action-item danger" @click="removeDevice">
						<cl-icon name="cl-icon-delete" color="#ff4d4f" size="20" />
						<text class="action-text">移除设备</text>
					</view>
				</view>
			</view>
		</cl-popup>
	</view>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { router } from "@/cool";
import StatusIndicator from "./status-indicator.uvue";

// Props
interface DeviceCardProps {
	device: {
		id: number;
		name: string;
		type: string;
		location: string;
		online: boolean;
		isOn: boolean;
		power: number;
		brightness: number;
		temperature: number;
		hasFault: boolean;
		todayConsumption: number;
		runtime: number;
		color?: string;
	};
	showBrightnessControl?: boolean;
	showColorControl?: boolean;
}

const props = withDefaults(defineProps<DeviceCardProps>(), {
	showBrightnessControl: true,
	showColorControl: false
});

// Emits
const emit = defineEmits<{
	click: [device: any];
	togglePower: [device: any];
	brightnessChange: [device: any, brightness: number];
	colorChange: [device: any, color: string];
}>();

// 响应式数据
const showBrightnessModal = ref(false);
const showColorModal = ref(false);
const showActionsModal = ref(false);
const brightnessValue = ref(props.device.brightness);
const selectedColor = ref(props.device.color || '#ffffff');

// 颜色预设
const colorPresets = [
	{ name: "暖白", value: "#fff2e6" },
	{ name: "冷白", value: "#f0f8ff" },
	{ name: "自然白", value: "#ffffff" },
	{ name: "红色", value: "#ff4d4f" },
	{ name: "橙色", value: "#fa8c16" },
	{ name: "黄色", value: "#fadb14" },
	{ name: "绿色", value: "#52c41a" },
	{ name: "蓝色", value: "#1890ff" },
	{ name: "紫色", value: "#722ed1" }
];

// 监听设备亮度变化
watch(() => props.device.brightness, (newVal) => {
	brightnessValue.value = newVal;
});

// 监听设备颜色变化
watch(() => props.device.color, (newVal) => {
	selectedColor.value = newVal || '#ffffff';
});

// 获取设备图标
const getDeviceIcon = (type: string) => {
	const iconMap: Record<string, string> = {
		led: "cl-icon-bulb",
		switch: "cl-icon-switch",
		dimmer: "cl-icon-brightness",
		sensor: "cl-icon-sensor",
		controller: "cl-icon-control"
	};
	return iconMap[type] || "cl-icon-device";
};

// 获取设备类型文本
const getDeviceTypeText = (type: string) => {
	const typeMap: Record<string, string> = {
		led: "LED灯具",
		switch: "智能开关",
		dimmer: "调光器",
		sensor: "传感器",
		controller: "控制器"
	};
	return typeMap[type] || "未知设备";
};

// 格式化运行时长
const formatRuntime = (minutes: number) => {
	if (minutes < 60) {
		return `${minutes}分钟`;
	} else if (minutes < 1440) {
		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;
		return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
	} else {
		const days = Math.floor(minutes / 1440);
		const hours = Math.floor((minutes % 1440) / 60);
		return hours > 0 ? `${days}天${hours}小时` : `${days}天`;
	}
};

// 获取颜色名称
const getColorName = (color: string) => {
	const preset = colorPresets.find(p => p.value === color);
	return preset ? preset.name : "自定义";
};

// 点击卡片
const handleClick = () => {
	emit('click', props.device);
};

// 切换电源
const togglePower = async () => {
	try {
		// 调用API切换设备电源状态
		emit('togglePower', props.device);
		
		uni.showToast({
			title: props.device.isOn ? "设备已关闭" : "设备已开启",
			icon: "success",
			duration: 1500
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
	}
};

// 显示亮度滑块
const showBrightnessSlider = () => {
	if (!props.device.online || !props.device.isOn) {
		uni.showToast({
			title: "设备离线或未开启",
			icon: "none"
		});
		return;
	}
	showBrightnessModal.value = true;
};

// 处理亮度变化
const handleBrightnessChange = (value: number) => {
	emit('brightnessChange', props.device, value);
};

// 显示颜色选择器
const showColorPicker = () => {
	if (!props.device.online || !props.device.isOn) {
		uni.showToast({
			title: "设备离线或未开启",
			icon: "none"
		});
		return;
	}
	showColorModal.value = true;
};

// 选择颜色
const selectColor = (color: string) => {
	selectedColor.value = color;
	emit('colorChange', props.device, color);
};

// 显示更多操作
const showMoreActions = () => {
	showActionsModal.value = true;
};

// 查看详情
const viewDetail = () => {
	showActionsModal.value = false;
	router.push("/pages/energy/device-detail", {
		deviceId: props.device.id
	});
};

// 编辑设备
const editDevice = () => {
	showActionsModal.value = false;
	router.push("/pages/energy/device-edit", {
		deviceId: props.device.id
	});
};

// 查看历史
const viewHistory = () => {
	showActionsModal.value = false;
	// 显示历史记录提示
	uni.showModal({
		title: '设备历史记录',
		content: '功能开发中，敬请期待！\n\n您可以点击设备进入详情页面查看最近的操作记录。',
		showCancel: false,
		confirmText: '知道了'
	});
};

// 重启设备
const resetDevice = async () => {
	showActionsModal.value = false;
	try {
		uni.showModal({
			title: "确认重启",
			content: "确认要重启此设备吗？",
			success: async (res) => {
				if (res.confirm) {
					// 调用API重启设备
					uni.showToast({
						title: "设备重启中...",
						icon: "loading",
						duration: 2000
					});
				}
			}
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "重启失败",
			icon: "none"
		});
	}
};

// 移除设备
const removeDevice = async () => {
	showActionsModal.value = false;
	try {
		uni.showModal({
			title: "确认移除",
			content: "确认要移除此设备吗？此操作不可恢复。",
			confirmColor: "#ff4d4f",
			success: async (res) => {
				if (res.confirm) {
					// 调用API移除设备
					uni.showToast({
						title: "设备已移除",
						icon: "success"
					});
				}
			}
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "移除失败",
			icon: "none"
		});
	}
};
</script>

<style scoped>
.device-card {
	position: relative;
	background-color: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
}

.device-card:active {
	transform: scale(0.98);
}

.device-card.offline {
	opacity: 0.7;
	background-color: #f5f5f5;
}

.status-indicator {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
}

.device-info {
	margin-bottom: 25rpx;
}

.device-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 15rpx;
}

.device-name {
	font-size: 18px;
	font-weight: 600;
	color: #333;
	max-width: 60%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.device-type {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.type-text {
	font-size: 12px;
	color: #666;
}

.device-location {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 20rpx;
}

.location-text {
	font-size: 14px;
	color: #999;
}

.device-stats {
	display: flex;
	gap: 30rpx;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 5rpx;
}

.stat-label {
	font-size: 12px;
	color: #666;
}

.stat-value {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.device-controls {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-top: 1px solid #f0f0f0;
	border-bottom: 1px solid #f0f0f0;
	margin-bottom: 20rpx;
}

.control-item {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	background-color: #f9f9f9;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.control-item:active {
	transform: scale(0.9);
	background-color: #e6f7ff;
}

.fault-indicator {
	position: absolute;
	top: 20rpx;
	left: 20rpx;
	display: flex;
	align-items: center;
	gap: 5rpx;
	background-color: #fff2f0;
	padding: 5rpx 10rpx;
	border-radius: 12rpx;
	border: 1px solid #ffccc7;
}

.fault-text {
	font-size: 10px;
	color: #ff4d4f;
}

.energy-info {
	display: flex;
	justify-content: space-between;
}

.energy-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 5rpx;
}

.energy-label {
	font-size: 12px;
	color: #666;
}

.energy-value {
	font-size: 14px;
	font-weight: 500;
	color: #1890ff;
}

/* 弹窗样式 */
.brightness-modal,
.color-modal,
.actions-modal {
	background-color: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 30rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.modal-title {
	font-size: 18px;
	font-weight: 500;
	color: #333;
}

.brightness-control {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.brightness-slider {
	flex: 1;
}

.brightness-value {
	text-align: center;
}

.value-text {
	font-size: 24px;
	font-weight: 600;
	color: #1890ff;
}

.color-presets {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.color-item {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 3rpx solid transparent;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.color-item.active {
	border-color: #1890ff;
	transform: scale(1.1);
}

.color-name {
	text-align: center;
}

.name-text {
	font-size: 16px;
	color: #333;
}

.action-list {
	display: flex;
	flex-direction: column;
	gap: 5rpx;
}

.action-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 25rpx 20rpx;
	border-radius: 12rpx;
	transition: all 0.3s ease;
}

.action-item:active {
	background-color: #f0f0f0;
}

.action-item.danger:active {
	background-color: #fff2f0;
}

.action-text {
	font-size: 16px;
	color: #333;
}

.action-item.danger .action-text {
	color: #ff4d4f;
}
</style>