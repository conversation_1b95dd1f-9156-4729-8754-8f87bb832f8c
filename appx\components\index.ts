// 组件库导出文件
import ClApp from './cl-app/cl-app.uvue'
import ClPage from './cl-page/cl-page.uvue'
import ClConfig from './cl-config/cl-config.uvue'

// 导出所有组件
export {
  ClApp,
  ClPage,
  ClConfig
}

// 组件列表
export const components = {
  ClApp,
  ClPage,
  ClConfig
}

// 安装函数（如果需要全局注册）
export const install = (app: any) => {
  Object.keys(components).forEach(key => {
    app.component(key, components[key as keyof typeof components])
  })
}

export default {
  install,
  ...components
}
