// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 场景输出参数
/// </summary>
public class EnergySceneOutput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 场景编码
    /// </summary>
    public string SceneCode { get; set; }

    /// <summary>
    /// 场景名称
    /// </summary>
    public string SceneName { get; set; }

    /// <summary>
    /// 场景类型：1手动场景 2定时场景 3感应场景 4联动场景
    /// </summary>
    public int SceneType { get; set; }

    /// <summary>
    /// 场景描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 场景图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 执行次数
    /// </summary>
    public int ExecuteCount { get; set; }

    /// <summary>
    /// 最后执行时间
    /// </summary>
    public DateTime? LastExecuteTime { get; set; }

    /// <summary>
    /// 平均执行时长(秒)
    /// </summary>
    public decimal? AvgExecuteTime { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public decimal? SuccessRate { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 状态名称
    /// </summary>
    public string? StatusName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 触发条件(JSON格式)
    /// </summary>
    public string? TriggerCondition { get; set; }

    /// <summary>
    /// 执行动作(JSON格式)
    /// </summary>
    public string? ExecuteActions { get; set; }

    /// <summary>
    /// 定时表达式
    /// </summary>
    public string? CronExpression { get; set; }

    /// <summary>
    /// 场景设备列表
    /// </summary>
    public List<EnergySceneDeviceOutput> Devices { get; set; } = new();
}

/// <summary>
/// 场景统计输出参数
/// </summary>
public class EnergySceneStatOutput
{
    /// <summary>
    /// 场景总数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 启用场景数
    /// </summary>
    public int EnabledCount { get; set; }

    /// <summary>
    /// 禁用场景数
    /// </summary>
    public int DisabledCount { get; set; }

    /// <summary>
    /// 总执行次数
    /// </summary>
    public int TotalExecuteCount { get; set; }

    /// <summary>
    /// 今日执行次数
    /// </summary>
    public int TodayExecuteCount { get; set; }

    /// <summary>
    /// 本月执行次数
    /// </summary>
    public int MonthExecuteCount { get; set; }

    /// <summary>
    /// 成功执行次数
    /// </summary>
    public int SuccessExecuteCount { get; set; }

    /// <summary>
    /// 失败执行次数
    /// </summary>
    public int FailedExecuteCount { get; set; }

    /// <summary>
    /// 平均成功率
    /// </summary>
    public decimal AvgSuccessRate { get; set; }

    /// <summary>
    /// 平均执行时长(秒)
    /// </summary>
    public decimal AvgExecuteDuration { get; set; }

    /// <summary>
    /// 统计时间
    /// </summary>
    public DateTime StatTime { get; set; }

    /// <summary>
    /// 场景总数
    /// </summary>
    public int TotalSceneCount { get; set; }

    /// <summary>
    /// 启用场景数
    /// </summary>
    public int EnabledSceneCount { get; set; }

    /// <summary>
    /// 禁用场景数
    /// </summary>
    public int DisabledSceneCount { get; set; }

    /// <summary>
    /// 热门场景排行
    /// </summary>
    public List<SceneRankItem> PopularScenes { get; set; } = new();
}

/// <summary>
/// 场景排行项
/// </summary>
public class SceneRankItem
{
    /// <summary>
    /// 场景ID
    /// </summary>
    public long SceneId { get; set; }

    /// <summary>
    /// 场景编码
    /// </summary>
    public string SceneCode { get; set; }

    /// <summary>
    /// 场景名称
    /// </summary>
    public string SceneName { get; set; }

    /// <summary>
    /// 执行次数
    /// </summary>
    public int ExecuteCount { get; set; }

    /// <summary>
    /// 排名
    /// </summary>
    public int Rank { get; set; }

    /// <summary>
    /// 最近执行时间
    /// </summary>
    public DateTime? LastExecuteTime { get; set; }
}