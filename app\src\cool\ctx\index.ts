import { isArray, isEmpty, isNull } from "../utils";

type Page = {
	path: string;
	style?: UTSJSONObject;
};

type SubPackage = {
	root: string;
	pages: Page[];
};

export type TabBarItem = {
	text?: string;
	pagePath: string;
	iconPath?: string;
	selectedIconPath?: string;
	visible?: boolean;
};

export type TabBar = {
	custom?: boolean;
	color?: string;
	selectedColor?: string;
	backgroundColor?: string;
	borderStyle?: string;
	blurEffect?: "dark" | "extralight" | "light" | "none";
	list?: TabBarItem[];
	position?: "top" | "bottom";
	fontSize?: string;
	iconWidth?: string;
	spacing?: string;
	height?: string;
	backgroundImage?: string;
	backgroundRepeat?: "repeat" | "repeat-x" | "repeat-y" | "no-repeat";
	redDotColor?: string;
};

export type Ctx = {
	appid: string;
	globalStyle: UTSJSONObject;
	pages: Page[];
	uniIdRouter: UTSJSONObject;
	theme: UTSJSONObject;
	tabBar: TabBar;
	subPackages: SubPackage[];
	SAFE_CHAR_MAP_LOCALE: string[][];
	color: UTSJSONObject;
};

// 初始化 ctx 对象，手动注入 pages.json 配置
export const ctx = {
	appid: "",
	globalStyle: {},
	pages: [
		{ path: "pages/index/home", style: { navigationStyle: "custom" } },
		{ path: "pages/index/my", style: { navigationStyle: "custom" } },
		{ path: "pages/index/template", style: { navigationBarTitleText: "%模板%" } },
		{ path: "pages/test", style: { navigationBarTitleText: "测试页面" } },
		{ path: "pages/simple", style: { navigationBarTitleText: "简单页面" } }
	],
	uniIdRouter: {},
	theme: {},
	tabBar: {
		custom: false,
		color: "@tabColor",
		selectedColor: "@tabSelectedColor",
		backgroundColor: "@tabBgColor",
		borderStyle: "@tabBorderStyle",
		height: "60px",
		list: [
			{
				pagePath: "pages/index/home",
				iconPath: "/static/icon/tabbar/home.png",
				selectedIconPath: "/static/icon/tabbar/home2.png",
				text: "%首页%"
			},
			{
				pagePath: "pages/index/template",
				iconPath: "/static/icon/tabbar/template.png",
				selectedIconPath: "/static/icon/tabbar/template2.png",
				text: "%模板%"
			},
			{
				pagePath: "pages/index/my",
				iconPath: "/static/icon/tabbar/my.png",
				selectedIconPath: "/static/icon/tabbar/my2.png",
				text: "%我的%"
			}
		]
	},
	subPackages: [
		{
			root: "pages/set",
			pages: [
				{ path: "index", style: { navigationBarTitleText: "%设置%" } },
				{ path: "general", style: { navigationBarTitleText: "%通用设置%" } },
				{ path: "notice", style: { navigationBarTitleText: "%通知设置%" } },
				{ path: "about", style: { navigationBarTitleText: "" } },
				{ path: "cs", style: { navigationBarTitleText: "%联系客服%" } }
			]
		}
	],
	SAFE_CHAR_MAP_LOCALE: [],
	color: {}
} as Ctx;

// PAGES 用于存储所有页面的路径及样式信息
export let PAGES: Page[] = [...ctx.pages];

// 遍历 ctx.subPackages，将所有子包下的页面信息合并到 PAGES 中
if (isArray(ctx.subPackages)) {
	ctx.subPackages.forEach((a) => {
		a.pages.forEach((b) => {
			PAGES.push({
				path: a.root + "/" + b.path, // 拼接子包根路径和页面路径
				style: b.style
			});
		});
	});
}

// 确保每个页面路径都以 "/" 开头，符合 uni-app x 规范
PAGES.forEach((e) => {
	if (!e.path.startsWith("/")) {
		e.path = "/" + e.path;
	}
});

// TABS 用于存储 tabBar 配置项
export let TABS: TabBarItem[] = [];

// 如果 tabBar 配置存在且列表不为空，则初始化 TABS
if (!isNull(ctx.tabBar) && !isEmpty(ctx.tabBar.list!)) {
	TABS = ctx.tabBar.list!;

	// 确保每个 tabBar 页面的路径都以 "/" 开头
	TABS.forEach((e) => {
		if (!e.pagePath.startsWith("/")) {
			e.pagePath = "/" + e.pagePath;
		}
	});
}
