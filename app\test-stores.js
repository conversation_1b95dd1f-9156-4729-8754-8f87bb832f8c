// 简单的状态管理模块测试文件
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { useStore } from './cool/store/index.ts';

// 创建Vue应用和Pinia实例
const app = createApp({});
const pinia = createPinia();
app.use(pinia);

// 测试状态管理模块
try {
  const stores = useStore();
  
  console.log('✅ 状态管理模块加载成功');
  console.log('可用的stores:', Object.keys(stores));
  
  // 测试设备状态管理
  if (stores.energyDevice) {
    console.log('✅ 设备状态管理模块加载成功');
    console.log('设备列表长度:', stores.energyDevice.devices.length);
  }
  
  // 测试能耗监控状态管理
  if (stores.energyMonitor) {
    console.log('✅ 能耗监控状态管理模块加载成功');
    console.log('实时数据状态:', stores.energyMonitor.realTimeData ? '已加载' : '未加载');
  }
  
  // 测试用户设置状态管理
  if (stores.energySettings) {
    console.log('✅ 用户设置状态管理模块加载成功');
    console.log('当前主题:', stores.energySettings.currentTheme);
  }
  
} catch (error) {
  console.error('❌ 状态管理模块测试失败:', error.message);
  console.error('错误详情:', error);
}