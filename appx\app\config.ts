// 应用全局配置
const config = {
  // API配置
  api: {
    baseUrl: 'https://api.example.com', // 实际项目中替换为真实API地址
    timeout: 10000, // 请求超时时间(毫秒)
    retryCount: 3, // 请求重试次数
  },

  // 主题配置
  theme: {
    primaryColor: '#3a86ff', // 主色调
    secondaryColor: '#8338ec', // 辅助色
    successColor: '#07c160', // 成功色
    warningColor: '#ff9f1c', // 警告色
    errorColor: '#f5222d', // 错误色
    textColor: {
      primary: '#333333',
      secondary: '#666666',
      tertiary: '#999999',
    },
    backgroundColor: {
      primary: '#ffffff',
      secondary: '#f5f5f5',
      tertiary: '#eeeeee',
    },
  },

  // 设备配置
  device: {
    minBrightness: 10, // 最小亮度值
    maxBrightness: 100, // 最大亮度值
    minColorTemperature: 2700, // 最小色温值(K)
    maxColorTemperature: 6500, // 最大色温值(K)
  },

  // 分页配置
  pagination: {
    defaultPageSize: 10,
    pageSizes: [10, 20, 50, 100],
  },

  // 路由配置
  routes: {
    home: '/pages/index/home',
    deviceList: '/pages/energy/device-list',
    lightingControl: '/pages/energy/lighting-control',
    energyMonitor: '/pages/energy/energy-monitor',
    my: '/pages/user/my',
  },

  // 场景模式配置
  scenes: [
    { id: 'reading', name: '阅读模式', brightness: 80, colorTemperature: 4000 },
    { id: 'movie', name: '观影模式', brightness: 40, colorTemperature: 3000 },
    { id: 'sleep', name: '睡眠模式', brightness: 10, colorTemperature: 2700 },
    { id: 'work', name: '工作模式', brightness: 70, colorTemperature: 5000 },
  ],
}

export default config