// 全局类型定义文件

// 基础响应接口
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp?: string
}

// 分页请求参数
export interface PageParams {
  page: number
  pageSize: number
  keyword?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页响应数据
export interface PageResult<T = any> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 设备相关类型
export interface Device {
  id: string
  name: string
  type: DeviceType
  status: DeviceStatus
  online: boolean
  powerOn: boolean
  brightness: number
  colorTemp: number
  colorTemperature: number
  scene: SceneMode
  location: string
  room?: string
  floor?: string
  building?: string
  lastActiveTime: string
  installDate?: string
  model?: string
  manufacturer?: string
  version?: string
  ip?: string
  mac?: string
  tags?: string[]
  workingHours?: number
  totalEnergy?: number
  powerConsumption?: number
}

// 设备类型枚举
export type DeviceType = 'led_light' | 'fluorescent' | 'halogen' | 'smart_bulb'

// 设备状态枚举
export type DeviceStatus = 'online' | 'offline' | 'error' | 'maintenance'

// 场景模式枚举
export type SceneMode = 'home' | 'reading' | 'movie' | 'sleep' | 'work' | 'custom'

// 设备统计信息
export interface DeviceStats {
  total: number
  online: number
  offline: number
  error?: number
  maintenance?: number
}

// 设备控制参数
export interface DeviceControlParams {
  powerOn?: boolean
  brightness?: number
  colorTemp?: number
  scene?: SceneMode
  [key: string]: any
}

// 能耗数据类型
export interface EnergyData {
  id?: string
  date: string
  timestamp?: string
  energy: number
  power?: number
  voltage?: number
  current?: number
  powerFactor?: number
  frequency?: number
  temperature?: number
  humidity?: number
  deviceId: string
  deviceName: string
  location?: string
  workingTime?: number
}

// 能耗概览数据
export interface EnergyOverview {
  totalEnergy: number
  totalEnergyDelta: number
  avgEnergy: number
  avgEnergyDelta: number
  savingEffect: number
  savingAmount: number
  peakPower?: number
  peakTime?: string
  workingHours?: number
  efficiency?: number
  carbonEmission?: number
}

// 能耗统计数据
export interface EnergyStats {
  timeRange: string
  totalEnergy: number
  totalCost: number
  averagePower: number
  peakPower: number
  peakTime: string
  workingHours: number
  deviceCount: number
  efficiency: number
  carbonEmission: number
  byDevice: EnergyByDevice[]
  byTime: EnergyByTime[]
  byLocation: EnergyByLocation[]
  trend: EnergyTrend[]
}

// 按设备统计
export interface EnergyByDevice {
  deviceId: string
  deviceName: string
  energy: number
  cost: number
  percentage: number
}

// 按时间统计
export interface EnergyByTime {
  time: string
  energy: number
  power: number
  cost: number
}

// 按位置统计
export interface EnergyByLocation {
  location: string
  energy: number
  cost: number
  deviceCount: number
  percentage: number
}

// 能耗趋势
export interface EnergyTrend {
  date: string
  energy: number
  cost?: number
  change?: number
}

// 用户信息类型
export interface UserInfo {
  id: string
  name: string
  username?: string
  email?: string
  phone?: string
  role: UserRole
  avatar: string
  token: string
  permissions?: string[]
  lastLoginTime?: string
  createTime?: string
}

// 用户角色枚举
export type UserRole = 'admin' | 'manager' | 'operator' | 'viewer'

// 字典项类型
export interface DictItem {
  label: string
  value: string | number
  color?: string
  icon?: string
  description?: string
  sort?: number
  disabled?: boolean
}

// 字典数据类型
export interface DictData {
  [key: string]: DictItem[]
}

// 通知类型
export interface Notification {
  id: string
  title: string
  content: string
  type: NotificationType
  level: NotificationLevel
  read: boolean
  createTime: string
  deviceId?: string
  deviceName?: string
  data?: any
}

// 通知类型枚举
export type NotificationType = 'device' | 'energy' | 'system' | 'maintenance' | 'alert'

// 通知级别枚举
export type NotificationLevel = 'info' | 'warning' | 'error' | 'success'

// 日期范围类型
export type DateRange = 'today' | 'yesterday' | '7days' | '30days' | 'thisMonth' | 'lastMonth' | 'custom'

// 主题类型
export type Theme = 'light' | 'dark' | 'auto'

// 导航样式类型
export type NavigationStyle = 'default' | 'custom'

// 组件Props基础类型
export interface BaseComponentProps {
  className?: string
  style?: string | object
}

// 页面组件Props
export interface PageProps extends BaseComponentProps {
  title?: string
  showStatusBar?: boolean
  showNavBar?: boolean
  showBack?: boolean
  showSafeArea?: boolean
  backgroundColor?: string
  navBarColor?: string
  titleColor?: string
}

// 配置组件Props
export interface ConfigProps {
  theme?: Theme
  navigationStyle?: NavigationStyle
  tabBar?: string | object
  primaryColor?: string
  backgroundColor?: string
}

// 错误类型
export interface AppError {
  code: string | number
  message: string
  details?: any
  timestamp?: string
}

// 请求配置类型
export interface RequestConfig {
  baseURL?: string
  timeout?: number
  headers?: Record<string, string>
  interceptors?: {
    request?: (config: any) => any
    response?: (response: any) => any
    error?: (error: any) => any
  }
}

// 存储键名枚举
export enum StorageKeys {
  USER_INFO = 'userInfo',
  DICT_DATA = 'dictData',
  THEME = 'theme',
  LANGUAGE = 'language',
  DEVICE_CACHE = 'deviceCache',
  ENERGY_CACHE = 'energyCache'
}

// 事件类型
export interface AppEvent {
  type: string
  data?: any
  timestamp?: number
}

// 导出所有类型（这些文件将在后续创建）
// export * from './device'
// export * from './energy'
// export * from './user'
// export * from './api'
