import { ref, computed } from 'vue';
import { defineStore } from 'pinia';

// 能源数据类型定义
export interface EnergyData {
  timestamp: string;
  power: number; // 功率 (W)
  energy: number; // 用电量 (kWh)
  voltage: number; // 电压 (V)
  current: number; // 电流 (A)
  deviceId?: string;
}

// 能源统计信息
export interface EnergyStats {
  totalEnergy: number; // 总用电量
  totalCost: number; // 总电费
  avgPower: number; // 平均功率
  peakPower: number; // 峰值功率
  efficiency: number; // 能效比
  carbonEmission: number; // 碳排放量
}

// 能源报告
export interface EnergyReport {
  period: 'day' | 'week' | 'month' | 'year';
  startDate: string;
  endDate: string;
  stats: EnergyStats;
  data: EnergyData[];
  comparison?: {
    previousPeriod: EnergyStats;
    changePercent: number;
  };
}

// 节能建议
export interface EnergySuggestion {
  id: string;
  type: 'schedule' | 'brightness' | 'replacement' | 'maintenance';
  title: string;
  description: string;
  potentialSaving: number; // 潜在节省 (kWh)
  priority: 'high' | 'medium' | 'low';
  deviceIds?: string[];
}

// 查询参数
export interface EnergyQueryParams {
  startDate: string;
  endDate: string;
  deviceIds?: string[];
  granularity?: 'minute' | 'hour' | 'day' | 'month';
}

// 实时数据
export interface RealTimeData {
  currentPower: number;
  todayEnergy: number;
  todayCost: number;
  onlineDevices: number;
  totalDevices: number;
  lastUpdate: string;
}

export const useEnergyMonitorStore = defineStore('energyMonitor', () => {
  // 实时数据
  const realTimeData = ref<RealTimeData>({
    currentPower: 0,
    todayEnergy: 0,
    todayCost: 0,
    onlineDevices: 0,
    totalDevices: 0,
    lastUpdate: new Date().toISOString()
  });
  
  // 历史数据
  const historyData = ref<EnergyData[]>([]);
  
  // 能源报告
  const reports = ref<EnergyReport[]>([]);
  
  // 节能建议
  const suggestions = ref<EnergySuggestion[]>([]);
  
  // 加载状态
  const loading = ref(false);
  
  // 电价设置 (元/kWh)
  const electricityPrice = ref(0.6);
  
  // 计算属性：今日统计
  const todayStats = computed<EnergyStats>(() => {
    const today = new Date().toDateString();
    const todayData = historyData.value.filter(d => 
      new Date(d.timestamp).toDateString() === today
    );
    
    if (todayData.length === 0) {
      return {
        totalEnergy: 0,
        totalCost: 0,
        avgPower: 0,
        peakPower: 0,
        efficiency: 0,
        carbonEmission: 0
      };
    }
    
    const totalEnergy = todayData.reduce((sum, d) => sum + d.energy, 0);
    const avgPower = todayData.reduce((sum, d) => sum + d.power, 0) / todayData.length;
    const peakPower = Math.max(...todayData.map(d => d.power));
    const totalCost = totalEnergy * electricityPrice.value;
    const carbonEmission = totalEnergy * 0.785; // 碳排放系数
    
    return {
      totalEnergy,
      totalCost,
      avgPower,
      peakPower,
      efficiency: avgPower > 0 ? (totalEnergy / avgPower) * 100 : 0,
      carbonEmission
    };
  });
  
  // 获取实时数据
  const fetchRealTimeData = async () => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 模拟实时数据
      realTimeData.value = {
        currentPower: Math.random() * 100 + 50,
        todayEnergy: Math.random() * 20 + 10,
        todayCost: (Math.random() * 20 + 10) * electricityPrice.value,
        onlineDevices: Math.floor(Math.random() * 10) + 5,
        totalDevices: 15,
        lastUpdate: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取实时数据失败:', error);
    }
  };
  
  // 获取设备实时数据
  const getRealtimeData = async (deviceId: string) => {
    try {
      // 这里应该调用实际的API
      // const response = await request.get(`/api/energy/devices/${deviceId}/realtime`);
      // return response.data;
      
      // 模拟设备实时数据
      return [
        {
          timestamp: new Date(Date.now() - 60000).toISOString(),
          power: Math.floor(Math.random() * 100) + 20,
          voltage: 220 + Math.random() * 10,
          current: Math.random() * 2 + 0.5,
          consumption: Math.random() * 5 + 1,
          powerTrend: Math.floor(Math.random() * 3) - 1,
          voltageTrend: Math.floor(Math.random() * 3) - 1,
          currentTrend: Math.floor(Math.random() * 3) - 1,
          consumptionTrend: Math.floor(Math.random() * 3) - 1
        },
        {
          timestamp: new Date().toISOString(),
          power: Math.floor(Math.random() * 100) + 20,
          voltage: 220 + Math.random() * 10,
          current: Math.random() * 2 + 0.5,
          consumption: Math.random() * 5 + 1,
          powerTrend: Math.floor(Math.random() * 3) - 1,
          voltageTrend: Math.floor(Math.random() * 3) - 1,
          currentTrend: Math.floor(Math.random() * 3) - 1,
          consumptionTrend: Math.floor(Math.random() * 3) - 1
        }
      ];
    } catch (error) {
      console.error('获取设备实时数据失败:', error);
      throw error;
    }
  };
  
  // 获取历史数据
  const fetchHistoryData = async (params: EnergyQueryParams) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 生成模拟历史数据
      const data: EnergyData[] = [];
      const start = new Date(params.startDate);
      const end = new Date(params.endDate);
      const interval = params.granularity === 'hour' ? 60 * 60 * 1000 : 24 * 60 * 60 * 1000;
      
      for (let time = start.getTime(); time <= end.getTime(); time += interval) {
        data.push({
          timestamp: new Date(time).toISOString(),
          power: Math.random() * 80 + 20,
          energy: Math.random() * 5 + 1,
          voltage: 220 + Math.random() * 10 - 5,
          current: Math.random() * 2 + 0.5
        });
      }
      
      historyData.value = data;
    } catch (error) {
      console.error('获取历史数据失败:', error);
    } finally {
      loading.value = false;
    }
  };
  
  // 生成能源报告
  const generateReport = async (period: 'day' | 'week' | 'month' | 'year') => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const now = new Date();
      let startDate: Date;
      let endDate = new Date(now);
      
      switch (period) {
        case 'day':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
      }
      
      // 生成模拟报告数据
      const stats: EnergyStats = {
        totalEnergy: Math.random() * 1000 + 500,
        totalCost: (Math.random() * 1000 + 500) * electricityPrice.value,
        avgPower: Math.random() * 60 + 30,
        peakPower: Math.random() * 100 + 80,
        efficiency: Math.random() * 20 + 80,
        carbonEmission: (Math.random() * 1000 + 500) * 0.785
      };
      
      const report: EnergyReport = {
        period,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        stats,
        data: historyData.value,
        comparison: {
          previousPeriod: {
            ...stats,
            totalEnergy: stats.totalEnergy * 0.9,
            totalCost: stats.totalCost * 0.9
          },
          changePercent: 10
        }
      };
      
      reports.value.unshift(report);
      return report;
    } catch (error) {
      console.error('生成报告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };
  
  // 获取节能建议
  const fetchSuggestions = async () => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // 模拟节能建议
      suggestions.value = [
        {
          id: '1',
          type: 'schedule',
          title: '优化照明时间表',
          description: '建议在非工作时间自动关闭部分照明设备',
          potentialSaving: 15.5,
          priority: 'high',
          deviceIds: ['1', '2']
        },
        {
          id: '2',
          type: 'brightness',
          title: '调整亮度设置',
          description: '根据环境光线自动调节LED亮度可节省20%电量',
          potentialSaving: 12.3,
          priority: 'medium'
        },
        {
          id: '3',
          type: 'replacement',
          title: '更换高效LED',
          description: '将传统灯具更换为更高效的LED灯具',
          potentialSaving: 25.8,
          priority: 'high'
        }
      ];
    } catch (error) {
      console.error('获取节能建议失败:', error);
    }
  };
  
  // 设置电价
  const setElectricityPrice = (price: number) => {
    electricityPrice.value = price;
  };
  
  // 开始实时监控
  const startRealTimeMonitoring = () => {
    const interval = setInterval(() => {
      fetchRealTimeData();
    }, 5000); // 每5秒更新一次
    
    return () => clearInterval(interval);
  };
  
  return {
    realTimeData,
    historyData,
    reports,
    suggestions,
    loading,
    electricityPrice,
    todayStats,
    fetchRealTimeData,
    getRealtimeData,
    fetchHistoryData,
    generateReport,
    fetchSuggestions,
    setElectricityPrice,
    startRealTimeMonitoring
  };
});