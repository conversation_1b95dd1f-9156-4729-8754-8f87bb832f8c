import { ref, computed } from 'vue';
import { defineStore } from 'pinia';

// 设备类型定义
export interface EnergyDevice {
  id: string;
  name: string;
  type: 'light' | 'sensor' | 'controller';
  status: 'online' | 'offline' | 'error';
  power: number; // 功率 (W)
  energy: number; // 累计用电量 (kWh)
  location: string;
  lastUpdate: string;
  brightness?: number; // 亮度 (0-100)
  temperature?: number; // 温度传感器数据
  humidity?: number; // 湿度传感器数据
}

// 设备控制参数
export interface DeviceControlParams {
  deviceId: string;
  action: 'on' | 'off' | 'dim';
  brightness?: number;
}

// 批量控制参数
export interface BatchControlParams {
  deviceIds: string[];
  action: 'on' | 'off' | 'dim';
  brightness?: number;
}

// 设备筛选参数
export interface DeviceFilterParams {
  type?: string;
  status?: string;
  location?: string;
}

// 设备统计信息
export interface DeviceStats {
  total: number;
  online: number;
  offline: number;
  error: number;
  totalPower: number;
  totalEnergy: number;
}

export const useEnergyDeviceStore = defineStore('energyDevice', () => {
  // 设备列表
  const devices = ref<EnergyDevice[]>([]);
  
  // 当前选中的设备
  const selectedDevice = ref<EnergyDevice | null>(null);
  
  // 加载状态
  const loading = ref(false);
  
  // 筛选条件
  const filter = ref<DeviceFilterParams>({});
  
  // 计算属性：筛选后的设备列表
  const filteredDevices = computed(() => {
    return devices.value.filter(device => {
      if (filter.value.type && device.type !== filter.value.type) return false;
      if (filter.value.status && device.status !== filter.value.status) return false;
      if (filter.value.location && !device.location.includes(filter.value.location)) return false;
      return true;
    });
  });
  
  // 计算属性：设备统计信息
  const stats = computed<DeviceStats>(() => {
    const total = devices.value.length;
    const online = devices.value.filter(d => d.status === 'online').length;
    const offline = devices.value.filter(d => d.status === 'offline').length;
    const error = devices.value.filter(d => d.status === 'error').length;
    const totalPower = devices.value.reduce((sum, d) => sum + d.power, 0);
    const totalEnergy = devices.value.reduce((sum, d) => sum + d.energy, 0);
    
    return {
      total,
      online,
      offline,
      error,
      totalPower,
      totalEnergy
    };
  });
  
  // 获取设备列表
  const fetchDevices = async (params?: DeviceFilterParams) => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟数据
      devices.value = [
        {
          id: '1',
          name: '办公室LED灯1',
          type: 'light',
          status: 'online',
          power: 12,
          energy: 24.5,
          location: '办公室A',
          lastUpdate: new Date().toISOString(),
          brightness: 80
        },
        {
          id: '2',
          name: '走廊LED灯2',
          type: 'light',
          status: 'online',
          power: 8,
          energy: 16.2,
          location: '走廊',
          lastUpdate: new Date().toISOString(),
          brightness: 60
        }
      ];
    } catch (error) {
      console.error('获取设备列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 获取设备列表（别名）
  const getDeviceList = fetchDevices;

  // 获取设备详情
  const getDeviceDetail = async (deviceId: string) => {
    try {
      // 这里应该调用实际的API
      // const response = await request.get(`/api/energy/devices/${deviceId}`);
      // return response.data;
      
      // 模拟数据
      const device = devices.value.find(d => d.id === deviceId);
      if (device) {
        return {
          ...device,
          installTime: '2023-01-15',
          runningHours: 2400,
          maintenanceCycle: 180,
          model: 'LED-2024-Pro',
          manufacturer: 'EnergyTech',
          version: '1.2.0'
        };
      }
      throw new Error('设备不存在');
    } catch (error) {
      console.error('获取设备详情失败:', error);
      throw error;
    }
  };

  // 更新设备
  const updateDevice = async (deviceId: string, data: Partial<EnergyDevice>) => {
    try {
      // 这里应该调用实际的API
      // const response = await request.put(`/api/energy/devices/${deviceId}`, data);
      
      // 模拟更新
      const index = devices.value.findIndex(d => d.id === deviceId);
      if (index !== -1) {
        devices.value[index] = { ...devices.value[index], ...data };
      }
    } catch (error) {
      console.error('更新设备失败:', error);
      throw error;
    }
  };

  // 删除设备
  const removeDevice = async (deviceId: string) => {
    try {
      // 这里应该调用实际的API
      // await request.delete(`/api/energy/devices/${deviceId}`);
      
      // 模拟删除
      const index = devices.value.findIndex(d => d.id === deviceId);
      if (index !== -1) {
        devices.value.splice(index, 1);
      }
    } catch (error) {
      console.error('删除设备失败:', error);
      throw error;
    }
  };
  
  // 控制单个设备
  const controlDevice = async (params: DeviceControlParams) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const device = devices.value.find(d => d.id === params.deviceId);
      if (device) {
        if (params.action === 'on') {
          device.status = 'online';
          device.brightness = params.brightness || 100;
        } else if (params.action === 'off') {
          device.status = 'offline';
          device.brightness = 0;
        } else if (params.action === 'dim' && params.brightness !== undefined) {
          device.brightness = params.brightness;
        }
        device.lastUpdate = new Date().toISOString();
      }
    } catch (error) {
      console.error('控制设备失败:', error);
      throw error;
    }
  };
  
  // 批量控制设备
  const batchControlDevices = async (params: BatchControlParams) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      params.deviceIds.forEach(deviceId => {
        const device = devices.value.find(d => d.id === deviceId);
        if (device) {
          if (params.action === 'on') {
            device.status = 'online';
            device.brightness = params.brightness || 100;
          } else if (params.action === 'off') {
            device.status = 'offline';
            device.brightness = 0;
          } else if (params.action === 'dim' && params.brightness !== undefined) {
            device.brightness = params.brightness;
          }
          device.lastUpdate = new Date().toISOString();
        }
      });
    } catch (error) {
      console.error('批量控制设备失败:', error);
      throw error;
    }
  };
  
  // 设置筛选条件
  const setFilter = (newFilter: DeviceFilterParams) => {
    filter.value = { ...filter.value, ...newFilter };
  };
  
  // 清除筛选条件
  const clearFilter = () => {
    filter.value = {};
  };
  
  // 选择设备
  const selectDevice = (device: EnergyDevice | null) => {
    selectedDevice.value = device;
  };
  
  // 设备控制方法集合
  const deviceControls = {
    // 控制设备亮度
    brightness: async (deviceId: string, value: number) => {
      return controlDevice({ deviceId, action: 'dim', brightness: value });
    },
    
    // 控制设备色温
    colorTemp: async (deviceId: string, value: number) => {
      const device = devices.value.find(d => d.id === deviceId);
      if (device) {
        await updateDevice(deviceId, { ...device, lastUpdate: new Date().toISOString() });
      }
    },
    
    // 控制设备颜色
    color: async (deviceId: string, value: string) => {
      const device = devices.value.find(d => d.id === deviceId);
      if (device) {
        await updateDevice(deviceId, { ...device, lastUpdate: new Date().toISOString() });
      }
    },
    
    // 控制设备场景
    scene: async (deviceId: string, value: string) => {
      const device = devices.value.find(d => d.id === deviceId);
      if (device) {
        await updateDevice(deviceId, { ...device, lastUpdate: new Date().toISOString() });
      }
    },
    
    // 控制设备开关
    toggle: async (deviceId: string) => {
      const device = devices.value.find(d => d.id === deviceId);
      if (device) {
        const newAction = device.status === 'online' ? 'off' : 'on';
        return controlDevice({ deviceId, action: newAction });
      }
      throw new Error('设备不存在');
    },
    
    // 控制渐变模式
    gradientMode: async (deviceId: string, mode: string) => {
      const device = devices.value.find(d => d.id === deviceId);
      if (device) {
        await updateDevice(deviceId, { ...device, lastUpdate: new Date().toISOString() });
      }
    },
    
    // 控制渐变速度
    gradientSpeed: async (deviceId: string, speed: number) => {
      const device = devices.value.find(d => d.id === deviceId);
      if (device) {
        await updateDevice(deviceId, { ...device, lastUpdate: new Date().toISOString() });
      }
    },
    
    // 控制自动模式
    autoMode: async (deviceId: string, enabled: boolean) => {
      const device = devices.value.find(d => d.id === deviceId);
      if (device) {
        await updateDevice(deviceId, { ...device, lastUpdate: new Date().toISOString() });
      }
    }
  };

  return {
    devices,
    selectedDevice,
    loading,
    filter,
    filteredDevices,
    stats,
    fetchDevices,
    getDeviceList,
    getDeviceDetail,
    updateDevice,
    removeDevice,
    controlDevice,
    batchControlDevices,
    setFilter,
    clearFilter,
    selectDevice,
    deviceControls
  };
});