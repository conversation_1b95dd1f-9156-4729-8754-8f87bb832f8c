<template>
	<view class="refresh-control" :class="[`theme-${theme}`]">
		<!-- 下拉刷新指示器 -->
		<view 
			class="refresh-indicator" 
			:class="{
				'pulling': isPulling,
				'refreshing': isRefreshing,
				'can-refresh': canRefresh
			}"
			:style="{
				transform: `translateY(${indicatorTransform}px)`,
				opacity: indicatorOpacity
			}"
		>
			<!-- 自定义指示器内容 -->
			<view v-if="$slots.indicator" class="custom-indicator">
				<slot name="indicator" :status="refreshStatus" :progress="pullProgress"></slot>
			</view>
			
			<!-- 默认指示器 -->
			<view v-else class="default-indicator">
				<!-- 图标 -->
				<view class="indicator-icon" :class="{ 'rotating': isRefreshing }">
					<cl-icon 
						:name="currentIcon" 
						:size="iconSize" 
						:color="iconColor"
					/>
				</view>
				
				<!-- 文本 -->
				<text class="indicator-text">{{ currentText }}</text>
				
				<!-- 进度条 -->
				<view v-if="showProgress" class="progress-bar">
					<view 
						class="progress-fill" 
						:style="{ width: `${pullProgress * 100}%` }"
					></view>
				</view>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<view 
			class="refresh-content"
			:style="{
				transform: `translateY(${contentTransform}px)`,
				transition: contentTransition
			}"
			@touchstart="handleTouchStart"
			@touchmove="handleTouchMove"
			@touchend="handleTouchEnd"
			@touchcancel="handleTouchCancel"
		>
			<slot></slot>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick } from "vue";

// 刷新状态类型
type RefreshStatus = 'idle' | 'pulling' | 'can-refresh' | 'refreshing' | 'success' | 'error';
type RefreshTheme = 'light' | 'dark';

// Props
interface RefreshControlProps {
	// 是否启用下拉刷新
	enabled?: boolean;
	// 触发刷新的距离
	triggerDistance?: number;
	// 最大下拉距离
	maxDistance?: number;
	// 阻尼系数
	damping?: number;
	// 是否显示进度条
	showProgress?: boolean;
	// 主题
	theme?: RefreshTheme;
	// 自定义文本
	customTexts?: {
		idle?: string;
		pulling?: string;
		canRefresh?: string;
		refreshing?: string;
		success?: string;
		error?: string;
	};
	// 自定义图标
	customIcons?: {
		idle?: string;
		pulling?: string;
		canRefresh?: string;
		refreshing?: string;
		success?: string;
		error?: string;
	};
	// 图标大小
	iconSize?: number;
	// 动画持续时间
	animationDuration?: number;
	// 成功/错误状态显示时间
	statusDisplayTime?: number;
}

const props = withDefaults(defineProps<RefreshControlProps>(), {
	enabled: true,
	triggerDistance: 60,
	maxDistance: 120,
	damping: 0.5,
	showProgress: true,
	theme: 'light',
	iconSize: 20,
	animationDuration: 300,
	statusDisplayTime: 1500
});

// Emits
const emit = defineEmits<{
	refresh: [];
	statusChange: [status: RefreshStatus];
	pullProgress: [progress: number];
}>();

// 响应式数据
const refreshStatus = ref<RefreshStatus>('idle');
const isPulling = ref(false);
const isRefreshing = ref(false);
const pullDistance = ref(0);
const startY = ref(0);
const currentY = ref(0);
const isScrollAtTop = ref(true);

// 默认配置
const defaultTexts = {
	idle: '下拉刷新',
	pulling: '继续下拉',
	canRefresh: '释放刷新',
	refreshing: '正在刷新...',
	success: '刷新成功',
	error: '刷新失败'
};

const defaultIcons = {
	idle: 'cl-icon-arrow-down',
	pulling: 'cl-icon-arrow-down',
	canRefresh: 'cl-icon-arrow-up',
	refreshing: 'cl-icon-refresh-cw',
	success: 'cl-icon-check',
	error: 'cl-icon-x'
};

// 计算属性
const canRefresh = computed(() => {
	return pullDistance.value >= props.triggerDistance;
});

const pullProgress = computed(() => {
	return Math.min(pullDistance.value / props.triggerDistance, 1);
});

const currentText = computed(() => {
	const texts = { ...defaultTexts, ...props.customTexts };
	return texts[refreshStatus.value] || texts.idle;
});

const currentIcon = computed(() => {
	const icons = { ...defaultIcons, ...props.customIcons };
	return icons[refreshStatus.value] || icons.idle;
});

const iconColor = computed(() => {
	return props.theme === 'dark' ? '#fff' : '#666';
});

const indicatorTransform = computed(() => {
	if (refreshStatus.value === 'refreshing') {
		return 0;
	}
	return Math.min(pullDistance.value - props.triggerDistance, 0);
});

const indicatorOpacity = computed(() => {
	if (refreshStatus.value === 'refreshing') {
		return 1;
	}
	return Math.min(pullDistance.value / props.triggerDistance, 1);
});

const contentTransform = computed(() => {
	if (refreshStatus.value === 'refreshing') {
		return props.triggerDistance;
	}
	return Math.min(pullDistance.value, props.maxDistance);
});

const contentTransition = computed(() => {
	return isPulling.value ? 'none' : `transform ${props.animationDuration}ms ease-out`;
});

// 触摸事件处理
const handleTouchStart = (e: TouchEvent) => {
	if (!props.enabled || isRefreshing.value) return;
	
	startY.value = e.touches[0].clientY;
	currentY.value = startY.value;
	isPulling.value = false;
	
	// 检查是否在顶部
	checkScrollPosition();
};

const handleTouchMove = (e: TouchEvent) => {
	if (!props.enabled || isRefreshing.value || !isScrollAtTop.value) return;
	
	currentY.value = e.touches[0].clientY;
	const deltaY = currentY.value - startY.value;
	
	if (deltaY > 0) {
		e.preventDefault();
		isPulling.value = true;
		
		// 应用阻尼效果
		const damping = deltaY > props.triggerDistance ? props.damping : 1;
		pullDistance.value = deltaY * damping;
		
		// 更新状态
		updateRefreshStatus();
		
		// 发送进度事件
		emit('pullProgress', pullProgress.value);
	}
};

const handleTouchEnd = () => {
	if (!props.enabled || isRefreshing.value) return;
	
	isPulling.value = false;
	
	if (canRefresh.value) {
		// 触发刷新
		startRefresh();
	} else {
		// 重置状态
		resetRefresh();
	}
};

const handleTouchCancel = () => {
	if (!props.enabled) return;
	
	isPulling.value = false;
	resetRefresh();
};

// 检查滚动位置
const checkScrollPosition = () => {
	// 这里需要根据实际的滚动容器来判断
	// 简化实现，假设总是在顶部
	isScrollAtTop.value = true;
};

// 更新刷新状态
const updateRefreshStatus = () => {
	let newStatus: RefreshStatus = 'idle';
	
	if (isPulling.value) {
		if (canRefresh.value) {
			newStatus = 'can-refresh';
		} else {
			newStatus = 'pulling';
		}
	}
	
	if (newStatus !== refreshStatus.value) {
		refreshStatus.value = newStatus;
		emit('statusChange', newStatus);
	}
};

// 开始刷新
const startRefresh = () => {
	refreshStatus.value = 'refreshing';
	isRefreshing.value = true;
	emit('statusChange', 'refreshing');
	emit('refresh');
};

// 重置刷新状态
const resetRefresh = () => {
	pullDistance.value = 0;
	refreshStatus.value = 'idle';
	emit('statusChange', 'idle');
};

// 完成刷新
const finishRefresh = (success: boolean = true) => {
	const status: RefreshStatus = success ? 'success' : 'error';
	refreshStatus.value = status;
	emit('statusChange', status);
	
	// 显示状态一段时间后重置
	setTimeout(() => {
		isRefreshing.value = false;
		resetRefresh();
	}, props.statusDisplayTime);
};

// 暴露方法给父组件
defineExpose({
	finishRefresh,
	resetRefresh,
	startRefresh
});

// 监听刷新状态变化
watch(refreshStatus, (newStatus) => {
	emit('statusChange', newStatus);
});
</script>

<style scoped>
.refresh-control {
	position: relative;
	overflow: hidden;
	height: 100%;
}

/* 主题样式 */
.theme-light {
	background-color: #fff;
	color: #333;
}

.theme-dark {
	background-color: #1a1a1a;
	color: #fff;
}

/* 刷新指示器 */
.refresh-indicator {
	position: absolute;
	top: -60px;
	left: 0;
	right: 0;
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10;
	background-color: inherit;
}

.refresh-indicator.pulling {
	/* 下拉中的样式 */
}

.refresh-indicator.refreshing {
	top: 0;
	opacity: 1 !important;
	transform: translateY(0) !important;
}

.refresh-indicator.can-refresh {
	/* 可以刷新时的样式 */
}

/* 自定义指示器 */
.custom-indicator {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 默认指示器 */
.default-indicator {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

/* 指示器图标 */
.indicator-icon {
	transition: transform 0.3s ease;
}

.indicator-icon.rotating {
	animation: rotate 1s linear infinite;
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* 指示器文本 */
.indicator-text {
	font-size: 12px;
	color: inherit;
	opacity: 0.8;
}

/* 进度条 */
.progress-bar {
	width: 60rpx;
	height: 4rpx;
	background-color: rgba(0, 0, 0, 0.1);
	border-radius: 2rpx;
	overflow: hidden;
	margin-top: 4rpx;
}

.theme-dark .progress-bar {
	background-color: rgba(255, 255, 255, 0.2);
}

.progress-fill {
	height: 100%;
	background-color: #1890ff;
	border-radius: 2rpx;
	transition: width 0.1s ease;
}

/* 内容区域 */
.refresh-content {
	position: relative;
	z-index: 1;
	min-height: 100%;
	background-color: inherit;
}

/* 状态变化动画 */
.refresh-indicator {
	transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 成功状态 */
.refresh-indicator .indicator-icon {
	transition: all 0.3s ease;
}

.refresh-control .success .indicator-icon {
	color: #52c41a;
}

.refresh-control .error .indicator-icon {
	color: #ff4d4f;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.refresh-indicator {
		height: 50px;
		top: -50px;
	}
	
	.indicator-text {
		font-size: 11px;
	}
}

/* 无障碍访问 */
.refresh-indicator {
	/* 确保有足够的对比度 */
	/* 支持屏幕阅读器 */
}

/* 性能优化 */
.refresh-content {
	/* 启用硬件加速 */
	transform: translateZ(0);
	will-change: transform;
}

.refresh-indicator {
	transform: translateZ(0);
	will-change: transform, opacity;
}

/* 触摸反馈 */
.refresh-content {
	touch-action: pan-y;
}

/* 加载状态优化 */
.refresh-indicator.refreshing .indicator-icon {
	transform-origin: center;
}

/* 平滑过渡 */
.refresh-indicator,
.refresh-content {
	backface-visibility: hidden;
	-webkit-backface-visibility: hidden;
}
</style>