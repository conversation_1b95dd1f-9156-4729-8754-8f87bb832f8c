# 节能灯管理系统产品需求规格说明书

## 1. 产品概述

节能灯管理系统是一套基于Admin.NET框架和物联网技术的企业级智能照明管控平台，为企业和机构提供设备管理、远程控制、能耗监控和故障预警等功能。
系统采用前后端分离架构，支持Web管理端和移动端双平台访问，通过统一的管理平台实现对分布式照明设备的集中管控，帮助用户降低能耗成本、提升管理效率，实现绿色节能的智慧照明目标。
系统基于成熟的Admin.NET企业级开发框架，具备完善的权限管理、代码生成、任务调度等企业级功能基础。

## 2. 核心功能

### 2.1 用户角色

| 角色 | 注册方式 | 核心权限 |
|------|----------|----------|
| 超级管理员 | 系统预设账号 | 系统配置、租户管理、全局设置、插件管理 |
| 系统管理员 | 超管创建或LDAP同步 | 用户管理、角色权限、组织架构、系统监控 |
| 设备管理员 | 管理员创建 | 设备管理、照明控制、能耗监控、数据导出 |
| 普通用户 | 邀请码或自助注册 | 基础照明控制、数据查看、个人设置 |
| 维护人员 | 管理员分配角色 | 故障处理、设备维护记录、工单管理 |

### 2.2 功能模块

我们的节能灯管理系统基于Admin.NET框架构建，包含以下主要页面：
1. **智能仪表板**：数据概览、设备状态统计、实时告警、可视化图表
2. **设备管理中心**：设备列表、设备详情、设备配置、分组管理、批量导入
3. **智能照明控制**：实时控制、定时任务、情景模式、批量操作、MQTT通信
4. **能耗监控分析**：实时功耗、历史统计、节能分析、报表导出、趋势预测
5. **故障管理系统**：告警列表、故障处理、维修记录、预警设置、工单流程
6. **系统管理**：用户管理、角色权限、组织架构、菜单配置、操作日志
7. **系统配置**：基础配置、通信参数、数据备份、代码生成、任务调度
8. **移动端应用**：设备监控、远程控制、告警推送、数据查看

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 智能仪表板 | 数据概览 | 显示设备总数、在线率、能耗统计等关键指标，支持实时刷新 |
| 智能仪表板 | 设备状态地图 | 地图展示设备分布和实时状态，支持地理位置定位 |
| 智能仪表板 | 告警中心 | 实时显示设备告警信息和处理状态，支持SignalR推送 |
| 智能仪表板 | 可视化图表 | ECharts图表展示趋势分析，支持GoView大屏展示 |
| 设备管理中心 | 设备列表 | 展示所有设备信息，支持高级搜索、多条件筛选、分页导出 |
| 设备管理中心 | 设备详情 | 查看设备参数、运行状态、历史数据，支持实时监控 |
| 设备管理中心 | 设备配置 | 设置设备参数、MQTT通信地址、控制策略配置 |
| 设备管理中心 | 分组管理 | 创建设备组，支持树形结构管理和批量操作 |
| 设备管理中心 | 批量导入 | Excel模板导入设备信息，支持数据验证和错误提示 |
| 智能照明控制 | 实时控制 | 远程开关灯、调节亮度、颜色控制，基于MQTT协议通信 |
| 智能照明控制 | 定时任务 | 创建定时开关、周期性任务、节假日模式，支持Cron表达式 |
| 智能照明控制 | 情景模式 | 预设照明场景，一键切换不同模式，支持场景联动 |
| 智能照明控制 | 批量操作 | 选择多个设备进行统一控制操作，支持设备分组控制 |
| 智能照明控制 | MQTT通信 | 设备状态实时同步，支持断线重连和消息确认机制 |
| 能耗监控分析 | 实时功耗 | 显示当前功耗数据和变化趋势图表，支持多维度展示 |
| 能耗监控分析 | 历史统计 | 按时间段统计能耗数据，生成对比分析和同比环比 |
| 能耗监控分析 | 节能分析 | 计算节能效果，AI算法提供优化建议和预测 |
| 能耗监控分析 | 报表导出 | 生成Excel/PDF格式的能耗报表，支持模板定制 |
| 能耗监控分析 | 趋势预测 | 基于历史数据进行能耗趋势预测和异常检测 |
| 故障管理系统 | 告警列表 | 显示设备故障告警，支持多级别状态筛选和实时更新 |
| 故障管理系统 | 故障处理 | 记录故障处理过程和解决方案，支持工单流程管理 |
| 故障管理系统 | 维修记录 | 维护历史记录和设备保养计划，支持周期性提醒 |
| 故障管理系统 | 预警设置 | 配置故障预警规则和通知方式，支持多渠道推送 |
| 故障管理系统 | 工单流程 | 故障工单创建、分派、处理、验收的完整流程管理 |
| 系统管理 | 用户管理 | 管理系统用户账号和基本信息，支持LDAP集成 |
| 系统管理 | 角色权限 | 设置用户角色和功能权限，支持细粒度权限控制 |
| 系统管理 | 组织架构 | 管理部门组织结构，支持树形层级和人员分配 |
| 系统管理 | 菜单配置 | 动态配置系统菜单和路由权限 |
| 系统管理 | 操作日志 | 记录用户操作行为和系统访问日志，支持审计追踪 |
| 系统配置 | 基础配置 | 系统参数设置、界面个性化配置和多语言支持 |
| 系统配置 | 通信参数 | 配置MQTT服务器、设备通信协议和网络参数 |
| 系统配置 | 数据备份 | 定期备份系统数据和配置信息，支持自动备份策略 |
| 系统配置 | 代码生成 | 基于数据库表结构自动生成CRUD代码和API接口 |
| 系统配置 | 任务调度 | 配置定时任务和后台作业，支持Cron表达式 |
| 移动端应用 | 设备监控 | 移动端查看设备状态和实时数据，支持推送通知 |
| 移动端应用 | 远程控制 | 手机端远程控制设备开关和参数调节 |
| 移动端应用 | 告警处理 | 移动端接收和处理设备告警信息 |
| 移动端应用 | 个人中心 | 用户信息管理和应用设置配置 |

## 3. 核心流程

**管理员操作流程：**
管理员登录系统后，首先查看首页大屏了解整体运行状况。通过设备管理页面添加和配置新设备，设置设备分组便于批量管理。在照明控制页面创建定时任务和情景模式，实现自动化控制。定期查看能耗监控数据，分析节能效果并导出报表。处理故障告警，记录维修情况，并管理系统用户权限。

**普通用户操作流程：**
普通用户登录后可查看被授权的设备状态，在照明控制页面对指定设备进行开关和亮度调节。可以使用预设的情景模式快速切换照明场景，查看设备的能耗数据和历史记录。

**维护人员操作流程：**
维护人员接收到故障告警通知后，登录系统查看具体故障信息，前往现场处理故障并在系统中记录处理过程。完成维修后更新设备状态，录入维护记录和下次保养计划。

```mermaid
graph TD
    A[登录页面] --> B[首页大屏]
    B --> C[设备管理页]
    B --> D[照明控制页]
    B --> E[能耗监控页]
    B --> F[故障管理页]
    C --> G[设备详情页]
    C --> H[设备配置页]
    D --> I[定时任务页]
    D --> J[情景模式页]
    E --> K[历史统计页]
    E --> L[节能分析页]
    F --> M[故障处理页]
    F --> N[维修记录页]
    B --> O[用户管理页]
    B --> P[系统设置页]
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**：深蓝色(#1890FF)作为主色，浅蓝色(#E6F7FF)作为辅助色
- **按钮样式**：圆角矩形按钮，支持悬停和点击效果
- **字体**：主要使用微软雅黑，标题14-16px，正文12-14px
- **布局风格**：卡片式布局，左侧导航菜单，顶部面包屑导航
- **图标风格**：使用Element Plus图标库，简洁线性图标风格

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 首页大屏 | 数据概览 | 数字卡片展示，使用渐变背景，大号数字字体，配色为蓝色系 |
| 首页大屏 | 设备状态地图 | 交互式地图组件，设备状态用不同颜色标识，支持缩放和点击 |
| 首页大屏 | 告警中心 | 红色告警标识，滚动列表展示，支持点击查看详情 |
| 设备管理页 | 设备列表 | 表格布局，状态用彩色标签显示，操作按钮右对齐 |
| 设备管理页 | 设备详情 | 分栏布局，左侧基本信息，右侧实时数据图表 |
| 照明控制页 | 实时控制 | 开关按钮使用Toggle样式，亮度调节用滑块组件 |
| 照明控制页 | 情景模式 | 卡片式布局，每个场景用图标和名称展示 |
| 能耗监控页 | 实时功耗 | 仪表盘图表，使用ECharts组件，绿色表示正常 |
| 能耗监控页 | 历史统计 | 折线图和柱状图结合，支持时间范围选择 |
| 故障管理页 | 告警列表 | 表格布局，紧急程度用颜色区分，红色高危，黄色中等 |

### 4.3 响应式设计

系统采用桌面优先设计，同时适配平板和手机端访问。移动端优化触摸操作，按钮尺寸不小于44px，支持手势滑动和长按操作。