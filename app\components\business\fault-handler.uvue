<template>
	<view class="fault-handler">
		<!-- 故障信息卡片 -->
		<view class="fault-card" :class="`severity-${faultData.severity}`">
			<!-- 故障头部 -->
			<view class="fault-header">
				<view class="fault-title">
					<status-indicator 
						:status="faultData.status" 
						size="small" 
						:show-text="false" 
						:show-pulse="true"
					/>
					<text class="fault-name">{{ faultData.name }}</text>
					<view class="severity-badge" :class="`severity-${faultData.severity}`">
						<text class="severity-text">{{ getSeverityText(faultData.severity) }}</text>
					</view>
				</view>
				
				<view class="fault-actions">
					<cl-button 
						type="text" 
						size="small" 
						@click="toggleExpand"
					>
						<cl-icon :name="expanded ? 'cl-icon-chevron-up' : 'cl-icon-chevron-down'" size="16" />
					</cl-button>
				</view>
			</view>
			
			<!-- 故障基本信息 -->
			<view class="fault-info">
				<view class="info-row">
					<text class="info-label">设备:</text>
					<text class="info-value">{{ faultData.deviceName }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">时间:</text>
					<text class="info-value">{{ formatTime(faultData.occurTime) }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">持续:</text>
					<text class="info-value">{{ getDuration(faultData.occurTime) }}</text>
				</view>
			</view>
			
			<!-- 展开内容 -->
			<view v-if="expanded" class="fault-detail">
				<!-- 故障描述 -->
				<view class="detail-section">
					<text class="section-title">故障描述</text>
					<text class="section-content">{{ faultData.description }}</text>
				</view>
				
				<!-- 可能原因 -->
				<view class="detail-section">
					<text class="section-title">可能原因</text>
					<view class="cause-list">
						<view 
							v-for="(cause, index) in faultData.possibleCauses" 
							:key="index" 
							class="cause-item"
						>
							<text class="cause-text">{{ cause }}</text>
						</view>
					</view>
				</view>
				
				<!-- 建议解决方案 -->
				<view class="detail-section">
					<text class="section-title">建议解决方案</text>
					<view class="solution-list">
						<view 
							v-for="(solution, index) in faultData.suggestedSolutions" 
							:key="index" 
							class="solution-item"
							@click="applySolution(solution)"
						>
							<cl-icon name="cl-icon-lightbulb" size="14" color="#faad14" />
							<text class="solution-text">{{ solution.title }}</text>
							<cl-icon name="cl-icon-chevron-right" size="12" color="#999" />
						</view>
					</view>
				</view>
				
				<!-- 处理记录 -->
				<view v-if="faultData.handleRecords?.length" class="detail-section">
					<text class="section-title">处理记录</text>
					<view class="record-list">
						<view 
							v-for="(record, index) in faultData.handleRecords" 
							:key="index" 
							class="record-item"
						>
							<view class="record-header">
								<text class="record-time">{{ formatTime(record.time) }}</text>
								<text class="record-operator">{{ record.operator }}</text>
							</view>
							<text class="record-content">{{ record.content }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 操作按钮 -->
			<view class="fault-operations">
				<cl-button 
					v-if="canAssign" 
					type="primary" 
					size="small" 
					@click="showAssignModal = true"
				>
					分配处理
				</cl-button>
				
				<cl-button 
					v-if="canHandle" 
					type="success" 
					size="small" 
					@click="showHandleModal = true"
				>
					开始处理
				</cl-button>
				
				<cl-button 
					v-if="canClose" 
					type="default" 
					size="small" 
					@click="showCloseModal = true"
				>
					关闭故障
				</cl-button>
				
				<cl-button 
					type="text" 
					size="small" 
					@click="showMoreActions = true"
				>
					更多
				</cl-button>
			</view>
		</view>
		
		<!-- 分配处理弹窗 -->
		<cl-popup v-model="showAssignModal" direction="bottom">
			<view class="assign-modal">
				<view class="modal-header">
					<text class="modal-title">分配处理人员</text>
					<cl-button type="text" @click="showAssignModal = false">
						<cl-icon name="cl-icon-x" size="18" />
					</cl-button>
				</view>
				
				<view class="modal-content">
					<view class="form-item">
						<text class="form-label">处理人员</text>
						<cl-select 
							v-model="assignForm.assignee" 
							:options="assigneeOptions"
							placeholder="请选择处理人员"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">优先级</text>
						<cl-select 
							v-model="assignForm.priority" 
							:options="priorityOptions"
							placeholder="请选择优先级"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">备注</text>
						<cl-textarea 
							v-model="assignForm.remark" 
							placeholder="请输入分配备注"
							:rows="3"
						/>
					</view>
				</view>
				
				<view class="modal-footer">
					<cl-button @click="showAssignModal = false">取消</cl-button>
					<cl-button type="primary" @click="handleAssign">确认分配</cl-button>
				</view>
			</view>
		</cl-popup>
		
		<!-- 处理弹窗 -->
		<cl-popup v-model="showHandleModal" direction="bottom">
			<view class="handle-modal">
				<view class="modal-header">
					<text class="modal-title">处理故障</text>
					<cl-button type="text" @click="showHandleModal = false">
						<cl-icon name="cl-icon-x" size="18" />
					</cl-button>
				</view>
				
				<view class="modal-content">
					<view class="form-item">
						<text class="form-label">处理方案</text>
						<cl-select 
							v-model="handleForm.solution" 
							:options="solutionOptions"
							placeholder="请选择处理方案"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">处理说明</text>
						<cl-textarea 
							v-model="handleForm.description" 
							placeholder="请详细描述处理过程和结果"
							:rows="4"
						/>
					</view>
					
					<view class="form-item">
						<text class="form-label">上传图片</text>
						<cl-upload 
							v-model="handleForm.images" 
							:limit="3"
							accept="image/*"
							text="上传处理图片"
						/>
					</view>
				</view>
				
				<view class="modal-footer">
					<cl-button @click="showHandleModal = false">取消</cl-button>
					<cl-button type="success" @click="handleFault">完成处理</cl-button>
				</view>
			</view>
		</cl-popup>
		
		<!-- 关闭故障弹窗 -->
		<cl-popup v-model="showCloseModal" direction="center">
			<view class="close-modal">
				<view class="modal-header">
					<text class="modal-title">关闭故障</text>
				</view>
				
				<view class="modal-content">
					<text class="close-tip">确认要关闭此故障吗？关闭后将无法再次处理。</text>
					
					<view class="form-item">
						<text class="form-label">关闭原因</text>
						<cl-textarea 
							v-model="closeForm.reason" 
							placeholder="请输入关闭原因"
							:rows="3"
						/>
					</view>
				</view>
				
				<view class="modal-footer">
					<cl-button @click="showCloseModal = false">取消</cl-button>
					<cl-button type="error" @click="closeFault">确认关闭</cl-button>
				</view>
			</view>
		</cl-popup>
		
		<!-- 更多操作弹窗 -->
		<cl-action-sheet v-model="showMoreActions" :actions="moreActionList" @select="handleMoreAction" />
	</view>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { router } from "/@/cool";
import { formatTime, getDuration } from "/@/cool/utils";
import StatusIndicator from "./status-indicator.uvue";

// 故障数据类型定义
interface FaultData {
	id: string;
	name: string;
	description: string;
	deviceId: string;
	deviceName: string;
	status: 'fault' | 'warning' | 'processing' | 'resolved' | 'closed';
	severity: 'low' | 'medium' | 'high' | 'critical';
	occurTime: string;
	possibleCauses: string[];
	suggestedSolutions: {
		title: string;
		description: string;
		action?: string;
	}[];
	handleRecords?: {
		time: string;
		operator: string;
		content: string;
	}[];
	assignee?: string;
	priority?: 'low' | 'medium' | 'high' | 'urgent';
}

// Props
interface FaultHandlerProps {
	faultData: FaultData;
	canAssign?: boolean;
	canHandle?: boolean;
	canClose?: boolean;
	expandable?: boolean;
	defaultExpanded?: boolean;
}

const props = withDefaults(defineProps<FaultHandlerProps>(), {
	canAssign: true,
	canHandle: true,
	canClose: true,
	expandable: true,
	defaultExpanded: false
});

// Emits
const emit = defineEmits<{
	assign: [faultId: string, assignData: any];
	handle: [faultId: string, handleData: any];
	close: [faultId: string, closeData: any];
	expand: [faultId: string, expanded: boolean];
	solutionApply: [faultId: string, solution: any];
}>();

// 响应式数据
const expanded = ref(props.defaultExpanded);
const showAssignModal = ref(false);
const showHandleModal = ref(false);
const showCloseModal = ref(false);
const showMoreActions = ref(false);

// 表单数据
const assignForm = ref({
	assignee: '',
	priority: 'medium',
	remark: ''
});

const handleForm = ref({
	solution: '',
	description: '',
	images: []
});

const closeForm = ref({
	reason: ''
});

// 选项数据
const assigneeOptions = ref([
	{ label: '张工程师', value: 'zhang' },
	{ label: '李技术员', value: 'li' },
	{ label: '王维修员', value: 'wang' }
]);

const priorityOptions = ref([
	{ label: '低', value: 'low' },
	{ label: '中', value: 'medium' },
	{ label: '高', value: 'high' },
	{ label: '紧急', value: 'urgent' }
]);

const solutionOptions = ref([
	{ label: '重启设备', value: 'restart' },
	{ label: '更换部件', value: 'replace' },
	{ label: '软件修复', value: 'software_fix' },
	{ label: '现场维修', value: 'onsite_repair' }
]);

const moreActionList = ref([
	{ name: '查看详情', value: 'detail' },
	{ name: '导出报告', value: 'export' },
	{ name: '转发', value: 'forward' },
	{ name: '标记重要', value: 'mark_important' }
]);

// 计算属性
const canAssign = computed(() => {
	return props.canAssign && ['fault', 'warning'].includes(props.faultData.status);
});

const canHandle = computed(() => {
	return props.canHandle && ['fault', 'warning', 'processing'].includes(props.faultData.status);
});

const canClose = computed(() => {
	return props.canClose && ['resolved', 'processing'].includes(props.faultData.status);
});

// 方法
const getSeverityText = (severity: string) => {
	const severityMap = {
		low: '低',
		medium: '中',
		high: '高',
		critical: '严重'
	};
	return severityMap[severity] || severity;
};

const toggleExpand = () => {
	if (!props.expandable) return;
	expanded.value = !expanded.value;
	emit('expand', props.faultData.id, expanded.value);
};

const applySolution = (solution: any) => {
	emit('solutionApply', props.faultData.id, solution);
};

const handleAssign = () => {
	if (!assignForm.value.assignee) {
		uni.showToast({
			title: '请选择处理人员',
			icon: 'none'
		});
		return;
	}
	
	emit('assign', props.faultData.id, assignForm.value);
	showAssignModal.value = false;
	
	// 重置表单
	assignForm.value = {
		assignee: '',
		priority: 'medium',
		remark: ''
	};
};

const handleFault = () => {
	if (!handleForm.value.solution) {
		uni.showToast({
			title: '请选择处理方案',
			icon: 'none'
		});
		return;
	}
	
	if (!handleForm.value.description) {
		uni.showToast({
			title: '请输入处理说明',
			icon: 'none'
		});
		return;
	}
	
	emit('handle', props.faultData.id, handleForm.value);
	showHandleModal.value = false;
	
	// 重置表单
	handleForm.value = {
		solution: '',
		description: '',
		images: []
	};
};

const closeFault = () => {
	if (!closeForm.value.reason) {
		uni.showToast({
			title: '请输入关闭原因',
			icon: 'none'
		});
		return;
	}
	
	emit('close', props.faultData.id, closeForm.value);
	showCloseModal.value = false;
	
	// 重置表单
	closeForm.value = {
		reason: ''
	};
};

// 查看详情
const viewDetail = () => {
	// 显示故障详情弹窗
	uni.showModal({
		title: `故障详情 - ${props.faultData.name}`,
		content: `设备：${props.faultData.deviceName}\n故障类型：${props.faultData.name}\n故障描述：${props.faultData.description}\n发生时间：${formatTime(props.faultData.occurTime)}\n状态：${props.faultData.status}`,
		showCancel: false,
		confirmText: '关闭'
	});
};

const handleMoreAction = (action: any) => {
	switch (action.value) {
		case 'detail':
			viewDetail();
			break;
		case 'export':
			// 导出报告逻辑
			uni.showToast({
				title: '导出功能开发中',
				icon: 'none'
			});
			break;
		case 'forward':
			// 转发逻辑
			uni.showToast({
				title: '转发功能开发中',
				icon: 'none'
			});
			break;
		case 'mark_important':
			// 标记重要逻辑
			uni.showToast({
				title: '已标记为重要',
				icon: 'success'
			});
			break;
	}
};
</script>

<style scoped>
.fault-handler {
	padding: 0;
}

/* 故障卡片 */
.fault-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	border-left: 4rpx solid #ddd;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.severity-low {
	border-left-color: #52c41a;
}

.severity-medium {
	border-left-color: #faad14;
}

.severity-high {
	border-left-color: #ff7a45;
}

.severity-critical {
	border-left-color: #ff4d4f;
}

/* 故障头部 */
.fault-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.fault-title {
	display: flex;
	align-items: center;
	gap: 12rpx;
	flex: 1;
}

.fault-name {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	flex: 1;
}

.severity-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 10px;
}

.severity-badge.severity-low {
	background-color: #f6ffed;
	color: #52c41a;
}

.severity-badge.severity-medium {
	background-color: #fffbe6;
	color: #faad14;
}

.severity-badge.severity-high {
	background-color: #fff2e8;
	color: #ff7a45;
}

.severity-badge.severity-critical {
	background-color: #fff1f0;
	color: #ff4d4f;
}

.severity-text {
	font-weight: 500;
}

/* 故障信息 */
.fault-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	margin-bottom: 16rpx;
}

.info-row {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.info-label {
	font-size: 12px;
	color: #666;
	width: 80rpx;
	flex-shrink: 0;
}

.info-value {
	font-size: 12px;
	color: #333;
	flex: 1;
}

/* 故障详情 */
.fault-detail {
	margin-top: 16rpx;
	padding-top: 16rpx;
	border-top: 1rpx solid #f0f0f0;
}

.detail-section {
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 14px;
	font-weight: 600;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}

.section-content {
	font-size: 13px;
	color: #666;
	line-height: 1.5;
	display: block;
}

/* 原因列表 */
.cause-list {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.cause-item {
	display: flex;
	align-items: flex-start;
	gap: 8rpx;
	padding: 8rpx 12rpx;
	background-color: #fafafa;
	border-radius: 6rpx;
}

.cause-item::before {
	content: '•';
	color: #faad14;
	font-weight: bold;
	flex-shrink: 0;
}

.cause-text {
	font-size: 12px;
	color: #666;
	line-height: 1.4;
	flex: 1;
}

/* 解决方案列表 */
.solution-list {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.solution-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 12rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	border: 1rpx solid #e8e8e8;
	transition: all 0.2s;
}

.solution-item:active {
	background-color: #f0f0f0;
	transform: scale(0.98);
}

.solution-text {
	font-size: 13px;
	color: #333;
	flex: 1;
}

/* 处理记录 */
.record-list {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.record-item {
	padding: 12rpx;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	border-left: 3rpx solid #1890ff;
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 6rpx;
}

.record-time {
	font-size: 11px;
	color: #999;
}

.record-operator {
	font-size: 11px;
	color: #1890ff;
	font-weight: 500;
}

.record-content {
	font-size: 12px;
	color: #666;
	line-height: 1.4;
	display: block;
}

/* 操作按钮 */
.fault-operations {
	display: flex;
	gap: 12rpx;
	margin-top: 16rpx;
	padding-top: 16rpx;
	border-top: 1rpx solid #f0f0f0;
	flex-wrap: wrap;
}

/* 弹窗样式 */
.assign-modal,
.handle-modal,
.close-modal {
	background-color: #fff;
	border-radius: 16rpx 16rpx 0 0;
	padding: 0;
	max-height: 80vh;
	overflow: hidden;
}

.close-modal {
	border-radius: 16rpx;
	max-height: 60vh;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.modal-content {
	padding: 24rpx;
	max-height: 50vh;
	overflow-y: auto;
}

.modal-footer {
	display: flex;
	gap: 16rpx;
	padding: 24rpx;
	border-top: 1rpx solid #f0f0f0;
	justify-content: flex-end;
}

.form-item {
	margin-bottom: 24rpx;
}

.form-label {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	margin-bottom: 12rpx;
	display: block;
}

.close-tip {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
	margin-bottom: 20rpx;
	display: block;
}
</style>