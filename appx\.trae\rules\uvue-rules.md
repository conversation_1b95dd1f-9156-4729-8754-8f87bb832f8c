---
description: uvue 文件规则
globs: *.uvue
alwaysApply: false
---

# uvue 规则

## Vue 3 支持
- uvue 文件基于 Vue 3 语法
- 支持 Composition API
- 支持 `<script setup>` 语法
- 支持响应式系统

## 组件使用
```uvue
<template>
  <view class="container">
    <!-- 基础组件 -->
    <view class="content">
      <text class="title">{{ title }}</text>
      <image :src="imageUrl" class="image" />
    </view>
    
    <!-- 列表组件 -->
    <list-view class="list">
      <list-item v-for="item in list" :key="item.id">
        <text>{{ item.name }}</text>
      </list-item>
    </list-view>
    
    <!-- 滚动组件 -->
    <scroll-view scroll-y="true" class="scroll">
      <view v-for="item in scrollList" :key="item.id">
        {{ item.content }}
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const title = ref('页面标题')
const imageUrl = ref('/static/logo.png')
const list = ref([])
const scrollList = ref([])

// 计算属性
const computedValue = computed(() => {
  return list.value.length
})

// 生命周期
onMounted(() => {
  console.log('组件已挂载')
})
</script>

<style>
.container {
  flex: 1;
  padding: 20rpx;
}

.content {
  margin-bottom: 20rpx;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.image {
  width: 200rpx;
  height: 200rpx;
}

.list {
  flex: 1;
}

.scroll {
  height: 400rpx;
}
</style>
```

## 语法限制

### 支持的 Vue 3 特性
- Composition API
- `<script setup>` 语法
- 响应式 API (ref, reactive, computed, watch)
- 生命周期钩子
- 组件通信 (props, emit, provide/inject)
- 插槽 (slot)
- 指令 (v-if, v-for, v-show, v-model)

### 不支持的特性
- Teleport
- Suspense
- 部分高级指令
- 动态组件 (部分限制)
- 某些 Vue 3 高级特性

### 组件定义
```uvue
<template>
  <view class="custom-component">
    <text>{{ message }}</text>
    <button @click="handleClick">点击</button>
    <slot name="header"></slot>
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
// Props 定义
type Props = {
  message: string
  count?: number
}

const props = withDefaults(defineProps<Props>(), {
  count: 0
})

// Emits 定义
type Emits = {
  click: [value: string]
  update: [data: any]
}

const emit = defineEmits<Emits>()

// 事件处理
const handleClick = () => {
  emit('click', 'button clicked')
}

// 暴露给父组件的方法
defineExpose({
  handleClick
})
</script>
```

### 响应式系统
```uvue
<script lang="ts" setup>
import { ref, reactive, computed, watch, watchEffect } from 'vue'

// ref 响应式
const count = ref(0)
const message = ref('Hello')

// reactive 响应式
const state = reactive({
  user: {
    name: 'John',
    age: 30
  },
  list: []
})

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 侦听器
watch(count, (newValue, oldValue) => {
  console.log(`count changed from ${oldValue} to ${newValue}`)
})

// 立即执行的侦听器
watchEffect(() => {
  console.log(`Current count: ${count.value}`)
})

// 方法
const increment = () => {
  count.value++
}

const updateUser = (name: string) => {
  state.user.name = name
}
</script>
```

### 生命周期
```uvue
<script lang="ts" setup>
import { 
  onMounted, 
  onUnmounted, 
  onUpdated,
  onBeforeMount,
  onBeforeUnmount,
  onBeforeUpdate
} from 'vue'

// 挂载前
onBeforeMount(() => {
  console.log('组件即将挂载')
})

// 挂载后
onMounted(() => {
  console.log('组件已挂载')
  // 初始化数据、设置定时器等
})

// 更新前
onBeforeUpdate(() => {
  console.log('组件即将更新')
})

// 更新后
onUpdated(() => {
  console.log('组件已更新')
})

// 卸载前
onBeforeUnmount(() => {
  console.log('组件即将卸载')
  // 清理定时器、取消订阅等
})

// 卸载后
onUnmounted(() => {
  console.log('组件已卸载')
})
</script>
```

### 组件通信
```uvue
<!-- 父组件 -->
<template>
  <view>
    <child-component 
      :message="parentMessage"
      @child-event="handleChildEvent"
    >
      <template #header>
        <text>插槽内容</text>
      </template>
    </child-component>
  </view>
</template>

<script lang="ts" setup>
import { ref, provide } from 'vue'
import ChildComponent from './ChildComponent.uvue'

const parentMessage = ref('来自父组件的消息')

// 处理子组件事件
const handleChildEvent = (data: any) => {
  console.log('收到子组件事件:', data)
}

// 提供数据给后代组件
provide('globalData', {
  theme: 'dark',
  language: 'zh-CN'
})
</script>
```

```uvue
<!-- 子组件 -->
<template>
  <view class="child">
    <slot name="header"></slot>
    <text>{{ props.message }}</text>
    <text>{{ injectedData.theme }}</text>
    <button @click="sendEvent">发送事件</button>
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
import { inject } from 'vue'

// Props
type Props = {
  message: string
}

const props = defineProps<Props>()

// Emits
type Emits = {
  'child-event': [data: string]
}

const emit = defineEmits<Emits>()

// 注入父组件提供的数据
const injectedData = inject('globalData', { theme: 'light', language: 'en' })

// 发送事件给父组件
const sendEvent = () => {
  emit('child-event', '子组件数据')
}
</script>
```

### 条件渲染和列表渲染
```uvue
<template>
  <view>
    <!-- 条件渲染 -->
    <text v-if="showMessage">显示的消息</text>
    <text v-else-if="showAlternative">备选消息</text>
    <text v-else>默认消息</text>
    
    <!-- v-show -->
    <view v-show="isVisible" class="content">
      可见内容
    </view>
    
    <!-- 列表渲染 -->
    <view v-for="(item, index) in list" :key="item.id" class="item">
      <text>{{ index }}: {{ item.name }}</text>
    </view>
    
    <!-- 对象遍历 -->
    <view v-for="(value, key) in object" :key="key">
      <text>{{ key }}: {{ value }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const showMessage = ref(true)
const showAlternative = ref(false)
const isVisible = ref(true)

const list = ref([
  { id: 1, name: '项目1' },
  { id: 2, name: '项目2' },
  { id: 3, name: '项目3' }
])

const object = ref({
  name: 'John',
  age: 30,
  city: 'Beijing'
})
</script>
```

### 表单处理
```uvue
<template>
  <view class="form">
    <!-- 输入框 -->
    <input 
      v-model="formData.name" 
      placeholder="请输入姓名"
      class="input"
    />
    
    <!-- 文本域 -->
    <textarea 
      v-model="formData.description"
      placeholder="请输入描述"
      class="textarea"
    />
    
    <!-- 开关 -->
    <switch v-model="formData.enabled" />
    
    <!-- 滑块 -->
    <slider 
      v-model="formData.value"
      :min="0"
      :max="100"
      :step="1"
    />
    
    <button @click="submitForm">提交</button>
  </view>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'

const formData = reactive({
  name: '',
  description: '',
  enabled: false,
  value: 50
})

const submitForm = () => {
  console.log('表单数据:', formData)
}
</script>
```