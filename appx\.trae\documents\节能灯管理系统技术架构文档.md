# 节能灯管理系统技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[移动端APP - uni-app] --> B[负载均衡器]
    C[Web管理端 - Vue3] --> B
    B --> D[Admin.NET API网关]
    D --> E[设备管理服务]
    D --> F[照明控制服务]
    D --> G[能耗监控服务]
    D --> H[故障管理服务]
    D --> I[用户权限服务]
    
    E --> J[(MySQL数据库)]
    F --> J
    G --> J
    H --> J
    I --> J
    
    E --> K[Redis缓存]
    F --> K
    G --> K
    
    F --> L[MQTT消息队列]
    E --> L
    H --> L
    
    L --> M[IoT设备网关]
    M --> N[智能灯具设备]
    
    subgraph "前端层"
        A
        C
    end
    
    subgraph "API服务层"
        D
        E
        F
        G
        H
        I
    end
    
    subgraph "数据层"
        J
        K
    end
    
    subgraph "消息层"
        L
    end
    
    subgraph "设备层"
        M
        N
    end
```

## 2. 技术描述

* **前端Web管理端**：Vue3\@3.4 + Element Plus\@2.4 + TypeScript\@5.2 + Vite\@5.0

* **移动端APP**：uni-app\@3.0 + Vue3\@3.4 + cool-ui组件库

* **后端API**：Admin.NET (.NET 8) + Entity Framework Core\@8.0 + Furion框架

* **数据库**：MySQL\@8.0 (energy\_light) + Redis\@7.0

* **消息通信**：MQTT\@5.0 + SignalR\@8.0 实时推送

* **企业功能**：权限管理 + 代码生成 + 任务调度 + 多租户

* **部署**：Docker\@24.0 + Nginx\@1.24

## 3. 路由定义

### 3.1 Web前端路由 (Vue3 + Vue Router)

| 路由               | 用途                |
| ---------------- | ----------------- |
| /home            | 智能仪表板，显示系统概览和关键指标 |
| /system/device   | 设备管理中心，设备列表和控制操作  |
| /system/lighting | 智能照明控制，灯光调节和场景设置  |
| /system/energy   | 能耗监控分析，功耗统计和分析报表  |
| /system/fault    | 故障管理系统，告警处理和维修记录  |
| /system/user     | 用户管理，账号权限和角色配置    |
| /system/org      | 组织架构管理，部门和人员配置    |
| /system/menu     | 菜单配置，动态菜单和权限设置    |
| /system/config   | 系统配置，参数设置和代码生成    |
| /system/job      | 任务调度，定时任务和作业管理    |
| /login           | 用户登录页面，支持多种认证方式   |

### 3.2 移动端路由 (uni-app)

| 路由                | 用途           |
| ----------------- | ------------ |
| /pages/index/home | 移动端首页，设备状态概览 |
| /pages/index/my   | 个人中心，用户信息和设置 |
| /pages/user/login | 移动端登录页面      |
| /pages/set/index  | 设置页面，应用配置选项  |

## 4. API定义

### 4.1 核心API

**用户认证相关**

```
POST /api/auth/login
```

请求参数：

| 参数名      | 参数类型   | 是否必填 | 描述   |
| -------- | ------ | ---- | ---- |
| account  | string | 是    | 用户账号 |
| password | string | 是    | 密码   |
| captcha  | string | 否    | 验证码  |

响应参数：

| 参数名                 | 参数类型   | 描述      |
| ------------------- | ------ | ------- |
| code                | int    | 响应状态码   |
| message             | string | 响应消息    |
| result              | object | 响应数据    |
| result.accessToken  | string | JWT访问令牌 |
| result.refreshToken | string | 刷新令牌    |
| result.userInfo     | object | 用户基本信息  |

**设备管理相关**

```
GET /api/device/page          # 分页查询设备列表
POST /api/device/add          # 添加新设备
PUT /api/device/update        # 更新设备信息
DELETE /api/device/delete     # 删除设备
GET /api/device/detail/:id    # 获取设备详情
POST /api/device/batch        # 批量操作设备
```

**照明控制相关**

```
POST /api/lighting/control    # 设备控制指令
GET /api/lighting/status/:id  # 获取设备状态
POST /api/lighting/schedule   # 创建定时任务
GET /api/lighting/scene       # 获取情景模式
POST /api/lighting/batch      # 批量控制设备
```

**能耗监控相关**

```
GET /api/energy/realtime      # 实时功耗数据
GET /api/energy/history       # 历史能耗统计
GET /api/energy/report        # 生成能耗报表
GET /api/energy/analysis      # 能耗分析预测
```

**故障管理相关**

```
GET /api/fault/page           # 分页查询故障告警
POST /api/fault/handle        # 处理故障工单
GET /api/fault/maintenance    # 维修记录查询
POST /api/fault/warning       # 预警规则配置
```

示例请求：

```json
{
  "account": "admin",
  "password": "******",
  "captcha": "1234"
}
```

示例响应：

```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "userInfo": {
    "id": 1,
    "username": "admin",
    "role": "系统管理员"
  }
}
```

## 5. 服务架构图

```mermaid
graph TD
    A[客户端请求] --> B[API网关层]
    B --> C[身份验证中间件]
    C --> D[控制器层]
    D --> E[业务服务层]
    E --> F[数据访问层]
    F --> G[(数据库)]
    
    E --> H[缓存服务]
    E --> I[消息服务]
    E --> J[设备通信服务]
    
    subgraph "Admin.NET后端服务"
        B
        C
        D
        E
        F
        H
        I
        J
    end
```

## 6. 数据模型

### 6.1 数据库配置

* **数据库类型**: MySQL 8.0

* **数据库名**: energy\_light

* **连接字符串**: `Server=localhost;Database=energy_light;Uid=root;Pwd=******;SslMode=None;AllowLoadLocalInfile=true;AllowUserVariables=true;`

* **ORM框架**: SqlSugar

* **实体基类**: Admin.NET框架提供多层次实体基类支持

### 6.2 Admin.NET框架实体基类结构

```csharp
// 基础实体类
public abstract class EntityBase
{
    public long Id { get; set; }                    // 主键ID
    public DateTime CreateTime { get; set; }        // 创建时间
    public DateTime? UpdateTime { get; set; }       // 更新时间
    public long? CreateUserId { get; set; }         // 创建用户ID
    public long? UpdateUserId { get; set; }         // 更新用户ID
}

// 租户实体基类
public abstract class EntityBaseTenant : EntityBase
{
    public long? TenantId { get; set; }             // 租户ID
}

// 机构实体基类（数据权限）
public abstract class EntityBaseOrg : EntityBase
{
    public long OrgId { get; set; }                 // 机构ID
}

// 软删除实体基类
public abstract class EntityBaseDel : EntityBase
{
    public bool IsDelete { get; set; }              // 删除标志
}
```

### 6.3 数据模型定义

```mermaid
erDiagram
  %% 系统核心表（Admin.NET框架）
  SysUser ||--o{ SysUserRole : "用户角色关联"
  SysRole ||--o{ SysUserRole : "角色用户关联"
  SysRole ||--o{ SysRoleMenu : "角色菜单关联"
  SysMenu ||--o{ SysRoleMenu : "菜单角色关联"
  SysOrg ||--o{ SysUser : "用户所属机构"
  SysTenant ||--o{ SysUser : "租户用户关联"
  
  %% 节能灯管理业务表
  EnergyDevice ||--o{ EnergyDeviceGroup : "设备分组"
  EnergyDevice ||--o{ EnergyConsumption : "设备能耗"
  EnergyDevice ||--o{ EnergyFault : "设备故障"
  EnergyDevice ||--o{ EnergyControl : "设备控制记录"
  EnergyScene ||--o{ EnergySceneDevice : "场景设备关联"
  EnergyDevice ||--o{ EnergySceneDevice : "设备场景关联"
  
  %% 系统核心实体
  SysUser {
      bigint Id PK
      string Account
      string Password
      string RealName
      string Phone
      string Email
      int Status
      bigint OrgId FK
      bigint TenantId FK
      datetime CreateTime
      datetime UpdateTime
  }
  
  SysRole {
      bigint Id PK
      string Name
      string Code
      int DataScope
      int Status
      bigint TenantId FK
      datetime CreateTime
  }
  
  SysMenu {
      bigint Id PK
      bigint Pid
      string Title
      string Path
      string Component
      string Permission
      int Type
      int Status
      datetime CreateTime
  }
  
  %% 节能灯管理业务实体
  EnergyDevice {
      bigint Id PK
      string DeviceCode
      string DeviceName
      string DeviceType
      string Location
      string IpAddress
      int Status
      decimal PowerRating
      bigint GroupId FK
      bigint OrgId FK
      bigint TenantId FK
      datetime CreateTime
      datetime UpdateTime
  }
  
  EnergyDeviceGroup {
      bigint Id PK
      string GroupName
      string Description
      bigint ParentId
      bigint OrgId FK
      bigint TenantId FK
      datetime CreateTime
  }
  
  EnergyConsumption {
      bigint Id PK
      bigint DeviceId FK
      decimal PowerConsumption
      decimal Voltage
      decimal Current
      datetime RecordTime
      bigint TenantId FK
      datetime CreateTime
  }
  
  EnergyFault {
      bigint Id PK
      bigint DeviceId FK
      string FaultType
      string FaultDescription
      int FaultLevel
      int Status
      datetime FaultTime
      datetime RepairTime
      bigint RepairUserId FK
      bigint TenantId FK
      datetime CreateTime
  }
  
  EnergyControl {
      bigint Id PK
      bigint DeviceId FK
      string ControlType
      string ControlValue
      bigint OperatorId FK
      datetime ControlTime
      bigint TenantId FK
      datetime CreateTime
  }
  
  EnergyScene {
      bigint Id PK
      string SceneName
      string Description
      string SceneConfig
      int Status
      bigint OrgId FK
      bigint TenantId FK
      datetime CreateTime
  }
```

### 6.4 数据定义语言（DDL）

基于Admin.NET框架的实体基类设计，节能灯管理系统的核心业务表创建语句：

#### 设备管理表

```sql
-- 设备分组表
CREATE TABLE energy_device_group (
    Id BIGINT PRIMARY KEY AUTO_INCREMENT,
    GroupName VARCHAR(64) NOT NULL COMMENT '分组名称',
    Description VARCHAR(256) COMMENT '分组描述',
    ParentId BIGINT DEFAULT 0 COMMENT '父分组ID',
    OrderNo INT DEFAULT 100 COMMENT '排序',
    Status INT DEFAULT 1 COMMENT '状态：1启用 2禁用',
    OrgId BIGINT COMMENT '机构ID',
    TenantId BIGINT COMMENT '租户ID',
    CreateTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UpdateTime DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    CreateUserId BIGINT COMMENT '创建用户ID',
    UpdateUserId BIGINT COMMENT '更新用户ID',
    IsDelete TINYINT(1) DEFAULT 0 COMMENT '删除标志'
) COMMENT='设备分组表';

-- 设备信息表
CREATE TABLE energy_device (
    Id BIGINT PRIMARY KEY AUTO_INCREMENT,
    DeviceCode VARCHAR(32) UNIQUE NOT NULL COMMENT '设备编码',
    DeviceName VARCHAR(64) NOT NULL COMMENT '设备名称',
    DeviceType VARCHAR(32) NOT NULL COMMENT '设备类型',
    DeviceModel VARCHAR(64) COMMENT '设备型号',
    Location VARCHAR(128) COMMENT '设备位置',
    IpAddress VARCHAR(15) COMMENT 'IP地址',
    MacAddress VARCHAR(17) COMMENT 'MAC地址',
    PowerRating DECIMAL(10,2) COMMENT '额定功率(W)',
    Voltage DECIMAL(10,2) COMMENT '额定电压(V)',
    InstallDate DATE COMMENT '安装日期',
    Status INT DEFAULT 1 COMMENT '设备状态：1正常 2故障 3离线 4维护',
    GroupId BIGINT COMMENT '设备分组ID',
    OrgId BIGINT COMMENT '机构ID',
    TenantId BIGINT COMMENT '租户ID',
    CreateTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UpdateTime DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    CreateUserId BIGINT COMMENT '创建用户ID',
    UpdateUserId BIGINT COMMENT '更新用户ID',
    IsDelete TINYINT(1) DEFAULT 0 COMMENT '删除标志',
    INDEX idx_device_code (DeviceCode),
    INDEX idx_device_status (Status),
    INDEX idx_device_group (GroupId),
    INDEX idx_device_org (OrgId)
) COMMENT='设备信息表';

-- 能耗监控表
CREATE TABLE energy_consumption (
    Id BIGINT PRIMARY KEY AUTO_INCREMENT,
    DeviceId BIGINT NOT NULL COMMENT '设备ID',
    PowerConsumption DECIMAL(10,4) NOT NULL COMMENT '功耗(kWh)',
    Voltage DECIMAL(8,2) COMMENT '电压(V)',
    Current DECIMAL(8,3) COMMENT '电流(A)',
    PowerFactor DECIMAL(4,3) COMMENT '功率因数',
    Temperature DECIMAL(5,2) COMMENT '温度(°C)',
    Humidity DECIMAL(5,2) COMMENT '湿度(%)',
    RecordTime DATETIME NOT NULL COMMENT '记录时间',
    TenantId BIGINT COMMENT '租户ID',
    CreateTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_consumption_device (DeviceId),
    INDEX idx_consumption_time (RecordTime DESC),
    INDEX idx_consumption_tenant (TenantId)
) COMMENT='能耗监控表';

-- 设备故障表
CREATE TABLE energy_fault (
    Id BIGINT PRIMARY KEY AUTO_INCREMENT,
    DeviceId BIGINT NOT NULL COMMENT '设备ID',
    FaultCode VARCHAR(32) COMMENT '故障代码',
    FaultType VARCHAR(32) NOT NULL COMMENT '故障类型',
    FaultDescription TEXT COMMENT '故障描述',
    FaultLevel INT DEFAULT 1 COMMENT '故障等级：1一般 2重要 3紧急',
    Status INT DEFAULT 1 COMMENT '处理状态：1待处理 2处理中 3已解决 4已关闭',
    FaultTime DATETIME NOT NULL COMMENT '故障发生时间',
    RepairTime DATETIME COMMENT '维修时间',
    RepairUserId BIGINT COMMENT '维修人员ID',
    RepairDescription TEXT COMMENT '维修说明',
    TenantId BIGINT COMMENT '租户ID',
    CreateTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UpdateTime DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    CreateUserId BIGINT COMMENT '创建用户ID',
    UpdateUserId BIGINT COMMENT '更新用户ID',
    INDEX idx_fault_device (DeviceId),
    INDEX idx_fault_status (Status),
    INDEX idx_fault_level (FaultLevel),
    INDEX idx_fault_time (FaultTime DESC)
) COMMENT='设备故障表';

-- 设备控制记录表
CREATE TABLE energy_control (
    Id BIGINT PRIMARY KEY AUTO_INCREMENT,
    DeviceId BIGINT NOT NULL COMMENT '设备ID',
    ControlType VARCHAR(32) NOT NULL COMMENT '控制类型：switch开关 brightness亮度 scene场景',
    ControlValue VARCHAR(128) NOT NULL COMMENT '控制值',
    ControlResult VARCHAR(32) COMMENT '控制结果：success成功 failed失败',
    OperatorId BIGINT NOT NULL COMMENT '操作人员ID',
    ControlTime DATETIME NOT NULL COMMENT '控制时间',
    Remark VARCHAR(256) COMMENT '备注',
    TenantId BIGINT COMMENT '租户ID',
    CreateTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_control_device (DeviceId),
    INDEX idx_control_operator (OperatorId),
    INDEX idx_control_time (ControlTime DESC)
) COMMENT='设备控制记录表';

-- 场景管理表
CREATE TABLE energy_scene (
    Id BIGINT PRIMARY KEY AUTO_INCREMENT,
    SceneName VARCHAR(64) NOT NULL COMMENT '场景名称',
    SceneType VARCHAR(32) DEFAULT 'custom' COMMENT '场景类型：preset预设 custom自定义',
    Description VARCHAR(256) COMMENT '场景描述',
    SceneConfig JSON COMMENT '场景配置(JSON格式)',
    Status INT DEFAULT 1 COMMENT '状态：1启用 2禁用',
    OrderNo INT DEFAULT 100 COMMENT '排序',
    OrgId BIGINT COMMENT '机构ID',
    TenantId BIGINT COMMENT '租户ID',
    CreateTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UpdateTime DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    CreateUserId BIGINT COMMENT '创建用户ID',
    UpdateUserId BIGINT COMMENT '更新用户ID',
    IsDelete TINYINT(1) DEFAULT 0 COMMENT '删除标志',
    INDEX idx_scene_status (Status),
    INDEX idx_scene_org (OrgId)
) COMMENT='场景管理表';

-- 场景设备关联表
CREATE TABLE energy_scene_device (
    Id BIGINT PRIMARY KEY AUTO_INCREMENT,
    SceneId BIGINT NOT NULL COMMENT '场景ID',
    DeviceId BIGINT NOT NULL COMMENT '设备ID',
    DeviceConfig JSON COMMENT '设备在场景中的配置',
    CreateTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_scene_device (SceneId, DeviceId),
    INDEX idx_scene_device_scene (SceneId),
    INDEX idx_scene_device_device (DeviceId)
) COMMENT='场景设备关联表';
```

#### 初始化数据

```sql
-- 初始化设备分组数据
INSERT INTO energy_device_group (Id, GroupName, Description, ParentId, OrderNo, Status, CreateTime) VALUES
(1, '办公区域', '办公楼照明设备', 0, 1, 1, NOW()),
(2, '生产区域', '生产车间照明设备', 0, 2, 1, NOW()),
(3, '公共区域', '走廊、楼梯等公共照明', 0, 3, 1, NOW()),
(4, '室外区域', '室外景观和安全照明', 0, 4, 1, NOW());

-- 初始化预设场景数据
INSERT INTO energy_scene (Id, SceneName, SceneType, Description, SceneConfig, Status, CreateTime) VALUES
(1, '节能模式', 'preset', '降低亮度，节约能源', '{"brightness": 60, "autoOff": true}', 1, NOW()),
(2, '正常模式', 'preset', '正常工作照明', '{"brightness": 100, "autoOff": false}', 1, NOW()),
(3, '夜间模式', 'preset', '夜间值班照明', '{"brightness": 30, "autoOff": true}', 1, NOW()),
(4, '应急模式', 'preset', '应急情况全开照明', '{"brightness": 100, "emergency": true}', 1, NOW());
```

