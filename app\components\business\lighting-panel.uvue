<template>
	<view class="lighting-panel">
		<!-- 设备信息头部 -->
		<view class="panel-header">
			<view class="device-info">
				<view class="device-avatar">
					<cl-icon name="cl-icon-bulb" size="24" color="#1890ff" />
				</view>
				<view class="device-details">
					<text class="device-name">{{ deviceInfo.name }}</text>
					<text class="device-location">{{ deviceInfo.location }}</text>
				</view>
			</view>
			
			<view class="device-status">
				<status-indicator 
					:status="deviceInfo.status" 
					:size="'small'"
				/>
			</view>
		</view>
		
		<!-- 主控制区域 -->
		<view class="main-controls">
			<!-- 电源开关 -->
			<view class="power-control">
				<view class="control-header">
					<cl-icon name="cl-icon-power" size="20" color="#666" />
					<text class="control-title">电源控制</text>
				</view>
				
				<view class="power-switch">
					<cl-switch 
						v-model="controls.power" 
						size="large"
						@change="handlePowerChange"
					/>
					<text class="switch-label">{{ controls.power ? '开启' : '关闭' }}</text>
				</view>
			</view>
			
			<!-- 亮度控制 -->
			<view class="brightness-control" :class="{ disabled: !controls.power }">
				<view class="control-header">
					<cl-icon name="cl-icon-sun" size="20" color="#666" />
					<text class="control-title">亮度调节</text>
					<text class="brightness-value">{{ controls.brightness }}%</text>
				</view>
				
				<view class="brightness-slider">
					<cl-slider 
						v-model="controls.brightness"
						:min="1"
						:max="100"
						:disabled="!controls.power"
						@change="handleBrightnessChange"
					/>
				</view>
				
				<view class="brightness-presets">
					<view 
						v-for="preset in brightnessPresets" 
						:key="preset.value"
						class="preset-btn"
						:class="{ active: controls.brightness === preset.value, disabled: !controls.power }"
						@click="setBrightness(preset.value)"
					>
						<text class="preset-text">{{ preset.label }}</text>
					</view>
				</view>
			</view>
			
			<!-- 色温控制 -->
			<view class="temperature-control" :class="{ disabled: !controls.power }">
				<view class="control-header">
					<cl-icon name="cl-icon-thermometer" size="20" color="#666" />
					<text class="control-title">色温调节</text>
					<text class="temperature-value">{{ controls.temperature }}K</text>
				</view>
				
				<view class="temperature-slider">
					<cl-slider 
						v-model="controls.temperature"
						:min="2700"
						:max="6500"
						:disabled="!controls.power"
						@change="handleTemperatureChange"
					/>
				</view>
				
				<view class="temperature-labels">
					<text class="temp-label warm">暖光</text>
					<text class="temp-label cool">冷光</text>
				</view>
			</view>
			
			<!-- 颜色控制 -->
			<view v-if="deviceInfo.supportColor" class="color-control" :class="{ disabled: !controls.power }">
				<view class="control-header">
					<cl-icon name="cl-icon-palette" size="20" color="#666" />
					<text class="control-title">颜色选择</text>
				</view>
				
				<view class="color-picker">
					<view class="color-wheel">
						<view 
							v-for="(color, index) in colorPresets" 
							:key="index"
							class="color-item"
							:class="{ active: controls.color === color.value, disabled: !controls.power }"
							:style="{ backgroundColor: color.hex }"
							@click="setColor(color.value, color.hex)"
						>
							<cl-icon v-if="controls.color === color.value" name="cl-icon-check" size="16" color="white" />
						</view>
					</view>
					
					<view class="custom-color">
						<text class="custom-label">自定义颜色</text>
						<view class="color-input" :style="{ backgroundColor: controls.colorHex }" @click="openColorPicker">
							<cl-icon name="cl-icon-edit" size="14" color="white" />
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 场景模式 -->
		<view class="scene-modes">
			<view class="control-header">
				<cl-icon name="cl-icon-magic-wand" size="20" color="#666" />
				<text class="control-title">场景模式</text>
			</view>
			
			<view class="scene-list">
				<view 
					v-for="scene in scenePresets" 
					:key="scene.id"
					class="scene-item"
					:class="{ active: controls.scene === scene.id, disabled: !controls.power }"
					@click="setScene(scene)"
				>
					<view class="scene-icon">
						<cl-icon :name="scene.icon" size="20" :color="controls.scene === scene.id ? '#1890ff' : '#666'" />
					</view>
					<view class="scene-info">
						<text class="scene-name">{{ scene.name }}</text>
						<text class="scene-desc">{{ scene.description }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 定时控制 -->
		<view class="timer-control">
			<view class="control-header">
				<cl-icon name="cl-icon-clock" size="20" color="#666" />
				<text class="control-title">定时控制</text>
				<cl-switch v-model="controls.timerEnabled" size="small" @change="handleTimerToggle" />
			</view>
			
			<view v-if="controls.timerEnabled" class="timer-settings">
				<view class="timer-item">
					<text class="timer-label">开启时间</text>
					<view class="timer-picker" @click="pickTime('on')">
						<text class="timer-time">{{ controls.timerOn || '未设置' }}</text>
						<cl-icon name="cl-icon-chevron-right" size="16" color="#999" />
					</view>
				</view>
				
				<view class="timer-item">
					<text class="timer-label">关闭时间</text>
					<view class="timer-picker" @click="pickTime('off')">
						<text class="timer-time">{{ controls.timerOff || '未设置' }}</text>
						<cl-icon name="cl-icon-chevron-right" size="16" color="#999" />
					</view>
				</view>
				
				<view class="timer-repeat">
					<text class="timer-label">重复</text>
					<view class="repeat-days">
						<view 
							v-for="(day, index) in weekDays" 
							:key="index"
							class="day-item"
							:class="{ active: controls.repeatDays.includes(index) }"
							@click="toggleRepeatDay(index)"
						>
							<text class="day-text">{{ day }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="action-buttons">
			<cl-button 
				type="primary" 
				size="large" 
				:loading="saving"
				@click="saveSettings"
			>
				保存设置
			</cl-button>
			
			<cl-button 
				type="default" 
				size="large" 
				@click="resetSettings"
			>
				重置
			</cl-button>
		</view>
		
		<!-- 时间选择器弹窗 -->
		<cl-popup v-model="showTimePicker" direction="bottom">
			<view class="time-picker-modal">
				<view class="modal-header">
					<text class="modal-title">选择时间</text>
					<cl-button text @click="showTimePicker = false">取消</cl-button>
				</view>
				
				<picker-view 
					v-model="pickerValue" 
					class="time-picker"
					@change="handlePickerChange"
				>
					<picker-view-column>
						<view v-for="hour in 24" :key="hour - 1" class="picker-item">
							{{ (hour - 1).toString().padStart(2, '0') }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="minute in 60" :key="minute - 1" class="picker-item">
							{{ (minute - 1).toString().padStart(2, '0') }}
						</view>
					</picker-view-column>
				</picker-view>
				
				<view class="modal-actions">
					<cl-button type="primary" @click="confirmTime">确定</cl-button>
				</view>
			</view>
		</cl-popup>
	</view>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from "vue";
import StatusIndicator from "./status-indicator.uvue";

// Props
interface DeviceInfo {
	id: string;
	name: string;
	location: string;
	status: 'online' | 'offline' | 'fault';
	supportColor?: boolean;
}

interface LightingControls {
	power: boolean;
	brightness: number;
	temperature: number;
	color?: string;
	colorHex?: string;
	scene?: string;
	timerEnabled: boolean;
	timerOn?: string;
	timerOff?: string;
	repeatDays: number[];
}

interface LightingPanelProps {
	deviceInfo: DeviceInfo;
	initialControls?: Partial<LightingControls>;
	readonly?: boolean;
}

const props = withDefaults(defineProps<LightingPanelProps>(), {
	readonly: false
});

// Emits
const emit = defineEmits<{
	controlChange: [controls: LightingControls];
	save: [controls: LightingControls];
	reset: [];
}>();

// 响应式数据
const saving = ref(false);
const showTimePicker = ref(false);
const currentTimerType = ref<'on' | 'off'>('on');
const pickerValue = ref([0, 0]);

// 控制状态
const controls = ref<LightingControls>({
	power: false,
	brightness: 50,
	temperature: 4000,
	color: 'white',
	colorHex: '#ffffff',
	scene: 'normal',
	timerEnabled: false,
	repeatDays: [],
	...props.initialControls
});

// 亮度预设
const brightnessPresets = [
	{ label: "低", value: 25 },
	{ label: "中", value: 50 },
	{ label: "高", value: 75 },
	{ label: "最高", value: 100 }
];

// 颜色预设
const colorPresets = [
	{ name: "白色", value: "white", hex: "#ffffff" },
	{ name: "红色", value: "red", hex: "#ff4d4f" },
	{ name: "绿色", value: "green", hex: "#52c41a" },
	{ name: "蓝色", value: "blue", hex: "#1890ff" },
	{ name: "黄色", value: "yellow", hex: "#fadb14" },
	{ name: "紫色", value: "purple", hex: "#722ed1" },
	{ name: "橙色", value: "orange", hex: "#fa8c16" },
	{ name: "青色", value: "cyan", hex: "#13c2c2" }
];

// 场景预设
const scenePresets = [
	{ id: "normal", name: "普通模式", description: "日常照明", icon: "cl-icon-sun" },
	{ id: "reading", name: "阅读模式", description: "适合阅读", icon: "cl-icon-book" },
	{ id: "sleep", name: "睡眠模式", description: "温和光线", icon: "cl-icon-moon" },
	{ id: "party", name: "聚会模式", description: "多彩灯光", icon: "cl-icon-music" },
	{ id: "work", name: "工作模式", description: "专注照明", icon: "cl-icon-briefcase" },
	{ id: "relax", name: "放松模式", description: "舒缓氛围", icon: "cl-icon-heart" }
];

// 星期
const weekDays = ["日", "一", "二", "三", "四", "五", "六"];

// 电源控制
const handlePowerChange = (value: boolean) => {
	controls.value.power = value;
	if (!value) {
		// 关闭电源时重置一些设置
		controls.value.scene = 'normal';
	}
	emitControlChange();
};

// 亮度控制
const handleBrightnessChange = (value: number) => {
	controls.value.brightness = value;
	emitControlChange();
};

const setBrightness = (value: number) => {
	if (!controls.value.power) return;
	controls.value.brightness = value;
	emitControlChange();
};

// 色温控制
const handleTemperatureChange = (value: number) => {
	controls.value.temperature = value;
	emitControlChange();
};

// 颜色控制
const setColor = (color: string, hex: string) => {
	if (!controls.value.power) return;
	controls.value.color = color;
	controls.value.colorHex = hex;
	emitControlChange();
};

const openColorPicker = () => {
	if (!controls.value.power) return;
	// 这里可以集成颜色选择器
	uni.showToast({
		title: "颜色选择器开发中",
		icon: "none"
	});
};

// 场景控制
const setScene = (scene: any) => {
	if (!controls.value.power) return;
	controls.value.scene = scene.id;
	
	// 根据场景设置相应的参数
	switch (scene.id) {
		case 'reading':
			controls.value.brightness = 80;
			controls.value.temperature = 4000;
			break;
		case 'sleep':
			controls.value.brightness = 20;
			controls.value.temperature = 2700;
			break;
		case 'work':
			controls.value.brightness = 90;
			controls.value.temperature = 5000;
			break;
		case 'relax':
			controls.value.brightness = 40;
			controls.value.temperature = 3000;
			break;
	}
	
	emitControlChange();
};

// 定时控制
const handleTimerToggle = (enabled: boolean) => {
	controls.value.timerEnabled = enabled;
	emitControlChange();
};

const pickTime = (type: 'on' | 'off') => {
	currentTimerType.value = type;
	
	// 设置当前时间为默认值
	const currentTime = type === 'on' ? controls.value.timerOn : controls.value.timerOff;
	if (currentTime) {
		const [hour, minute] = currentTime.split(':').map(Number);
		pickerValue.value = [hour, minute];
	} else {
		const now = new Date();
		pickerValue.value = [now.getHours(), now.getMinutes()];
	}
	
	showTimePicker.value = true;
};

const handlePickerChange = (e: any) => {
	pickerValue.value = e.detail.value;
};

const confirmTime = () => {
	const [hour, minute] = pickerValue.value;
	const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
	
	if (currentTimerType.value === 'on') {
		controls.value.timerOn = timeString;
	} else {
		controls.value.timerOff = timeString;
	}
	
	showTimePicker.value = false;
	emitControlChange();
};

const toggleRepeatDay = (dayIndex: number) => {
	const index = controls.value.repeatDays.indexOf(dayIndex);
	if (index > -1) {
		controls.value.repeatDays.splice(index, 1);
	} else {
		controls.value.repeatDays.push(dayIndex);
	}
	emitControlChange();
};

// 保存设置
const saveSettings = async () => {
	if (props.readonly) return;
	
	saving.value = true;
	try {
		// 模拟保存延迟
		await new Promise(resolve => setTimeout(resolve, 1000));
		
		emit('save', { ...controls.value });
		
		uni.showToast({
			title: "设置已保存",
			icon: "success"
		});
	} catch (error) {
		uni.showToast({
			title: "保存失败",
			icon: "error"
		});
	} finally {
		saving.value = false;
	}
};

// 重置设置
const resetSettings = () => {
	uni.showModal({
		title: "确认重置",
		content: "是否重置所有设置到默认值？",
		success: (res) => {
			if (res.confirm) {
				controls.value = {
					power: false,
					brightness: 50,
					temperature: 4000,
					color: 'white',
					colorHex: '#ffffff',
					scene: 'normal',
					timerEnabled: false,
					repeatDays: []
				};
				
				emit('reset');
				emitControlChange();
				
				uni.showToast({
					title: "已重置",
					icon: "success"
				});
			}
		}
	});
};

// 发送控制变化事件
const emitControlChange = () => {
	emit('controlChange', { ...controls.value });
};

// 监听初始控制参数变化
watch(() => props.initialControls, (newControls) => {
	if (newControls) {
		controls.value = { ...controls.value, ...newControls };
	}
}, { deep: true });

onMounted(() => {
	// 组件挂载后的初始化逻辑
});
</script>

<style scoped>
.lighting-panel {
	background-color: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.panel-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.device-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.device-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background-color: #e6f7ff;
	display: flex;
	align-items: center;
	justify-content: center;
}

.device-details {
	display: flex;
	flex-direction: column;
	gap: 5rpx;
}

.device-name {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.device-location {
	font-size: 12px;
	color: #666;
}

.main-controls {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
	margin-bottom: 30rpx;
}

.power-control,
.brightness-control,
.temperature-control,
.color-control {
	padding: 25rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
	transition: all 0.3s ease;
}

.power-control.disabled,
.brightness-control.disabled,
.temperature-control.disabled,
.color-control.disabled {
	opacity: 0.5;
	pointer-events: none;
}

.control-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 20rpx;
}

.control-title {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	flex: 1;
}

.brightness-value,
.temperature-value {
	font-size: 12px;
	color: #1890ff;
	font-weight: 500;
}

.power-switch {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.switch-label {
	font-size: 14px;
	color: #333;
	font-weight: 500;
}

.brightness-slider,
.temperature-slider {
	margin-bottom: 20rpx;
}

.brightness-presets {
	display: flex;
	gap: 15rpx;
}

.preset-btn {
	flex: 1;
	padding: 15rpx;
	border-radius: 8rpx;
	background-color: white;
	border: 1px solid #d9d9d9;
	text-align: center;
	transition: all 0.3s ease;
}

.preset-btn.active {
	background-color: #1890ff;
	border-color: #1890ff;
}

.preset-btn.active .preset-text {
	color: white;
}

.preset-btn.disabled {
	opacity: 0.5;
	pointer-events: none;
}

.preset-text {
	font-size: 12px;
	color: #666;
	font-weight: 500;
}

.temperature-labels {
	display: flex;
	justify-content: space-between;
	margin-top: 10rpx;
}

.temp-label {
	font-size: 12px;
	color: #666;
}

.temp-label.warm {
	color: #fa8c16;
}

.temp-label.cool {
	color: #1890ff;
}

.color-picker {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.color-wheel {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.color-item {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	border: 3rpx solid transparent;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	cursor: pointer;
}

.color-item.active {
	border-color: #333;
	transform: scale(1.1);
}

.color-item.disabled {
	opacity: 0.5;
	pointer-events: none;
}

.custom-color {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.custom-label {
	font-size: 14px;
	color: #333;
}

.color-input {
	width: 50rpx;
	height: 50rpx;
	border-radius: 8rpx;
	border: 1px solid #d9d9d9;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.scene-modes {
	margin-bottom: 30rpx;
}

.scene-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	margin-top: 20rpx;
}

.scene-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	background-color: #f9f9f9;
	border: 1px solid transparent;
	transition: all 0.3s ease;
	cursor: pointer;
}

.scene-item.active {
	background-color: #e6f7ff;
	border-color: #1890ff;
}

.scene-item.disabled {
	opacity: 0.5;
	pointer-events: none;
}

.scene-icon {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	background-color: white;
	display: flex;
	align-items: center;
	justify-content: center;
}

.scene-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 5rpx;
}

.scene-name {
	font-size: 14px;
	font-weight: 500;
	color: #333;
}

.scene-desc {
	font-size: 12px;
	color: #666;
}

.timer-control {
	margin-bottom: 30rpx;
}

.timer-settings {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-top: 20rpx;
	padding: 25rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
}

.timer-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.timer-label {
	font-size: 14px;
	color: #333;
}

.timer-picker {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 10rpx 15rpx;
	border-radius: 8rpx;
	background-color: white;
	border: 1px solid #d9d9d9;
	cursor: pointer;
}

.timer-time {
	font-size: 14px;
	color: #333;
}

.timer-repeat {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.repeat-days {
	display: flex;
	gap: 10rpx;
}

.day-item {
	width: 50rpx;
	height: 50rpx;
	border-radius: 50%;
	background-color: white;
	border: 1px solid #d9d9d9;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	cursor: pointer;
}

.day-item.active {
	background-color: #1890ff;
	border-color: #1890ff;
}

.day-item.active .day-text {
	color: white;
}

.day-text {
	font-size: 12px;
	color: #666;
	font-weight: 500;
}

.action-buttons {
	display: flex;
	gap: 20rpx;
}

.action-buttons .cl-button {
	flex: 1;
}

.time-picker-modal {
	background-color: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 30rpx;
	max-height: 60vh;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.modal-title {
	font-size: 18px;
	font-weight: 500;
	color: #333;
}

.time-picker {
	height: 300rpx;
	margin-bottom: 30rpx;
}

.picker-item {
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
	color: #333;
}

.modal-actions {
	display: flex;
	justify-content: center;
}
</style>