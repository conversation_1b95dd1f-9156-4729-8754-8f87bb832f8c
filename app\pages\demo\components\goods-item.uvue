<template>
	<view class="p-3 pb-0">
		<view class="w-full p-3 bg-white rounded-xl dark:bg-surface-800">
			<cl-image :src="item?.image" mode="aspectFill" width="100%" height="280rpx"></cl-image>
			<cl-text :pt="{ className: 'mt-2' }">{{ item?.title }}</cl-text>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { parse } from "@/cool";

defineOptions({
	name: "goods-item"
});

type GoodsItem = {
	id: number;
	title: string;
	image: string;
};

const props = defineProps({
	value: {
		type: Object,
		default: () => ({})
	}
});

const item = computed(() => parse<GoodsItem>(props.value));
</script>
