<template>
  <view class="cl-app">
    <slot />
  </view>
</template>

<script lang="ts" setup>
// 应用根组件，提供全局样式和配置
defineOptions({
  name: 'ClApp'
})
</script>

<style>
.cl-app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

/* 全局样式重置 */
.cl-app * {
  box-sizing: border-box;
}

.cl-app view,
.cl-app text,
.cl-app button,
.cl-app input {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 通用工具类 */
.cl-app .flex {
  display: flex;
}

.cl-app .flex-1 {
  flex: 1;
}

.cl-app .flex-column {
  flex-direction: column;
}

.cl-app .flex-row {
  flex-direction: row;
}

.cl-app .justify-center {
  justify-content: center;
}

.cl-app .justify-between {
  justify-content: space-between;
}

.cl-app .align-center {
  align-items: center;
}

.cl-app .text-center {
  text-align: center;
}

.cl-app .text-left {
  text-align: left;
}

.cl-app .text-right {
  text-align: right;
}

/* 间距工具类 */
.cl-app .m-0 { margin: 0; }
.cl-app .m-1 { margin: 10rpx; }
.cl-app .m-2 { margin: 20rpx; }
.cl-app .m-3 { margin: 30rpx; }

.cl-app .p-0 { padding: 0; }
.cl-app .p-1 { padding: 10rpx; }
.cl-app .p-2 { padding: 20rpx; }
.cl-app .p-3 { padding: 30rpx; }

/* 颜色工具类 */
.cl-app .text-primary { color: #3a86ff; }
.cl-app .text-success { color: #07c160; }
.cl-app .text-warning { color: #ff9500; }
.cl-app .text-danger { color: #fa5151; }
.cl-app .text-muted { color: #999999; }

.cl-app .bg-primary { background-color: #3a86ff; }
.cl-app .bg-success { background-color: #07c160; }
.cl-app .bg-warning { background-color: #ff9500; }
.cl-app .bg-danger { background-color: #fa5151; }
.cl-app .bg-white { background-color: #ffffff; }
.cl-app .bg-light { background-color: #f5f5f5; }

/* 圆角工具类 */
.cl-app .rounded { border-radius: 8rpx; }
.cl-app .rounded-lg { border-radius: 12rpx; }
.cl-app .rounded-xl { border-radius: 16rpx; }
.cl-app .rounded-full { border-radius: 50%; }

/* 阴影工具类 */
.cl-app .shadow-sm { box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05); }
.cl-app .shadow { box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1); }
.cl-app .shadow-lg { box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15); }
</style>
