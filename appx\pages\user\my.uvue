<template>
  <cl-page>
    <view class="my-container">
      <!-- 顶部状态栏 -->
      <view class="status-bar"></view>
      
      <!-- 用户信息区域 -->
      <view class="user-info-section">
        <view class="avatar-container">
          <image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'"></image>
        </view>
        <view class="user-info">
          <text class="user-name">{{ userInfo.name || '未登录' }}</text>
          <text class="user-role">{{ userInfo.role || '普通用户' }}</text>
        </view>
        <view class="settings-btn" @click="navigateToSettings">
          <text class="iconfont icon-settings"></text>
        </view>
      </view>
      
      <!-- 功能菜单 -->
      <view class="menu-list">
        <view class="menu-group">
          <view class="menu-item" @click="navigateToMyDevices">
            <view class="menu-icon">
              <text class="iconfont icon-devices"></text>
            </view>
            <view class="menu-text">我的设备</view>
            <view class="menu-arrow">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
          <view class="menu-item" @click="navigateToEnergyReport">
            <view class="menu-icon">
              <text class="iconfont icon-report"></text>
            </view>
            <view class="menu-text">能耗报表</view>
            <view class="menu-arrow">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
          <view class="menu-item" @click="navigateToScenes">
            <view class="menu-icon">
              <text class="iconfont icon-scene"></text>
            </view>
            <view class="menu-text">我的场景</view>
            <view class="menu-arrow">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
        </view>
        
        <view class="menu-group">
          <view class="menu-item" @click="navigateToNotifications">
            <view class="menu-icon">
              <text class="iconfont icon-notification"></text>
              <view class="badge" v-if="unreadNotifications > 0">{{ unreadNotifications }}</view>
            </view>
            <view class="menu-text">消息通知</view>
            <view class="menu-arrow">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
          <view class="menu-item" @click="navigateToHelpCenter">
            <view class="menu-icon">
              <text class="iconfont icon-help"></text>
            </view>
            <view class="menu-text">帮助中心</view>
            <view class="menu-arrow">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
          <view class="menu-item" @click="navigateToAbout">
            <view class="menu-icon">
              <text class="iconfont icon-about"></text>
            </view>
            <view class="menu-text">关于我们</view>
            <view class="menu-arrow">
              <text class="iconfont icon-arrow-right"></text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 退出登录按钮 -->
      <view class="logout-btn" @click="logout" v-if="isLoggedIn">
        <text>退出登录</text>
      </view>
      
      <!-- 登录按钮 -->
      <view class="login-btn" @click="login" v-else>
        <text>登录</text>
      </view>
    </view>
  </cl-page>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useNotificationStore } from '@/stores/notification'

// 响应式数据
const userInfo = ref<any>({ name: '', role: '', avatar: '' })
const unreadNotifications = ref(0)
const isLoggedIn = computed(() => !!userInfo.value.name)

// stores
const userStore = useUserStore()
const notificationStore = useNotificationStore()

// 生命周期
onMounted(() => {
  console.log('My page mounted')
  // 初始化用户信息
  initUserInfo()
  // 初始化通知数量
  initNotifications()
})

// 初始化用户信息
const initUserInfo = () => {
  try {
    const user = userStore.getUserInfo()
    if (user) {
      userInfo.value = user
    }
  } catch (error) {
    console.error('Failed to load user info:', error)
  }
}

// 初始化通知数量
const initNotifications = async () => {
  try {
    if (isLoggedIn.value) {
      const count = await notificationStore.getUnreadCount()
      unreadNotifications.value = count
    }
  } catch (error) {
    console.error('Failed to load notifications:', error)
  }
}

// 导航到设置页面
const navigateToSettings = () => {
  if (!isLoggedIn.value) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  uni.navigateTo({
    url: '/pages/user/settings'
  })
}

// 导航到我的设备
const navigateToMyDevices = () => {
  if (!isLoggedIn.value) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  uni.navigateTo({
    url: '/pages/energy/device-list'
  })
}

// 导航到能耗报表
const navigateToEnergyReport = () => {
  if (!isLoggedIn.value) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  uni.navigateTo({
    url: '/pages/energy/energy-report'
  })
}

// 导航到我的场景
const navigateToScenes = () => {
  if (!isLoggedIn.value) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  uni.navigateTo({
    url: '/pages/scene/my-scenes'
  })
}

// 导航到消息通知
const navigateToNotifications = () => {
  if (!isLoggedIn.value) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  uni.navigateTo({
    url: '/pages/user/notifications'
  })
}

// 导航到帮助中心
const navigateToHelpCenter = () => {
  uni.navigateTo({
    url: '/pages/user/help-center'
  })
}

// 导航到关于我们
const navigateToAbout = () => {
  uni.navigateTo({
    url: '/pages/user/about'
  })
}

// 登录
const login = () => {
  uni.navigateTo({
    url: '/pages/user/login'
  })
}

// 退出登录
const logout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        try {
          userStore.logout()
          userInfo.value = { name: '', role: '', avatar: '' }
          unreadNotifications.value = 0
          uni.showToast({
            title: '退出成功',
            icon: 'success'
          })
        } catch (error) {
          console.error('Failed to logout:', error)
          uni.showToast({
            title: '退出失败',
            icon: 'none'
          })
        }
      }
    }
  })
}
</script>

<style>
.my-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.status-bar {
  height: var(--status-bar-height);
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

.user-info-section {
  margin-top: var(--status-bar-height);
  background-color: #3a86ff;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 4rpx solid rgba(255, 255, 255, 0.5);
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  margin-left: 20rpx;
  color: #ffffff;
}

.user-name {
  font-size: 20px;
  font-weight: bold;
}

.user-role {
  font-size: 14px;
  margin-top: 5rpx;
  opacity: 0.8;
}

.settings-btn {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #ffffff;
  font-size: 28rpx;
}

.menu-list {
  flex: 1;
  margin-top: 20rpx;
  padding: 0 20rpx;
}

.menu-group {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #3a86ff;
  position: relative;
}

.badge {
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  background-color: #f5222d;
  color: #ffffff;
  font-size: 20rpx;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.menu-text {
  flex: 1;
  margin-left: 20rpx;
  font-size: 16px;
  color: #333;
}

.menu-arrow {
  color: #999;
  font-size: 24rpx;
}

.logout-btn {
  margin: 40rpx 30rpx;
  padding: 20rpx 0;
  background-color: #f5222d;
  color: #ffffff;
  border-radius: 8rpx;
  text-align: center;
  font-size: 16px;
}

.login-btn {
  margin: 40rpx 30rpx;
  padding: 20rpx 0;
  background-color: #3a86ff;
  color: #ffffff;
  border-radius: 8rpx;
  text-align: center;
  font-size: 16px;
}
</style>