import { defineStore } from 'pinia'
import { ref } from 'vue'

// 能耗数据接口定义
interface EnergyData {
  date: string
  energy: number
  deviceId: string
  deviceName: string
}

// 能耗概览接口定义
interface EnergyOverview {
  totalEnergy: number
  totalEnergyDelta: number
  avgEnergy: number
  avgEnergyDelta: number
  savingEffect: number
  savingAmount: number
}

// 定义能耗store
export const useEnergyStore = defineStore('energy', () => {
  // 响应式数据
  const energyDetails = ref<EnergyData[]>([])
  const energyTrend = ref<EnergyData[]>([])
  const energyOverview = ref<EnergyOverview>({
    totalEnergy: 0,
    totalEnergyDelta: 0,
    avgEnergy: 0,
    avgEnergyDelta: 0,
    savingEffect: 0,
    savingAmount: 0
  })
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取能耗概览
  const getEnergyOverview = async (deviceId: string, dateRange: string) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      // 实际项目中应该替换为真实的API调用
      const response = await uni.request({
        url: '/api/energy/overview',
        method: 'GET',
        data: {
          deviceId,
          dateRange
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        const data = response.data.data
        energyOverview.value = data
        return data
      } else {
        error.value = response.data.message || '获取能耗概览失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return {
          totalEnergy: 0,
          totalEnergyDelta: 0,
          avgEnergy: 0,
          avgEnergyDelta: 0,
          savingEffect: 0,
          savingAmount: 0
        }
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to get energy overview:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return {
        totalEnergy: 0,
        totalEnergyDelta: 0,
        avgEnergy: 0,
        avgEnergyDelta: 0,
        savingEffect: 0,
        savingAmount: 0
      }
    } finally {
      loading.value = false
    }
  }

  // 获取能耗趋势
  const getEnergyTrend = async (deviceId: string, dateRange: string) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: '/api/energy/trend',
        method: 'GET',
        data: {
          deviceId,
          dateRange
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        const data = response.data.data
        energyTrend.value = data
        return data
      } else {
        error.value = response.data.message || '获取能耗趋势失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return []
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to get energy trend:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return []
    } finally {
      loading.value = false
    }
  }

  // 获取今日能耗
  const getTodayEnergy = async (): Promise<number> => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: '/api/energy/today',
        method: 'GET'
      })

      if (response.statusCode === 200 && response.data.success) {
        return response.data.data.energy || 0
      } else {
        // 返回模拟数据
        return 24.5
      }
    } catch (err) {
      console.error('Failed to get today energy:', err)
      return 24.5
    } finally {
      loading.value = false
    }
  }

  // 获取能耗趋势（重载方法，支持不同参数）
  const getEnergyTrend = async (deviceIdOrDays: string | number, dateRange?: string): Promise<any[]> => {
    try {
      loading.value = true
      error.value = null

      let url = '/api/energy/trend'
      let data: any = {}

      if (typeof deviceIdOrDays === 'number') {
        // 按天数获取趋势
        data.days = deviceIdOrDays
      } else {
        // 按设备和日期范围获取趋势
        data.deviceId = deviceIdOrDays
        data.dateRange = dateRange
      }

      // 模拟API调用
      const response = await uni.request({
        url,
        method: 'GET',
        data
      })

      if (response.statusCode === 200 && response.data.success) {
        const trendData = response.data.data
        energyTrend.value = trendData
        return trendData
      } else {
        // 返回模拟数据
        const mockTrend = Array.from({ length: typeof deviceIdOrDays === 'number' ? deviceIdOrDays : 7 }, (_, i) => ({
          date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          energy: Math.random() * 30 + 10,
          deviceId: typeof deviceIdOrDays === 'string' ? deviceIdOrDays : '',
          deviceName: '模拟设备'
        }))
        energyTrend.value = mockTrend
        return mockTrend
      }
    } catch (err) {
      console.error('Failed to get energy trend:', err)
      const mockTrend = Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        energy: Math.random() * 30 + 10,
        deviceId: '',
        deviceName: '模拟设备'
      }))
      return mockTrend
    } finally {
      loading.value = false
    }
  }

  // 获取能耗明细
  const getEnergyDetails = async (deviceId: string, dateRange: string, params: { page: number, pageSize: number }) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: '/api/energy/details',
        method: 'GET',
        data: {
          deviceId,
          dateRange,
          ...params
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        const data = response.data.data
        energyDetails.value = data.items
        return {
          items: data.items,
          total: data.total
        }
      } else {
        error.value = response.data.message || '获取能耗明细失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return {
          items: [],
          total: 0
        }
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to get energy details:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return {
        items: [],
          total: 0
        }
    } finally {
      loading.value = false
    }
  }

  return {
    energyDetails,
    energyTrend,
    energyOverview,
    loading,
    error,
    getTodayEnergy,
    getEnergyOverview,
    getEnergyTrend,
    getEnergyDetails
  }
})