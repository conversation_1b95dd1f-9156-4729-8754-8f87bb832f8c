import { Dict, dict } from "./dict";
import { User, user } from "./user";

// 导入节能灯业务状态管理模块
import { useEnergyDeviceStore } from "@/stores/energy-device";
import { useEnergyMonitorStore } from "@/stores/energy-monitor";
import { useEnergySettingsStore } from "@/stores/energy-settings";

type Store = {
	user: User;
	dict: Dict;
	// 节能灯业务模块
	energyDevice: ReturnType<typeof useEnergyDeviceStore>;
	energyMonitor: ReturnType<typeof useEnergyMonitorStore>;
	energySettings: ReturnType<typeof useEnergySettingsStore>;
};

export function useStore(): Store {
	return {
		user,
		dict,
		// 初始化节能灯业务状态管理
		energyDevice: useEnergyDeviceStore(),
		energyMonitor: useEnergyMonitorStore(),
		energySettings: useEnergySettingsStore()
	};
}

export * from "./dict";
export * from "./user";

// 导出节能灯业务状态管理模块
export { useEnergyDeviceStore } from "@/stores/energy-device";
export { useEnergyMonitorStore } from "@/stores/energy-monitor";
export { useEnergySettingsStore } from "@/stores/energy-settings";

// 导出节能灯业务相关类型
export type {
	EnergyDevice,
	DeviceControlParams,
	BatchControlParams,
	DeviceFilterParams,
	DeviceStats
} from "@/stores/energy-device";

export type {
	EnergyData,
	EnergyStats,
	EnergyReport,
	EnergySuggestion,
	EnergyQueryParams,
	RealTimeData
} from "@/stores/energy-monitor";

export type {
	UserSettings,
	NotificationSettings,
	DashboardSettings,
	ControlSettings,
	PrivacySettings,
	SettingsUpdateParams
} from "@/stores/energy-settings";
