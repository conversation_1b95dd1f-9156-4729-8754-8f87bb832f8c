<template>
  <cl-page>
    <view class="lighting-control-container">
      <!-- 顶部状态栏 -->
      <view class="status-bar"></view>
      
      <!-- 设备选择器 -->
      <view class="device-selector">
        <picker @change="onDeviceChange" :value="currentDeviceIndex" :range="deviceNames">
          <view class="picker">
            当前设备: {{ deviceNames[currentDeviceIndex] }}
          </view>
        </picker>
      </view>
      
      <!-- 控制区域 -->
      <view class="control-panel">
        <!-- 开关控制 -->
        <view class="control-item">
          <text class="control-label">电源开关</text>
          <switch :checked="deviceStatus.powerOn" @change="togglePower" :disabled="!currentDevice.online" />
        </view>
        
        <!-- 亮度调节 -->
        <view class="control-item" :class="deviceStatus.powerOn ? '' : 'disabled'">
          <text class="control-label">亮度调节</text>
          <slider
            :min="0"
            :max="100"
            :value="deviceStatus.brightness"
            @change="adjustBrightness"
            :disabled="!deviceStatus.powerOn || !currentDevice.online"
          />
          <text class="value-text">{{ deviceStatus.brightness }}%</text>
        </view>
        
        <!-- 色温调节 -->
        <view class="control-item" :class="deviceStatus.powerOn ? '' : 'disabled'">
          <text class="control-label">色温调节</text>
          <slider
            :min="2700"
            :max="6500"
            :value="deviceStatus.colorTemp"
            @change="adjustColorTemp"
            :disabled="!deviceStatus.powerOn || !currentDevice.online"
          />
          <text class="value-text">{{ deviceStatus.colorTemp }}K</text>
        </view>
        
        <!-- 情景模式 -->
        <view class="control-item">
          <text class="control-label">情景模式</text>
          <view class="scene-buttons">
            <button class="scene-btn" :class="currentScene === 'home' ? 'active' : ''" @click="setScene('home')" :disabled="!deviceStatus.powerOn || !currentDevice.online">居家模式</button>
            <button class="scene-btn" :class="currentScene === 'reading' ? 'active' : ''" @click="setScene('reading')" :disabled="!deviceStatus.powerOn || !currentDevice.online">阅读模式</button>
            <button class="scene-btn" :class="currentScene === 'movie' ? 'active' : ''" @click="setScene('movie')" :disabled="!deviceStatus.powerOn || !currentDevice.online">影院模式</button>
            <button class="scene-btn" :class="currentScene === 'sleep' ? 'active' : ''" @click="setScene('sleep')" :disabled="!deviceStatus.powerOn || !currentDevice.online">睡眠模式</button>
          </view>
        </view>
      </view>
    </view>
  </cl-page>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useDeviceStore } from '@/stores/device'

// 响应式数据
const devices = ref<any[]>([])
const deviceNames = computed(() => devices.value.map(device => device.name))
const currentDeviceIndex = ref(0)
const deviceStatus = ref<{
  powerOn: boolean
  brightness: number
  colorTemp: number
}>({
  powerOn: false,
  brightness: 50,
  colorTemp: 4000
})
const currentScene = ref('home')

// 设备store
const deviceStore = useDeviceStore()

// 当前设备
const currentDevice = computed(() => {
  return devices.value[currentDeviceIndex.value] || {}
})

// 生命周期
onMounted(() => {
  console.log('Lighting control page mounted')
  // 初始化设备列表
  initDevices()
})

// 初始化设备列表
const initDevices = async () => {
  try {
    const data = await deviceStore.getDevices({ page: 1, pageSize: 100 })
    devices.value = data.items
    if (devices.value.length > 0) {
      // 加载第一个设备的状态
      loadDeviceStatus(devices.value[0].id)
    }
  } catch (error) {
    console.error('Failed to load devices:', error)
    uni.showToast({
      title: '设备加载失败',
      icon: 'none'
    })
  }
}

// 加载设备状态
const loadDeviceStatus = async (deviceId: string) => {
  try {
    const status = await deviceStore.getDeviceStatus(deviceId)
    deviceStatus.value = {
      powerOn: status.powerOn || false,
      brightness: status.brightness || 50,
      colorTemp: status.colorTemp || 4000
    }
    // 设置对应的情景模式
    setSceneByStatus()
  } catch (error) {
    console.error('Failed to load device status:', error)
  }
}

// 根据状态设置情景模式
const setSceneByStatus = () => {
  const { brightness, colorTemp } = deviceStatus.value

  // 简单的情景模式判断逻辑
  if (brightness > 70 && colorTemp > 5000) {
    currentScene.value = 'reading'
  } else if (brightness < 30 && colorTemp < 3500) {
    currentScene.value = 'sleep'
  } else if (brightness < 50 && colorTemp < 4000) {
    currentScene.value = 'movie'
  } else {
    currentScene.value = 'home'
  }
}

// 切换设备
const onDeviceChange = (e: any) => {
  const index = e.detail.value
  currentDeviceIndex.value = index
  if (devices.value.length > index) {
    loadDeviceStatus(devices.value[index].id)
  }
}

// 切换电源
const togglePower = async (e: any) => {
  const isOn = e.detail.value
  try {
    await deviceStore.controlDevice(currentDevice.value.id, { powerOn: isOn })
    deviceStatus.value.powerOn = isOn
    uni.showToast({
      title: isOn ? '设备已开启' : '设备已关闭',
      icon: 'success'
    })
  } catch (error) {
    console.error('Failed to toggle power:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  }
}

// 调节亮度
const adjustBrightness = async (e: any) => {
  const brightness = e.detail.value
  try {
    await deviceStore.controlDevice(currentDevice.value.id, { brightness })
    deviceStatus.value.brightness = brightness
    // 更新情景模式
    setSceneByStatus()
  } catch (error) {
    console.error('Failed to adjust brightness:', error)
    uni.showToast({
      title: '亮度调节失败',
      icon: 'none'
    })
  }
}

// 调节色温
const adjustColorTemp = async (e: any) => {
  const colorTemp = e.detail.value
  try {
    await deviceStore.controlDevice(currentDevice.value.id, { colorTemp })
    deviceStatus.value.colorTemp = colorTemp
    // 更新情景模式
    setSceneByStatus()
  } catch (error) {
    console.error('Failed to adjust color temperature:', error)
    uni.showToast({
      title: '色温调节失败',
      icon: 'none'
    })
  }
}

// 设置情景模式
const setScene = async (scene: string) => {
  currentScene.value = scene
  let settings: any = {}

  switch (scene) {
    case 'home':
      settings = { brightness: 50, colorTemp: 4000 }
      break
    case 'reading':
      settings = { brightness: 80, colorTemp: 5500 }
      break
    case 'movie':
      settings = { brightness: 30, colorTemp: 3500 }
      break
    case 'sleep':
      settings = { brightness: 10, colorTemp: 2700 }
      break
  }

  try {
    await deviceStore.controlDevice(currentDevice.value.id, settings)
    deviceStatus.value.brightness = settings.brightness
    deviceStatus.value.colorTemp = settings.colorTemp
    uni.showToast({
      title: '已切换到' + (scene === 'home' ? '居家' : scene === 'reading' ? '阅读' : scene === 'movie' ? '影院' : '睡眠') + '模式',
      icon: 'success'
    })
  } catch (error) {
    console.error('Failed to set scene:', error)
    uni.showToast({
      title: '模式切换失败',
      icon: 'none'
    })
  }
}
</script>

<style>
.lighting-control-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.status-bar {
  height: var(--status-bar-height);
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

.device-selector {
  margin-top: var(--status-bar-height);
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.picker {
  font-size: 16px;
  color: #333;
  padding: 10rpx 0;
}

.control-panel {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.control-item {
  margin-bottom: 30rpx;
}

.control-label {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.disabled .control-label {
  color: #999;
}

.slider {
  width: 100%;
  margin-bottom: 10rpx;
}

.value-text {
  font-size: 14px;
  color: #666;
}

.scene-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.scene-btn {
  flex: 1;
  padding: 15rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  font-size: 14px;
}

.scene-btn.active {
  background-color: #007aff;
  color: #ffffff;
}
</style>