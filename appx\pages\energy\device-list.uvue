<template>
  <cl-page>
    <view class="device-list-container">
      <!-- 顶部状态栏 -->
      <view class="status-bar"></view>
      
      <!-- 搜索栏 -->
      <view class="search-bar">
        <view class="search-input">
          <image src="/static/icons/search.png" class="search-icon" />
          <input type="text" placeholder="搜索设备名称或编号" v-model="searchKeyword" @input="debounceSearch" />
        </view>
      </view>
      
      <!-- 设备列表 -->
      <list-view class="device-list" @scrolltolower="loadMore">
        <list-item v-for="(device, index) in devices" :key="device.id" class="device-item" @click="navigateToDeviceDetail(device.id)">
          <view class="device-info">
            <view class="device-basic">
              <text class="device-name">{{ device.name }}</text>
              <text class="device-id">{{ device.id }}</text>
            </view>
            <text class="device-location">{{ device.location }}</text>
          </view>
          <view class="device-status">
            <text :class="device.online ? 'status-online' : 'status-offline'">{{ device.online ? '在线' : '离线' }}</text>
            <switch :checked="device.powerOn" @change="toggleDevice(device.id, $event.detail.value)" :disabled="!device.online" />
          </view>
        </list-item>
      </list-view>
    </view>
  </cl-page>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { navigateTo } from 'uni-app'

// 响应式数据
const devices = ref<any[]>([])
const searchKeyword = ref('')
const loading = ref(false)
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)

// 设备store
const deviceStore = useDeviceStore()

// 生命周期
onMounted(() => {
  console.log('Device list page mounted')
  // 初始化数据
  initDevices()
})

// 初始化设备列表
const initDevices = async () => {
  try {
    loading.value = true
    const data = await deviceStore.getDevices({ page: 1, pageSize: pageSize.value, keyword: searchKeyword.value })
    devices.value = data.items
    hasMore.value = data.total > devices.value.length
    page.value = 1
  } catch (error) {
    console.error('Failed to load devices:', error)
    uni.showToast({
      title: '设备加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载更多设备
const loadMore = async () => {
  if (loading.value || !hasMore.value) return

  try {
    loading.value = true
    page.value += 1
    const data = await deviceStore.getDevices({ page: page.value, pageSize: pageSize.value, keyword: searchKeyword.value })
    devices.value = [...devices.value, ...data.items]
    hasMore.value = devices.value.length < data.total
  } catch (error) {
    console.error('Failed to load more devices:', error)
    page.value -= 1
  } finally {
    loading.value = false
  }
}

// 搜索设备（防抖）
const debounceSearch = reactive({
  timeout: null as any,
  handler: function() {
    if (this.timeout) clearTimeout(this.timeout)
    this.timeout = setTimeout(() => {
      initDevices()
    }, 500)
  }
})

// 切换设备开关状态
const toggleDevice = async (deviceId: string, isOn: boolean) => {
  try {
    await deviceStore.controlDevice(deviceId, { powerOn: isOn })
    // 更新本地设备状态
    const device = devices.value.find(d => d.id === deviceId)
    if (device) {
      device.powerOn = isOn
    }
    uni.showToast({
      title: isOn ? '设备已开启' : '设备已关闭',
      icon: 'success'
    })
  } catch (error) {
    console.error('Failed to control device:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  }
}

// 导航到设备详情
const navigateToDeviceDetail = (deviceId: string) => {
  navigateTo({
    url: `/pages/energy/device-detail?id=${deviceId}`
  })
}
</script>

<style>
.device-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.status-bar {
  height: var(--status-bar-height);
  background-color: #ffffff;
}

.search-bar {
  padding: 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eee;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 60rpx;
  padding: 15rpx 20rpx;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  opacity: 0.5;
}

input {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.device-list {
  flex: 1;
  background-color: #f5f5f5;
}

.device-item {
  padding: 20rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  margin: 0 20rpx 20rpx 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-info {
  flex: 1;
}

.device-basic {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.device-id {
  font-size: 12px;
  color: #999;
}

.device-location {
  font-size: 14px;
  color: #666;
  margin-top: 10rpx;
}

.device-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.status-online {
  font-size: 14px;
  color: #07c160;
  margin-bottom: 10rpx;
}

.status-offline {
  font-size: 14px;
  color: #999;
  margin-bottom: 10rpx;
}
</style>