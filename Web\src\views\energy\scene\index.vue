<template>
  <div class="system-scene lighting-control mobile-device-control responsive-table mobile-safe-area">
    <!-- 查询表单 -->
    <el-card class="box-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>场景管理</span>
        </div>
      </template>
      
      <el-form :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px" class="mobile-form">
        <el-form-item label="场景名称" prop="sceneName">
          <el-input
            v-model="queryParams.sceneName"
            placeholder="请输入场景名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            class="mobile-input"
          />
        </el-form-item>
        <el-form-item label="场景类型" prop="sceneType">
          <el-select v-model="queryParams.sceneType" placeholder="请选择场景类型" clearable style="width: 150px">
            <el-option label="节能模式" value="energy_saving" />
            <el-option label="舒适模式" value="comfort" />
            <el-option label="工作模式" value="work" />
            <el-option label="休息模式" value="rest" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="isEnabled">
          <el-select v-model="queryParams.isEnabled" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 场景列表 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>场景列表</span>
          <div class="header-actions">
            <el-button type="primary" icon="Plus" @click="handleAdd">新增场景</el-button>
            <el-button type="success" icon="MagicStick" @click="openSceneEnhanced">智能推荐</el-button>
            <el-button type="info" icon="Collection" @click="openTemplateLibrary">模板库</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="sceneList"
        row-key="id"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column prop="sceneName" label="场景名称" min-width="120" show-overflow-tooltip />
        <el-table-column prop="sceneType" label="场景类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getSceneTypeColor(row.sceneType)">{{ getSceneTypeLabel(row.sceneType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="场景描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="deviceCount" label="设备数量" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="info">{{ row.deviceCount || 0 }}台</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isEnabled" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.isEnabled"
              @change="handleStatusChange(row)"
              :loading="row.statusLoading"
            />
          </template>
        </el-table-column>
        <el-table-column prop="executionCount" label="执行次数" width="100" align="center" />
        <el-table-column prop="lastExecuteTime" label="最后执行" width="180" align="center">
          <template #default="{ row }">
            {{ row.lastExecuteTime ? formatDateTime(row.lastExecuteTime) : '未执行' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="{ row }">
            <el-button link type="primary" size="small" @click="handleExecute(row)" :disabled="!row.isEnabled">
              执行
            </el-button>
            <el-button link type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button link type="success" size="small" @click="handleDevices(row)">
              设备
            </el-button>
            <el-button link type="warning" size="small" @click="handleCopy(row)">
              复制
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 新增/编辑场景弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px" destroy-on-close>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="场景名称" prop="sceneName">
              <el-input v-model="form.sceneName" placeholder="请输入场景名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="场景类型" prop="sceneType">
              <el-select v-model="form.sceneType" placeholder="请选择场景类型" style="width: 100%">
                <el-option label="节能模式" value="energy_saving" />
                <el-option label="舒适模式" value="comfort" />
                <el-option label="工作模式" value="work" />
                <el-option label="休息模式" value="rest" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="执行时间" prop="executeTime">
              <el-time-picker
                v-model="form.executeTime"
                placeholder="请选择执行时间"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="isEnabled">
              <el-switch v-model="form.isEnabled" active-text="启用" inactive-text="禁用" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="场景描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入场景描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 场景设备管理弹窗 -->
    <el-dialog v-model="deviceDialogVisible" title="场景设备管理" width="1000px" destroy-on-close>
      <div class="device-management">
        <div class="device-header">
          <el-button type="primary" icon="Plus" @click="handleAddDevice">添加设备</el-button>
          <el-button type="success" icon="Setting" @click="handleBatchSetting">批量设置</el-button>
        </div>
        
        <el-table
          v-loading="deviceLoading"
          :data="sceneDeviceList"
          row-key="id"
          border
          stripe
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="deviceName" label="设备名称" min-width="120" show-overflow-tooltip />
          <el-table-column prop="deviceCode" label="设备编号" min-width="120" show-overflow-tooltip />
          <el-table-column prop="deviceType" label="设备类型" width="100" align="center" />
          <el-table-column prop="location" label="安装位置" min-width="120" show-overflow-tooltip />
          <el-table-column prop="brightness" label="亮度设置" width="120" align="center">
            <template #default="{ row }">
              <el-slider v-model="row.brightness" :min="0" :max="100" :step="1" style="width: 80px" @change="updateDeviceSetting(row)" />
            </template>
          </el-table-column>
          <el-table-column prop="isOn" label="开关状态" width="100" align="center">
            <template #default="{ row }">
              <el-switch v-model="row.isOn" @change="updateDeviceSetting(row)" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="{ row }">
              <el-button link type="danger" size="small" @click="removeDevice(row)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 添加设备弹窗 -->
    <el-dialog v-model="addDeviceVisible" title="添加设备" width="600px" destroy-on-close>
      <el-form ref="deviceFormRef" :model="deviceForm" label-width="100px">
        <el-form-item label="选择设备" prop="deviceIds">
          <el-select
            v-model="deviceForm.deviceIds"
            multiple
            filterable
            placeholder="请选择要添加的设备"
            style="width: 100%"
          >
            <el-option
              v-for="device in availableDevices"
              :key="device.id"
              :label="`${device.deviceName} (${device.deviceCode})`"
              :value="device.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="默认亮度" prop="defaultBrightness">
          <el-slider v-model="deviceForm.defaultBrightness" :min="0" :max="100" :step="1" show-input />
        </el-form-item>
        <el-form-item label="默认状态" prop="defaultStatus">
          <el-switch v-model="deviceForm.defaultStatus" active-text="开启" inactive-text="关闭" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDeviceVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddDeviceSubmit" :loading="addDeviceLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 增强功能组件 -->
    <SceneEnhanced ref="sceneEnhancedRef" v-model="state.enhancedVisible" @refresh="handleQuery" />
  </div>
</template>

<script setup lang="ts" name="scene">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSceneApi } from '/@/api-services/scene';
import { useDeviceApi } from '/@/api-services/device';
import { formatDateTime } from '@/utils/formatTime'
import SceneEnhanced from './sceneEnhanced.vue'

const loading = ref(false)
const deviceLoading = ref(false)
const total = ref(0)
const sceneList = ref([])
const sceneDeviceList = ref([])
const availableDevices = ref([])
const dialogVisible = ref(false)
const deviceDialogVisible = ref(false)
const addDeviceVisible = ref(false)
const dialogTitle = ref('')
const submitLoading = ref(false)
const addDeviceLoading = ref(false)
const currentSceneId = ref('')
const sceneEnhancedRef = ref()

const state = reactive({
  enhancedVisible: false
})

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  sceneName: '',
  sceneType: '',
  isEnabled: undefined
})

const form = reactive({
  id: undefined,
  sceneName: '',
  sceneType: '',
  description: '',
  executeTime: '',
  isEnabled: true,
  remark: ''
})

const deviceForm = reactive({
  deviceIds: [],
  defaultBrightness: 80,
  defaultStatus: true
})

const rules = reactive({
  sceneName: [{ required: true, message: '请输入场景名称', trigger: 'blur' }],
  sceneType: [{ required: true, message: '请选择场景类型', trigger: 'change' }],
  description: [{ required: true, message: '请输入场景描述', trigger: 'blur' }]
})

// 获取列表
const getList = async () => {
  loading.value = true
  try {
    const { data } = await EnergySceneApi.apiEnergyScenePagePost(queryParams)
    sceneList.value = data.items || []
    total.value = data.total || 0
  } catch (error) {
    console.error('获取场景列表失败:', error)
    ElMessage.error('获取场景列表失败')
  } finally {
    loading.value = false
  }
}

// 获取设备列表
const getDeviceList = async () => {
  try {
    const deviceApi = useDeviceApi();
		const { data } = await deviceApi.getList();
    availableDevices.value = data || []
  } catch (error) {
    console.error('获取设备列表失败:', error)
  }
}

// 获取场景设备列表
const getSceneDevices = async (sceneId: string) => {
  deviceLoading.value = true
  try {
    const { data } = await EnergySceneApi.apiEnergySceneDevicesGet({ sceneId })
    sceneDeviceList.value = data || []
  } catch (error) {
    console.error('获取场景设备失败:', error)
    ElMessage.error('获取场景设备失败')
  } finally {
    deviceLoading.value = false
  }
}

// 获取场景类型标签
const getSceneTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'energy_saving': '节能模式',
    'comfort': '舒适模式',
    'work': '工作模式',
    'rest': '休息模式',
    'custom': '自定义'
  }
  return typeMap[type] || type
}

// 获取场景类型颜色
const getSceneTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'energy_saving': 'success',
    'comfort': 'primary',
    'work': 'warning',
    'rest': 'info',
    'custom': ''
  }
  return colorMap[type] || ''
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置
const resetQuery = () => {
  Object.assign(queryParams, {
    sceneName: '',
    sceneType: '',
    isEnabled: undefined,
    pageNum: 1
  })
  getList()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增场景'
  dialogVisible.value = true
  Object.assign(form, {
    id: undefined,
    sceneName: '',
    sceneType: '',
    description: '',
    executeTime: '',
    isEnabled: true,
    remark: ''
  })
}

// 编辑
const handleEdit = (row: any) => {
  dialogTitle.value = '编辑场景'
  dialogVisible.value = true
  Object.assign(form, { ...row })
}

// 复制
const handleCopy = (row: any) => {
  dialogTitle.value = '复制场景'
  dialogVisible.value = true
  Object.assign(form, {
    ...row,
    id: undefined,
    sceneName: `${row.sceneName}_副本`
  })
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个场景吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await EnergySceneApi.apiEnergySceneDeletePost({ id: row.id })
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 执行场景
const handleExecute = async (row: any) => {
  try {
    await EnergySceneApi.apiEnergySceneExecutePost({ id: row.id })
    ElMessage.success('场景执行成功')
    getList() // 刷新列表以更新执行次数和最后执行时间
  } catch (error) {
    console.error('场景执行失败:', error)
    ElMessage.error('场景执行失败')
  }
}

// 状态变更
const handleStatusChange = async (row: any) => {
  row.statusLoading = true
  try {
    await EnergySceneApi.apiEnergySceneUpdatePost({
      id: row.id,
      isEnabled: row.isEnabled
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
    row.isEnabled = !row.isEnabled // 回滚状态
  } finally {
    row.statusLoading = false
  }
}

// 管理设备
const handleDevices = (row: any) => {
  currentSceneId.value = row.id
  deviceDialogVisible.value = true
  getSceneDevices(row.id)
}

// 添加设备
const handleAddDevice = () => {
  addDeviceVisible.value = true
  Object.assign(deviceForm, {
    deviceIds: [],
    defaultBrightness: 80,
    defaultStatus: true
  })
}

// 批量设置
const handleBatchSetting = () => {
  ElMessage.info('批量设置功能开发中')
}

// 更新设备设置
const updateDeviceSetting = async (row: any) => {
  try {
    await EnergySceneApi.apiEnergySceneDeviceUpdatePost({
      sceneId: currentSceneId.value,
      deviceId: row.deviceId,
      brightness: row.brightness,
      isOn: row.isOn
    })
  } catch (error) {
    console.error('更新设备设置失败:', error)
    ElMessage.error('更新设备设置失败')
  }
}

// 移除设备
const removeDevice = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要从场景中移除这个设备吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await EnergySceneApi.apiEnergySceneDeviceDeletePost({
      sceneId: currentSceneId.value,
      deviceId: row.deviceId
    })
    ElMessage.success('移除成功')
    getSceneDevices(currentSceneId.value)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除失败:', error)
      ElMessage.error('移除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    submitLoading.value = true
    
    if (form.id) {
      await EnergySceneApi.apiEnergySceneUpdatePost(form)
      ElMessage.success('编辑成功')
    } else {
      await EnergySceneApi.apiEnergySceneAddPost(form)
      ElMessage.success('新增成功')
    }
    
    dialogVisible.value = false
    getList()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 提交添加设备
const handleAddDeviceSubmit = async () => {
  if (deviceForm.deviceIds.length === 0) {
    ElMessage.warning('请选择要添加的设备')
    return
  }
  
  try {
    addDeviceLoading.value = true
    
    await EnergySceneApi.apiEnergySceneDeviceBatchAddPost({
      sceneId: currentSceneId.value,
      deviceIds: deviceForm.deviceIds,
      brightness: deviceForm.defaultBrightness,
      isOn: deviceForm.defaultStatus
    })
    
    ElMessage.success('添加设备成功')
    addDeviceVisible.value = false
    getSceneDevices(currentSceneId.value)
  } catch (error) {
    console.error('添加设备失败:', error)
    ElMessage.error('添加设备失败')
  } finally {
    addDeviceLoading.value = false
  }
}

// 打开增强功能
const openSceneEnhanced = () => {
  state.enhancedVisible = true
}

// 打开模板库
const openTemplateLibrary = () => {
  state.enhancedVisible = true
  // 可以通过参数控制显示模板库标签页
}

onMounted(() => {
  getList()
  getDeviceList()
})
</script>

<style scoped lang="scss">
.system-scene {
  .box-card {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .dialog-footer {
    text-align: right;
  }
  
  .device-management {
    .device-header {
      display: flex;
      gap: 10px;
    }
  }
}
</style>