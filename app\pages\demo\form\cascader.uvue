<template>
	<cl-page>
		<view class="p-3">
			<demo-item :label="t('基础用法')">
				<cl-cascader v-model="form.cascader1" :options="options"></cl-cascader>
			</demo-item>

			<demo-item :label="t('带索引、地区选择')">
				<cl-cascader v-model="form.cascader2" :options="options2"></cl-cascader>
			</demo-item>

			<demo-item :label="t('自定义')">
				<cl-cascader
					v-model="form.cascader3"
					:options="options"
					:disabled="isDisabled"
					:text-separator="isSeparator ? ' / ' : ' - '"
					:height="isHeight ? 500 : 800"
				></cl-cascader>

				<cl-list
					border
					:pt="{
						className: 'mt-3'
					}"
				>
					<cl-list-item :label="t('换个分隔符')">
						<cl-switch v-model="isSeparator"></cl-switch>
					</cl-list-item>

					<cl-list-item :label="t('列表高度小一点')">
						<cl-switch v-model="isHeight"></cl-switch>
					</cl-list-item>

					<cl-list-item :label="t('禁用')">
						<cl-switch v-model="isDisabled"></cl-switch>
					</cl-list-item>
				</cl-list>
			</demo-item>
		</view>
	</cl-page>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import DemoItem from "../components/item.uvue";
import { useCascader, type ClCascaderOption } from "@/uni_modules/cool-ui";
import { t } from "@/locale";
import pca from "@/data/pca.json";

type Form = {
	cascader1: string[];
	cascader2: string[];
	cascader3: string[];
};

const form = reactive<Form>({
	cascader1: [],
	cascader2: [],
	cascader3: []
});

const isDisabled = ref(false);
const isSeparator = ref(false);
const isHeight = ref(false);

const options = ref<ClCascaderOption[]>([
	{
		label: "电子产品",
		value: "1",
		children: [
			{
				label: "手机",
				value: "1-1",
				children: [
					{
						label: "苹果",
						value: "1-1-1",
						children: [
							{
								label: "iPhone 15 Pro Max",
								value: "1-1-1-1"
							},
							{
								label: "iPhone 15 Pro",
								value: "1-1-1-2"
							},
							{
								label: "iPhone 15",
								value: "1-1-1-3"
							},
							{
								label: "iPhone 14 Pro Max",
								value: "1-1-1-4"
							},
							{
								label: "iPhone 14",
								value: "1-1-1-5"
							}
						]
					},
					{
						label: "华为",
						value: "1-1-2",
						children: [
							{
								label: "Mate 60 Pro+",
								value: "1-1-2-1"
							},
							{
								label: "Mate 60 Pro",
								value: "1-1-2-2"
							},
							{
								label: "Mate 60",
								value: "1-1-2-3"
							},
							{
								label: "P60 Pro",
								value: "1-1-2-4"
							},
							{
								label: "P60",
								value: "1-1-2-5"
							}
						]
					},
					{
						label: "小米",
						value: "1-1-3",
						children: [
							{
								label: "小米14 Pro",
								value: "1-1-3-1"
							},
							{
								label: "小米14",
								value: "1-1-3-2"
							},
							{
								label: "Redmi K70 Pro",
								value: "1-1-3-3"
							},
							{
								label: "Redmi K70",
								value: "1-1-3-4"
							}
						]
					}
				]
			},
			{
				label: "电脑",
				value: "1-2",
				children: [
					{
						label: "笔记本",
						value: "1-2-1",
						children: [
							{
								label: "MacBook Pro 16",
								value: "1-2-1-1"
							},
							{
								label: "MacBook Pro 14",
								value: "1-2-1-2"
							},
							{
								label: "MacBook Air 15",
								value: "1-2-1-3"
							},
							{
								label: "ThinkPad X1",
								value: "1-2-1-4"
							},
							{
								label: "ROG 魔霸新锐",
								value: "1-2-1-5"
							},
							{
								label: "拯救者 Y9000P",
								value: "1-2-1-6"
							}
						]
					},
					{
						label: "台式机",
						value: "1-2-2",
						children: [
							{
								label: "iMac 24寸",
								value: "1-2-2-1"
							},
							{
								label: "Mac Studio",
								value: "1-2-2-2"
							},
							{
								label: "Mac Pro",
								value: "1-2-2-3"
							},
							{
								label: "外星人",
								value: "1-2-2-4"
							},
							{
								label: "惠普暗影精灵",
								value: "1-2-2-5"
							}
						]
					}
				]
			},
			{
				label: "平板",
				value: "1-3",
				children: [
					{
						label: "iPad",
						value: "1-3-1",
						children: [
							{
								label: "iPad Pro 12.9",
								value: "1-3-1-1"
							},
							{
								label: "iPad Pro 11",
								value: "1-3-1-2"
							},
							{
								label: "iPad Air",
								value: "1-3-1-3"
							},
							{
								label: "iPad mini",
								value: "1-3-1-4"
							}
						]
					},
					{
						label: "安卓平板",
						value: "1-3-2",
						children: [
							{
								label: "小米平板6 Pro",
								value: "1-3-2-1"
							},
							{
								label: "华为MatePad Pro",
								value: "1-3-2-2"
							},
							{
								label: "三星Galaxy Tab S9",
								value: "1-3-2-3"
							}
						]
					}
				]
			}
		]
	},
	{
		label: "服装",
		value: "2",
		children: [
			{
				label: "男装",
				value: "2-1",
				children: [
					{
						label: "上衣",
						value: "2-1-1",
						children: [
							{
								label: "短袖T恤",
								value: "2-1-1-1"
							},
							{
								label: "长袖T恤",
								value: "2-1-1-2"
							},
							{
								label: "衬衫",
								value: "2-1-1-3"
							},
							{
								label: "卫衣",
								value: "2-1-1-4"
							},
							{
								label: "夹克",
								value: "2-1-1-5"
							},
							{
								label: "毛衣",
								value: "2-1-1-6"
							}
						]
					},
					{
						label: "裤装",
						value: "2-1-2",
						children: [
							{
								label: "牛仔裤",
								value: "2-1-2-1"
							},
							{
								label: "休闲裤",
								value: "2-1-2-2"
							},
							{
								label: "运动裤",
								value: "2-1-2-3"
							},
							{
								label: "西裤",
								value: "2-1-2-4"
							},
							{
								label: "短裤",
								value: "2-1-2-5"
							}
						]
					},
					{
						label: "外套",
						value: "2-1-3",
						children: [
							{
								label: "羽绒服",
								value: "2-1-3-1"
							},
							{
								label: "大衣",
								value: "2-1-3-2"
							},
							{
								label: "夹克",
								value: "2-1-3-3"
							},
							{
								label: "西装",
								value: "2-1-3-4"
							}
						]
					}
				]
			},
			{
				label: "女装",
				value: "2-2",
				children: [
					{
						label: "裙装",
						value: "2-2-1",
						children: [
							{
								label: "连衣裙",
								value: "2-2-1-1"
							},
							{
								label: "半身裙",
								value: "2-2-1-2"
							},
							{
								label: "A字裙",
								value: "2-2-1-3"
							},
							{
								label: "包臀裙",
								value: "2-2-1-4"
							},
							{
								label: "百褶裙",
								value: "2-2-1-5"
							}
						]
					},
					{
						label: "上装",
						value: "2-2-2",
						children: [
							{
								label: "衬衫",
								value: "2-2-2-1"
							},
							{
								label: "T恤",
								value: "2-2-2-2"
							},
							{
								label: "毛衣",
								value: "2-2-2-3"
							},
							{
								label: "卫衣",
								value: "2-2-2-4"
							},
							{
								label: "雪纺衫",
								value: "2-2-2-5"
							}
						]
					},
					{
						label: "外套",
						value: "2-2-3",
						children: [
							{
								label: "风衣",
								value: "2-2-3-1"
							},
							{
								label: "羽绒服",
								value: "2-2-3-2"
							},
							{
								label: "大衣",
								value: "2-2-3-3"
							},
							{
								label: "西装",
								value: "2-2-3-4"
							},
							{
								label: "皮衣",
								value: "2-2-3-5"
							}
						]
					}
				]
			}
		]
	},
	{
		label: "食品",
		value: "3",
		children: [
			{
				label: "水果",
				value: "3-1",
				children: [
					{
						label: "苹果",
						value: "3-1-1"
					},
					{
						label: "香蕉",
						value: "3-1-2"
					},
					{
						label: "橘子",
						value: "3-1-3"
					}
				]
			},
			{
				label: "蔬菜",
				value: "3-2",
				children: [
					{
						label: "西红柿",
						value: "3-2-1"
					},
					{
						label: "黄瓜",
						value: "3-2-2"
					},
					{
						label: "胡萝卜",
						value: "3-2-3"
					}
				]
			}
		]
	},
	{
		label: "饮料",
		value: "4",
		children: [
			{
				label: "果汁",
				value: "4-1",
				children: [
					{
						label: "苹果汁",
						value: "4-1-1"
					},
					{
						label: "橙汁",
						value: "4-1-2"
					},
					{
						label: "葡萄汁",
						value: "4-1-3"
					},
					{
						label: "西瓜汁",
						value: "4-1-4"
					}
				]
			}
		]
	}
]);

const options2 = useCascader(pca);
</script>
