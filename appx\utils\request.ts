// 统一请求工具类
import type { RequestOptions, Response, ApiResponse, HttpClientConfig } from '@/types/api'

// 默认配置
const DEFAULT_CONFIG: HttpClientConfig = {
  baseURL: '',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
}

// 请求状态码枚举
export enum StatusCode {
  SUCCESS = 200,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  SERVER_ERROR = 500,
  NETWORK_ERROR = -1
}

// 业务状态码枚举
export enum BusinessCode {
  SUCCESS = 0,
  PARAM_ERROR = 1001,
  AUTH_ERROR = 1002,
  PERMISSION_ERROR = 1003,
  BUSINESS_ERROR = 2000,
  SYSTEM_ERROR = 5000
}

// HTTP客户端类
class HttpClient {
  private config: HttpClientConfig
  private requestQueue: Map<string, any> = new Map()

  constructor(config: Partial<HttpClientConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  // 请求拦截器
  private async requestInterceptor(options: RequestOptions): Promise<RequestOptions> {
    // 添加基础URL
    if (this.config.baseURL && !options.url.startsWith('http')) {
      options.url = this.config.baseURL + options.url
    }

    // 添加默认headers
    options.headers = {
      ...this.config.headers,
      ...options.headers
    }

    // 添加token
    const token = uni.getStorageSync('token')
    if (token) {
      options.headers.Authorization = `Bearer ${token}`
    }

    // 添加超时时间
    if (!options.timeout) {
      options.timeout = this.config.timeout
    }

    console.log('Request:', options)
    return options
  }

  // 响应拦截器
  private async responseInterceptor<T>(response: Response<T>): Promise<Response<T>> {
    console.log('Response:', response)

    // 处理HTTP状态码
    if (response.statusCode !== StatusCode.SUCCESS) {
      throw this.createError(response.statusCode, '网络请求失败', response)
    }

    // 处理业务状态码
    if (response.data && !response.data.success) {
      throw this.createError(response.data.code, response.data.message, response.data)
    }

    return response
  }

  // 错误处理
  private createError(code: number, message: string, data?: any): Error {
    const error = new Error(message) as any
    error.code = code
    error.data = data
    error.timestamp = Date.now()
    return error
  }

  // 统一错误处理
  private handleError(error: any): void {
    console.error('Request Error:', error)

    let message = '请求失败'
    
    if (error.code) {
      switch (error.code) {
        case StatusCode.UNAUTHORIZED:
          message = '登录已过期，请重新登录'
          // 清除token并跳转到登录页
          uni.removeStorageSync('token')
          uni.removeStorageSync('userInfo')
          uni.reLaunch({ url: '/pages/user/login' })
          break
        case StatusCode.FORBIDDEN:
          message = '没有权限访问'
          break
        case StatusCode.NOT_FOUND:
          message = '请求的资源不存在'
          break
        case StatusCode.SERVER_ERROR:
          message = '服务器内部错误'
          break
        case StatusCode.NETWORK_ERROR:
          message = '网络连接失败'
          break
        case BusinessCode.PARAM_ERROR:
          message = '参数错误'
          break
        case BusinessCode.AUTH_ERROR:
          message = '认证失败'
          break
        case BusinessCode.PERMISSION_ERROR:
          message = '权限不足'
          break
        default:
          message = error.message || '未知错误'
      }
    } else {
      message = error.message || '网络异常，请稍后重试'
    }

    // 显示错误提示
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }

  // 生成请求唯一标识
  private generateRequestId(options: RequestOptions): string {
    return `${options.method || 'GET'}_${options.url}_${JSON.stringify(options.data || {})}`
  }

  // 取消重复请求
  private cancelDuplicateRequest(requestId: string): void {
    if (this.requestQueue.has(requestId)) {
      const request = this.requestQueue.get(requestId)
      if (request && request.abort) {
        request.abort()
      }
      this.requestQueue.delete(requestId)
    }
  }

  // 发送请求
  async request<T = any>(options: RequestOptions): Promise<T> {
    try {
      // 请求前拦截
      const processedOptions = await this.requestInterceptor(options)
      
      // 生成请求ID
      const requestId = this.generateRequestId(processedOptions)
      
      // 取消重复请求
      this.cancelDuplicateRequest(requestId)

      // 发送请求
      const requestPromise = uni.request(processedOptions)
      this.requestQueue.set(requestId, requestPromise)

      const response = await requestPromise
      
      // 移除请求记录
      this.requestQueue.delete(requestId)

      // 响应拦截
      const processedResponse = await this.responseInterceptor(response)
      
      return processedResponse.data.data
    } catch (error) {
      this.handleError(error)
      throw error
    }
  }

  // GET请求
  get<T = any>(url: string, params?: any, options?: Partial<RequestOptions>): Promise<T> {
    return this.request<T>({
      url,
      method: 'GET',
      data: params,
      ...options
    })
  }

  // POST请求
  post<T = any>(url: string, data?: any, options?: Partial<RequestOptions>): Promise<T> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  // PUT请求
  put<T = any>(url: string, data?: any, options?: Partial<RequestOptions>): Promise<T> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  // DELETE请求
  delete<T = any>(url: string, params?: any, options?: Partial<RequestOptions>): Promise<T> {
    return this.request<T>({
      url,
      method: 'DELETE',
      data: params,
      ...options
    })
  }

  // 上传文件
  upload(url: string, filePath: string, formData?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: this.config.baseURL + url,
        filePath,
        name: 'file',
        formData,
        header: {
          ...this.config.headers,
          Authorization: `Bearer ${uni.getStorageSync('token')}`
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.success) {
              resolve(data.data)
            } else {
              reject(new Error(data.message))
            }
          } catch (error) {
            reject(error)
          }
        },
        fail: reject
      })
    })
  }

  // 下载文件
  download(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      uni.downloadFile({
        url: this.config.baseURL + url,
        header: {
          ...this.config.headers,
          Authorization: `Bearer ${uni.getStorageSync('token')}`
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.tempFilePath)
          } else {
            reject(new Error('下载失败'))
          }
        },
        fail: reject
      })
    })
  }

  // 设置配置
  setConfig(config: Partial<HttpClientConfig>): void {
    this.config = { ...this.config, ...config }
  }

  // 获取配置
  getConfig(): HttpClientConfig {
    return { ...this.config }
  }
}

// 创建默认实例
const httpClient = new HttpClient({
  baseURL: process.env.NODE_ENV === 'development' ? 'http://localhost:8080' : 'https://api.example.com',
  timeout: 10000
})

// 导出实例和类
export { HttpClient }
export default httpClient
