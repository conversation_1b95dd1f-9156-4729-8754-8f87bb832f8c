/**
 * 节能灯管理系统 - 类型定义
 * Energy Light Management System - Type Definitions
 */

// ==================== 设备管理相关类型 ====================

/**
 * 设备信息接口
 */
export interface DeviceInfo {
	/** 设备ID */
	id: number;
	/** 设备名称 */
	deviceName: string;
	/** 设备编码 */
	deviceCode: string;
	/** 设备类型 */
	deviceType: "LED灯" | "节能灯" | "智能灯" | "感应灯";
	/** 设备位置 */
	location: string;
	/** 设备状态：1-在线 2-故障 3-离线 4-维护 */
	status: 1 | 2 | 3 | 4;
	/** 是否开启 */
	isOn: boolean;
	/** 亮度 (0-100) */
	brightness: number;
	/** 色温 (2700K-6500K) */
	colorTemp: number;
	/** 功率消耗 (W) */
	powerConsumption: number;
	/** 创建时间 */
	createTime: string;
	/** 更新时间 */
	updateTime?: string;
	/** 最后在线时间 */
	lastOnlineTime?: string;
	/** 固件版本 */
	firmwareVersion?: string;
	/** MAC地址 */
	macAddress?: string;
	/** IP地址 */
	ipAddress?: string;
}

/**
 * 设备控制请求
 */
export interface DeviceControlRequest {
	/** 设备ID */
	deviceId: number;
	/** 控制动作 */
	action: "toggle" | "brightness" | "colorTemp" | "scene" | "timer";
	/** 控制值 */
	value?: number | string | object;
}

/**
 * 批量设备控制请求
 */
export interface BatchDeviceControlRequest {
	/** 设备ID列表 */
	deviceIds: number[];
	/** 控制动作 */
	action: "toggle" | "brightness" | "colorTemp" | "scene";
	/** 控制值 */
	value?: number | string;
}

/**
 * 设备列表查询参数
 */
export interface DeviceListParams {
	/** 页码 */
	page?: number;
	/** 每页数量 */
	pageSize?: number;
	/** 搜索关键词 */
	keyword?: string;
	/** 设备状态 */
	status?: number;
	/** 设备类型 */
	deviceType?: string;
	/** 位置 */
	location?: string;
}

/**
 * 设备列表响应
 */
export interface DeviceListResponse {
	/** 设备列表 */
	list: DeviceInfo[];
	/** 总数 */
	total: number;
	/** 当前页 */
	page: number;
	/** 每页数量 */
	pageSize: number;
}

// ==================== 能耗监控相关类型 ====================

/**
 * 能耗数据
 */
export interface EnergyData {
	/** 时间戳 */
	time: string;
	/** 功率 (W) */
	power: number;
	/** 累计耗电量 (kWh) */
	consumption: number;
	/** 电压 (V) */
	voltage: number;
	/** 电流 (A) */
	current: number;
	/** 设备ID */
	deviceId?: number;
	/** 温度 (°C) */
	temperature?: number;
	/** 湿度 (%) */
	humidity?: number;
}

/**
 * 能耗统计数据
 */
export interface EnergyStats {
	/** 总耗电量 (kWh) */
	totalConsumption: number;
	/** 平均功率 (W) */
	averagePower: number;
	/** 峰值功率 (W) */
	peakPower: number;
	/** 节能率 (%) */
	savingRate: number;
	/** 电费成本 (元) */
	cost: number;
	/** 碳排放量 (kg) */
	carbonEmission?: number;
}

/**
 * 能耗查询参数
 */
export interface EnergyQueryParams {
	/** 设备ID */
	deviceId?: number;
	/** 开始时间 */
	startTime: string;
	/** 结束时间 */
	endTime: string;
	/** 时间粒度 */
	period: "minute" | "hour" | "day" | "month";
	/** 数据类型 */
	dataType?: "power" | "consumption" | "all";
}

/**
 * 节能分析结果
 */
export interface SavingAnalysis {
	/** 分析周期 */
	period: string;
	/** 节能建议 */
	suggestions: string[];
	/** 预计节能量 (kWh) */
	potentialSaving: number;
	/** 预计节省费用 (元) */
	potentialCostSaving: number;
	/** 优化方案 */
	optimizationPlans: OptimizationPlan[];
}

/**
 * 优化方案
 */
export interface OptimizationPlan {
	/** 方案名称 */
	name: string;
	/** 方案描述 */
	description: string;
	/** 预计节能效果 (%) */
	effectiveness: number;
	/** 实施难度 */
	difficulty: "easy" | "medium" | "hard";
}

// ==================== 照明控制相关类型 ====================

/**
 * 照明场景
 */
export interface LightingScene {
	/** 场景ID */
	id: number;
	/** 场景名称 */
	name: string;
	/** 场景描述 */
	description?: string;
	/** 亮度设置 */
	brightness: number;
	/** 色温设置 */
	colorTemp: number;
	/** 是否默认场景 */
	isDefault: boolean;
	/** 创建时间 */
	createTime: string;
}

/**
 * 定时任务
 */
export interface TimerTask {
	/** 任务ID */
	id: number;
	/** 任务名称 */
	name: string;
	/** 设备ID */
	deviceId: number;
	/** 执行时间 (cron表达式) */
	cronExpression: string;
	/** 执行动作 */
	action: "on" | "off" | "brightness" | "scene";
	/** 动作参数 */
	actionParams?: any;
	/** 是否启用 */
	enabled: boolean;
	/** 创建时间 */
	createTime: string;
}

// ==================== 故障管理相关类型 ====================

/**
 * 故障信息
 */
export interface FaultInfo {
	/** 故障ID */
	id: number;
	/** 设备ID */
	deviceId: number;
	/** 设备名称 */
	deviceName: string;
	/** 故障类型 */
	faultType: "通信故障" | "硬件故障" | "软件故障" | "电源故障" | "其他";
	/** 故障等级 */
	faultLevel: "低" | "中" | "高" | "紧急";
	/** 故障描述 */
	faultDescription: string;
	/** 故障状态 */
	status: "待处理" | "处理中" | "已解决" | "已关闭";
	/** 发生时间 */
	occurTime: string;
	/** 处理时间 */
	handleTime?: string;
	/** 解决时间 */
	resolveTime?: string;
	/** 处理人员 */
	handler?: string;
	/** 处理备注 */
	handleRemark?: string;
}

/**
 * 故障处理请求
 */
export interface FaultHandleRequest {
	/** 故障ID */
	faultId: number;
	/** 处理动作 */
	action: "assign" | "handle" | "resolve" | "close";
	/** 处理人员 */
	handler?: string;
	/** 处理备注 */
	remark?: string;
}

/**
 * 维护记录
 */
export interface MaintenanceRecord {
	/** 记录ID */
	id: number;
	/** 设备ID */
	deviceId: number;
	/** 设备名称 */
	deviceName: string;
	/** 维护类型 */
	maintenanceType: "定期维护" | "故障维修" | "升级更新" | "清洁保养";
	/** 维护内容 */
	content: string;
	/** 维护人员 */
	maintainer: string;
	/** 维护时间 */
	maintenanceTime: string;
	/** 维护结果 */
	result: "正常" | "异常" | "需要更换";
	/** 备注 */
	remark?: string;
	/** 费用 */
	cost?: number;
}

// ==================== 通用响应类型 ====================

/**
 * API响应基础结构
 */
export interface ApiResponse<T = any> {
	/** 响应码 */
	code: number;
	/** 响应消息 */
	message: string;
	/** 响应数据 */
	data: T;
	/** 时间戳 */
	timestamp?: number;
}

/**
 * 分页响应结构
 */
export interface PageResponse<T = any> {
	/** 数据列表 */
	list: T[];
	/** 总数 */
	total: number;
	/** 当前页 */
	page: number;
	/** 每页数量 */
	pageSize: number;
	/** 总页数 */
	totalPages: number;
}

/**
 * 分页查询参数
 */
export interface PageParams {
	/** 页码 */
	page?: number;
	/** 每页数量 */
	pageSize?: number;
	/** 排序字段 */
	sortBy?: string;
	/** 排序方向 */
	sortOrder?: "asc" | "desc";
}