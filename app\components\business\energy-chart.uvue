<template>
	<view class="energy-chart">
		<!-- 图表头部 -->
		<view class="chart-header">
			<text class="chart-title">{{ title }}</text>
			<view class="chart-controls">
				<view class="time-tabs">
					<view 
						v-for="tab in timeTabs" 
						:key="tab.value"
						class="time-tab"
						:class="{ active: activeTimeRange === tab.value }"
						@click="switchTimeRange(tab.value)"
					>
						{{ tab.label }}
					</view>
				</view>
				
				<view class="chart-type-switch">
					<cl-icon 
						:name="chartType === 'line' ? 'cl-icon-bar-chart' : 'cl-icon-line-chart'"
						size="20"
						color="#1890ff"
						@click="toggleChartType"
					/>
				</view>
			</view>
		</view>
		
		<!-- 统计概览 -->
		<view class="chart-summary">
			<view class="summary-item">
				<text class="summary-label">总耗电量</text>
				<text class="summary-value">{{ totalConsumption }}kWh</text>
				<view class="summary-trend" :class="{ increase: consumptionTrend > 0, decrease: consumptionTrend < 0 }">
					<cl-icon 
						:name="consumptionTrend > 0 ? 'cl-icon-arrow-up' : 'cl-icon-arrow-down'"
						size="12"
					/>
					<text class="trend-text">{{ Math.abs(consumptionTrend) }}%</text>
				</view>
			</view>
			
			<view class="summary-item">
				<text class="summary-label">平均功率</text>
				<text class="summary-value">{{ averagePower }}W</text>
				<view class="summary-trend" :class="{ increase: powerTrend > 0, decrease: powerTrend < 0 }">
					<cl-icon 
						:name="powerTrend > 0 ? 'cl-icon-arrow-up' : 'cl-icon-arrow-down'"
						size="12"
					/>
					<text class="trend-text">{{ Math.abs(powerTrend) }}%</text>
				</view>
			</view>
			
			<view class="summary-item">
				<text class="summary-label">节能率</text>
				<text class="summary-value">{{ energySavingRate }}%</text>
				<view class="summary-trend increase">
					<cl-icon name="cl-icon-arrow-up" size="12" />
					<text class="trend-text">{{ energySavingTrend }}%</text>
				</view>
			</view>
		</view>
		
		<!-- 图表容器 -->
		<view class="chart-container">
			<view v-if="loading" class="chart-loading">
				<cl-loading size="40" />
				<text class="loading-text">加载中...</text>
			</view>
			
			<view v-else-if="chartData.length === 0" class="chart-empty">
				<cl-icon name="cl-icon-chart" size="60" color="#ccc" />
				<text class="empty-text">暂无数据</text>
			</view>
			
			<view v-else class="chart-content">
				<!-- 简化的图表实现 -->
				<view class="chart-canvas">
					<!-- Y轴标签 -->
					<view class="y-axis">
						<view 
							v-for="(label, index) in yAxisLabels" 
							:key="index"
							class="y-label"
						>
							<text class="label-text">{{ label }}</text>
						</view>
					</view>
					
					<!-- 图表主体 -->
					<view class="chart-main">
						<!-- 网格线 -->
						<view class="grid-lines">
							<view 
								v-for="i in 5" 
								:key="i"
								class="grid-line"
								:style="{ top: (i - 1) * 20 + '%' }"
							></view>
						</view>
						
						<!-- 数据展示 -->
						<view class="data-container">
							<!-- 折线图 -->
							<view v-if="chartType === 'line'" class="line-chart">
								<view 
									v-for="(point, index) in chartPoints" 
									:key="index"
									class="chart-point"
									:style="{
										left: point.x + '%',
										bottom: point.y + '%'
									}"
									@click="showPointDetail(point, index)"
								>
									<view class="point-dot"></view>
									<view v-if="showValues" class="point-value">
										<text class="value-text">{{ point.value }}</text>
									</view>
								</view>
								
								<!-- 连接线 -->
								<view 
									v-for="(line, index) in chartLines" 
									:key="index"
									class="chart-line"
									:style="{
										left: line.x1 + '%',
										bottom: line.y1 + '%',
										width: line.width + '%',
										transform: `rotate(${line.angle}deg)`,
										'transform-origin': '0 50%'
									}"
								></view>
							</view>
							
							<!-- 柱状图 -->
							<view v-else class="bar-chart">
								<view 
									v-for="(bar, index) in chartBars" 
									:key="index"
									class="chart-bar"
									:style="{
										left: bar.x + '%',
										height: bar.height + '%',
										width: bar.width + '%'
									}"
									@click="showBarDetail(bar, index)"
								>
									<view v-if="showValues" class="bar-value">
										<text class="value-text">{{ bar.value }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					
					<!-- X轴标签 -->
					<view class="x-axis">
						<view 
							v-for="(label, index) in xAxisLabels" 
							:key="index"
							class="x-label"
						>
							<text class="label-text">{{ label }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 图表选项 -->
		<view class="chart-options">
			<view class="option-item">
				<cl-switch v-model="showValues" size="small" />
				<text class="option-label">显示数值</text>
			</view>
			
			<view class="option-item">
				<cl-switch v-model="showTrend" size="small" />
				<text class="option-label">显示趋势</text>
			</view>
			
			<view class="option-item" @click="exportChart">
				<cl-icon name="cl-icon-download" size="16" color="#1890ff" />
				<text class="option-label">导出图表</text>
			</view>
		</view>
		
		<!-- 数据详情弹窗 -->
		<cl-popup v-model="showDetailModal" direction="bottom">
			<view class="detail-modal">
				<view class="modal-header">
					<text class="modal-title">数据详情</text>
					<cl-button text @click="showDetailModal = false">关闭</cl-button>
				</view>
				
				<view v-if="selectedPoint" class="detail-content">
					<view class="detail-item">
						<text class="detail-label">时间</text>
						<text class="detail-value">{{ selectedPoint.time }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">数值</text>
						<text class="detail-value">{{ selectedPoint.value }}{{ unit }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">环比</text>
						<text class="detail-value" :class="{ increase: selectedPoint.change > 0, decrease: selectedPoint.change < 0 }">
							{{ selectedPoint.change > 0 ? '+' : '' }}{{ selectedPoint.change }}%
						</text>
					</view>
					<view v-if="selectedPoint.note" class="detail-item">
						<text class="detail-label">备注</text>
						<text class="detail-value">{{ selectedPoint.note }}</text>
					</view>
				</view>
			</view>
		</cl-popup>
	</view>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from "vue";

// Props
interface ChartProps {
	title?: string;
	data: Array<{
		time: string;
		value: number;
		change?: number;
		note?: string;
	}>;
	unit?: string;
	chartType?: 'line' | 'bar';
	showSummary?: boolean;
	showOptions?: boolean;
	height?: string;
}

const props = withDefaults(defineProps<ChartProps>(), {
	title: "能耗图表",
	unit: "kWh",
	chartType: "line",
	showSummary: true,
	showOptions: true,
	height: "300rpx"
});

// Emits
const emit = defineEmits<{
	timeRangeChange: [range: string];
	chartTypeChange: [type: string];
	pointClick: [point: any, index: number];
	export: [data: any[]];
}>();

// 响应式数据
const loading = ref(false);
const activeTimeRange = ref("day");
const chartType = ref(props.chartType);
const showValues = ref(true);
const showTrend = ref(true);
const showDetailModal = ref(false);
const selectedPoint = ref<any>(null);

// 时间范围选项
const timeTabs = [
	{ label: "今日", value: "day" },
	{ label: "本周", value: "week" },
	{ label: "本月", value: "month" },
	{ label: "本年", value: "year" }
];

// 图表数据
const chartData = computed(() => props.data || []);

// 统计数据
const totalConsumption = computed(() => {
	return chartData.value.reduce((sum, item) => sum + item.value, 0).toFixed(2);
});

const averagePower = computed(() => {
	if (chartData.value.length === 0) return 0;
	return (chartData.value.reduce((sum, item) => sum + item.value, 0) / chartData.value.length).toFixed(1);
});

const energySavingRate = computed(() => {
	// 模拟节能率计算
	return "15.6";
});

const consumptionTrend = computed(() => {
	// 模拟趋势计算
	return 5.2;
});

const powerTrend = computed(() => {
	// 模拟趋势计算
	return -2.1;
});

const energySavingTrend = computed(() => {
	// 模拟趋势计算
	return 3.4;
});

// Y轴标签
const yAxisLabels = computed(() => {
	if (chartData.value.length === 0) return [];
	
	const maxValue = Math.max(...chartData.value.map(item => item.value));
	const step = maxValue / 4;
	
	return [
		maxValue.toFixed(1),
		(maxValue * 0.75).toFixed(1),
		(maxValue * 0.5).toFixed(1),
		(maxValue * 0.25).toFixed(1),
		"0"
	];
});

// X轴标签
const xAxisLabels = computed(() => {
	return chartData.value.map(item => {
		const date = new Date(item.time);
		if (activeTimeRange.value === 'day') {
			return date.getHours().toString().padStart(2, '0') + ':00';
		} else if (activeTimeRange.value === 'week') {
			return ['日', '一', '二', '三', '四', '五', '六'][date.getDay()];
		} else if (activeTimeRange.value === 'month') {
			return date.getDate().toString();
		} else {
			return (date.getMonth() + 1).toString() + '月';
		}
	});
});

// 图表点位
const chartPoints = computed(() => {
	if (chartData.value.length === 0) return [];
	
	const maxValue = Math.max(...chartData.value.map(item => item.value));
	const dataLength = chartData.value.length;
	
	return chartData.value.map((item, index) => ({
		x: (index / (dataLength - 1)) * 90 + 5, // 5% 到 95% 的范围
		y: (item.value / maxValue) * 80 + 10, // 10% 到 90% 的范围
		value: item.value,
		time: item.time,
		change: item.change || 0,
		note: item.note
	}));
});

// 图表连接线
const chartLines = computed(() => {
	if (chartPoints.value.length < 2) return [];
	
	const lines = [];
	for (let i = 0; i < chartPoints.value.length - 1; i++) {
		const p1 = chartPoints.value[i];
		const p2 = chartPoints.value[i + 1];
		
		const dx = p2.x - p1.x;
		const dy = p2.y - p1.y;
		const width = Math.sqrt(dx * dx + dy * dy);
		const angle = Math.atan2(dy, dx) * 180 / Math.PI;
		
		lines.push({
			x1: p1.x,
			y1: p1.y,
			width,
			angle
		});
	}
	
	return lines;
});

// 图表柱状
const chartBars = computed(() => {
	if (chartData.value.length === 0) return [];
	
	const maxValue = Math.max(...chartData.value.map(item => item.value));
	const dataLength = chartData.value.length;
	const barWidth = 80 / dataLength; // 总宽度80%，平均分配
	
	return chartData.value.map((item, index) => ({
		x: index * barWidth + 10, // 从10%开始
		height: (item.value / maxValue) * 80, // 最高80%
		width: barWidth * 0.8, // 柱子宽度为分配宽度的80%
		value: item.value,
		time: item.time,
		change: item.change || 0,
		note: item.note
	}));
});

// 切换时间范围
const switchTimeRange = (range: string) => {
	activeTimeRange.value = range;
	emit('timeRangeChange', range);
};

// 切换图表类型
const toggleChartType = () => {
	chartType.value = chartType.value === 'line' ? 'bar' : 'line';
	emit('chartTypeChange', chartType.value);
};

// 显示点详情
const showPointDetail = (point: any, index: number) => {
	selectedPoint.value = point;
	showDetailModal.value = true;
	emit('pointClick', point, index);
};

// 显示柱详情
const showBarDetail = (bar: any, index: number) => {
	selectedPoint.value = bar;
	showDetailModal.value = true;
	emit('pointClick', bar, index);
};

// 导出图表
const exportChart = () => {
	emit('export', chartData.value);
	uni.showToast({
		title: "导出功能开发中",
		icon: "none"
	});
};

// 监听图表类型变化
watch(() => props.chartType, (newType) => {
	chartType.value = newType;
});

onMounted(() => {
	// 组件挂载后的初始化逻辑
});
</script>

<style scoped>
.energy-chart {
	background-color: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.chart-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.chart-title {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.chart-controls {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.time-tabs {
	display: flex;
	gap: 10rpx;
}

.time-tab {
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	background-color: #f0f0f0;
	color: #666;
	font-size: 12px;
	transition: all 0.3s ease;
}

.time-tab.active {
	background-color: #1890ff;
	color: white;
}

.chart-type-switch {
	padding: 10rpx;
	border-radius: 8rpx;
	background-color: #f9f9f9;
	transition: all 0.3s ease;
}

.chart-type-switch:active {
	background-color: #e6f7ff;
}

.chart-summary {
	display: flex;
	gap: 30rpx;
	margin-bottom: 30rpx;
	padding: 25rpx;
	background-color: #f9f9f9;
	border-radius: 12rpx;
}

.summary-item {
	flex: 1;
	text-align: center;
}

.summary-label {
	font-size: 12px;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.summary-value {
	font-size: 20px;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.summary-trend {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 5rpx;
}

.summary-trend.increase {
	color: #52c41a;
}

.summary-trend.decrease {
	color: #ff4d4f;
}

.trend-text {
	font-size: 12px;
}

.chart-container {
	position: relative;
	height: 300rpx;
	margin-bottom: 30rpx;
}

.chart-loading,
.chart-empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
	gap: 20rpx;
}

.loading-text,
.empty-text {
	font-size: 14px;
	color: #666;
}

.chart-content {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.chart-canvas {
	flex: 1;
	display: flex;
	position: relative;
}

.y-axis {
	width: 60rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	padding: 20rpx 0;
}

.y-label {
	text-align: right;
	padding-right: 10rpx;
}

.label-text {
	font-size: 10px;
	color: #666;
}

.chart-main {
	flex: 1;
	position: relative;
	margin: 20rpx 0;
}

.grid-lines {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.grid-line {
	position: absolute;
	left: 0;
	right: 0;
	height: 1rpx;
	background-color: #f0f0f0;
}

.data-container {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.line-chart,
.bar-chart {
	position: relative;
	height: 100%;
}

.chart-point {
	position: absolute;
	transform: translate(-50%, 50%);
	cursor: pointer;
}

.point-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background-color: #1890ff;
	border: 3rpx solid white;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
	transition: all 0.3s ease;
}

.chart-point:active .point-dot {
	transform: scale(1.2);
}

.point-value {
	position: absolute;
	top: -40rpx;
	left: 50%;
	transform: translateX(-50%);
	background-color: #333;
	color: white;
	padding: 5rpx 10rpx;
	border-radius: 6rpx;
	font-size: 10px;
	white-space: nowrap;
}

.point-value::after {
	content: '';
	position: absolute;
	top: 100%;
	left: 50%;
	transform: translateX(-50%);
	border: 5rpx solid transparent;
	border-top-color: #333;
}

.chart-line {
	position: absolute;
	height: 2rpx;
	background-color: #1890ff;
	transform-origin: 0 50%;
}

.chart-bar {
	position: absolute;
	bottom: 0;
	background: linear-gradient(to top, #1890ff, #69c0ff);
	border-radius: 4rpx 4rpx 0 0;
	cursor: pointer;
	transition: all 0.3s ease;
}

.chart-bar:active {
	opacity: 0.8;
	transform: scaleY(1.05);
}

.bar-value {
	position: absolute;
	top: -30rpx;
	left: 50%;
	transform: translateX(-50%);
	background-color: #333;
	color: white;
	padding: 5rpx 8rpx;
	border-radius: 4rpx;
	font-size: 10px;
	white-space: nowrap;
}

.x-axis {
	display: flex;
	justify-content: space-between;
	padding: 10rpx 60rpx 0 60rpx;
}

.x-label {
	text-align: center;
	flex: 1;
}

.chart-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20rpx;
	border-top: 1px solid #f0f0f0;
}

.option-item {
	display: flex;
	align-items: center;
	gap: 10rpx;
	cursor: pointer;
}

.option-label {
	font-size: 14px;
	color: #666;
}

.detail-modal {
	background-color: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 30rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.modal-title {
	font-size: 18px;
	font-weight: 500;
	color: #333;
}

.detail-content {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx 0;
	border-bottom: 1px solid #f9f9f9;
}

.detail-label {
	font-size: 14px;
	color: #666;
}

.detail-value {
	font-size: 14px;
	color: #333;
	font-weight: 500;
}

.detail-value.increase {
	color: #52c41a;
}

.detail-value.decrease {
	color: #ff4d4f;
}
</style>