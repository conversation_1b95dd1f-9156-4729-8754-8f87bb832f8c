import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { request } from '@/cool/service';

/**
 * 用户设置接口定义
 */
export interface UserSettings {
  id: string;
  userId: string;
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  currency: string;
  energyUnit: 'kWh' | 'MWh' | 'GWh';
  powerUnit: 'W' | 'kW' | 'MW';
  temperatureUnit: 'C' | 'F';
  notifications: NotificationSettings;
  dashboard: DashboardSettings;
  control: ControlSettings;
  privacy: PrivacySettings;
  createdAt: string;
  updatedAt: string;
}

/**
 * 通知设置
 */
export interface NotificationSettings {
  enabled: boolean;
  email: boolean;
  push: boolean;
  sms: boolean;
  deviceFault: boolean;
  energyAlert: boolean;
  maintenanceReminder: boolean;
  reportGenerated: boolean;
  systemUpdate: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  alertThresholds: {
    powerConsumption: number;
    energyCost: number;
    deviceOffline: number; // 分钟
    temperature: number;
  };
}

/**
 * 仪表板设置
 */
export interface DashboardSettings {
  layout: 'grid' | 'list' | 'card';
  refreshInterval: number; // 秒
  autoRefresh: boolean;
  showRealTime: boolean;
  defaultTimeRange: '1h' | '6h' | '24h' | '7d' | '30d';
  chartType: 'line' | 'bar' | 'area' | 'pie';
  widgets: {
    id: string;
    type: string;
    position: { x: number; y: number; w: number; h: number };
    visible: boolean;
    config: Record<string, any>;
  }[];
  favorites: string[]; // 收藏的设备ID
}

/**
 * 控制设置
 */
export interface ControlSettings {
  confirmBeforeAction: boolean;
  batchOperationLimit: number;
  defaultBrightness: number;
  defaultColorTemperature: number;
  scheduleEnabled: boolean;
  autoMode: {
    enabled: boolean;
    brightnessAdjustment: boolean;
    temperatureAdjustment: boolean;
    scheduleOptimization: boolean;
  };
  safetyLimits: {
    maxBrightness: number;
    minBrightness: number;
    maxTemperature: number;
    minTemperature: number;
    maxPower: number;
  };
}

/**
 * 隐私设置
 */
export interface PrivacySettings {
  dataCollection: boolean;
  analyticsTracking: boolean;
  locationTracking: boolean;
  usageStatistics: boolean;
  errorReporting: boolean;
  dataRetention: number; // 天数
  exportData: boolean;
  deleteAccount: boolean;
}

/**
 * 设置更新参数
 */
export interface SettingsUpdateParams {
  theme?: UserSettings['theme'];
  language?: UserSettings['language'];
  notifications?: Partial<NotificationSettings>;
  dashboard?: Partial<DashboardSettings>;
  control?: Partial<ControlSettings>;
  privacy?: Partial<PrivacySettings>;
}

/**
 * 用户设置状态管理
 */
export const useEnergySettingsStore = defineStore('energy-settings', () => {
  // 状态
  const settings = ref<UserSettings | null>(null);
  const loading = ref(false);
  const saving = ref(false);
  const lastSyncTime = ref<string | null>(null);
  const isDirty = ref(false); // 是否有未保存的更改
  const backupSettings = ref<UserSettings | null>(null); // 备份设置用于恢复

  // 计算属性
  const currentTheme = computed(() => {
    if (!settings.value) return 'light';
    
    if (settings.value.theme === 'auto') {
      // 根据系统主题自动切换
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    
    return settings.value.theme;
  });

  const isNotificationEnabled = computed(() => {
    return settings.value?.notifications.enabled ?? true;
  });

  const alertThresholds = computed(() => {
    return settings.value?.notifications.alertThresholds ?? {
      powerConsumption: 1000,
      energyCost: 100,
      deviceOffline: 5,
      temperature: 80
    };
  });

  const dashboardConfig = computed(() => {
    return settings.value?.dashboard ?? {
      layout: 'grid',
      refreshInterval: 30,
      autoRefresh: true,
      showRealTime: true,
      defaultTimeRange: '24h',
      chartType: 'line',
      widgets: [],
      favorites: []
    };
  });

  const controlConfig = computed(() => {
    return settings.value?.control ?? {
      confirmBeforeAction: true,
      batchOperationLimit: 50,
      defaultBrightness: 80,
      defaultColorTemperature: 4000,
      scheduleEnabled: true,
      autoMode: {
        enabled: false,
        brightnessAdjustment: true,
        temperatureAdjustment: true,
        scheduleOptimization: true
      },
      safetyLimits: {
        maxBrightness: 100,
        minBrightness: 1,
        maxTemperature: 6500,
        minTemperature: 2700,
        maxPower: 100
      }
    };
  });

  const privacyConfig = computed(() => {
    return settings.value?.privacy ?? {
      dataCollection: true,
      analyticsTracking: true,
      locationTracking: false,
      usageStatistics: true,
      errorReporting: true,
      dataRetention: 365,
      exportData: true,
      deleteAccount: false
    };
  });

  const favoriteDevices = computed(() => {
    return settings.value?.dashboard.favorites ?? [];
  });

  const isQuietHours = computed(() => {
    if (!settings.value?.notifications.quietHours.enabled) return false;
    
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    const startTime = parseTime(settings.value.notifications.quietHours.startTime);
    const endTime = parseTime(settings.value.notifications.quietHours.endTime);
    
    if (startTime <= endTime) {
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      // 跨天的情况
      return currentTime >= startTime || currentTime <= endTime;
    }
  });

  // Actions
  const fetchSettings = async () => {
    loading.value = true;
    try {
      const res = await request({
        url: '/energy/settings',
        method: 'GET'
      });

      settings.value = res.data;
      backupSettings.value = JSON.parse(JSON.stringify(res.data));
      lastSyncTime.value = new Date().toISOString();
      isDirty.value = false;

      // 应用主题设置
      applyTheme(currentTheme.value);
      
      return res.data;
    } catch (error) {
      console.error('获取用户设置失败:', error);
      // 使用默认设置
      settings.value = getDefaultSettings();
      throw error;
    } finally {
      loading.value = false;
    }
  };

  const updateSettings = async (updates: SettingsUpdateParams) => {
    if (!settings.value) return;

    saving.value = true;
    try {
      const res = await request({
        url: '/energy/settings',
        method: 'PUT',
        data: updates
      });

      // 更新本地设置
      Object.assign(settings.value, res.data);
      lastSyncTime.value = new Date().toISOString();
      isDirty.value = false;

      // 应用主题变更
      if (updates.theme) {
        applyTheme(currentTheme.value);
      }

      return res.data;
    } catch (error) {
      console.error('更新用户设置失败:', error);
      throw error;
    } finally {
      saving.value = false;
    }
  };

  const resetSettings = async () => {
    try {
      const res = await request({
        url: '/energy/settings/reset',
        method: 'POST'
      });

      settings.value = res.data;
      isDirty.value = false;
      applyTheme(currentTheme.value);

      return res.data;
    } catch (error) {
      console.error('重置设置失败:', error);
      throw error;
    }
  };

  const exportSettings = async () => {
    try {
      const res = await request({
        url: '/energy/settings/export',
        method: 'GET'
      });

      // 创建下载链接
      const blob = new Blob([JSON.stringify(res.data, null, 2)], { 
        type: 'application/json' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `energy-settings-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      window.URL.revokeObjectURL(url);

      return res.data;
    } catch (error) {
      console.error('导出设置失败:', error);
      throw error;
    }
  };

  const importSettings = async (settingsData: UserSettings) => {
    try {
      const res = await request({
        url: '/energy/settings/import',
        method: 'POST',
        data: settingsData
      });

      settings.value = res.data;
      isDirty.value = false;
      applyTheme(currentTheme.value);

      return res.data;
    } catch (error) {
      console.error('导入设置失败:', error);
      throw error;
    }
  };

  // 工具方法
  const updateTheme = (theme: UserSettings['theme']) => {
    if (!settings.value) return;
    
    settings.value.theme = theme;
    isDirty.value = true;
    applyTheme(currentTheme.value);
  };

  const updateLanguage = (language: UserSettings['language']) => {
    if (!settings.value) return;
    
    settings.value.language = language;
    isDirty.value = true;
    
    // 应用语言设置
    // 这里可以集成i18n
  };

  const updateNotificationSettings = (notifications: Partial<NotificationSettings>) => {
    if (!settings.value) return;
    
    Object.assign(settings.value.notifications, notifications);
    isDirty.value = true;
  };

  const updateDashboardSettings = (dashboard: Partial<DashboardSettings>) => {
    if (!settings.value) return;
    
    Object.assign(settings.value.dashboard, dashboard);
    isDirty.value = true;
  };

  const updateControlSettings = (control: Partial<ControlSettings>) => {
    if (!settings.value) return;
    
    Object.assign(settings.value.control, control);
    isDirty.value = true;
  };

  const updatePrivacySettings = (privacy: Partial<PrivacySettings>) => {
    if (!settings.value) return;
    
    Object.assign(settings.value.privacy, privacy);
    isDirty.value = true;
  };

  const addFavoriteDevice = (deviceId: string) => {
    if (!settings.value || settings.value.dashboard.favorites.includes(deviceId)) return;
    
    settings.value.dashboard.favorites.push(deviceId);
    isDirty.value = true;
  };

  const removeFavoriteDevice = (deviceId: string) => {
    if (!settings.value) return;
    
    const index = settings.value.dashboard.favorites.indexOf(deviceId);
    if (index > -1) {
      settings.value.dashboard.favorites.splice(index, 1);
      isDirty.value = true;
    }
  };

  const saveChanges = async () => {
    if (!isDirty.value || !settings.value) return;
    
    await updateSettings({
      theme: settings.value.theme,
      language: settings.value.language,
      notifications: settings.value.notifications,
      dashboard: settings.value.dashboard,
      control: settings.value.control,
      privacy: settings.value.privacy
    });
  };

  const discardChanges = () => {
    if (!backupSettings.value) return;
    
    settings.value = JSON.parse(JSON.stringify(backupSettings.value));
    isDirty.value = false;
    applyTheme(currentTheme.value);
  };

  // 辅助函数
  const getDefaultSettings = (): UserSettings => {
    return {
      id: '',
      userId: '',
      theme: 'light',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      timeFormat: '24h',
      currency: 'CNY',
      energyUnit: 'kWh',
      powerUnit: 'W',
      temperatureUnit: 'C',
      notifications: {
        enabled: true,
        email: true,
        push: true,
        sms: false,
        deviceFault: true,
        energyAlert: true,
        maintenanceReminder: true,
        reportGenerated: false,
        systemUpdate: true,
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00'
        },
        alertThresholds: {
          powerConsumption: 1000,
          energyCost: 100,
          deviceOffline: 5,
          temperature: 80
        }
      },
      dashboard: {
        layout: 'grid',
        refreshInterval: 30,
        autoRefresh: true,
        showRealTime: true,
        defaultTimeRange: '24h',
        chartType: 'line',
        widgets: [],
        favorites: []
      },
      control: {
        confirmBeforeAction: true,
        batchOperationLimit: 50,
        defaultBrightness: 80,
        defaultColorTemperature: 4000,
        scheduleEnabled: true,
        autoMode: {
          enabled: false,
          brightnessAdjustment: true,
          temperatureAdjustment: true,
          scheduleOptimization: true
        },
        safetyLimits: {
          maxBrightness: 100,
          minBrightness: 1,
          maxTemperature: 6500,
          minTemperature: 2700,
          maxPower: 100
        }
      },
      privacy: {
        dataCollection: true,
        analyticsTracking: true,
        locationTracking: false,
        usageStatistics: true,
        errorReporting: true,
        dataRetention: 365,
        exportData: true,
        deleteAccount: false
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  };

  const applyTheme = (theme: 'light' | 'dark') => {
    document.documentElement.setAttribute('data-theme', theme);
    document.documentElement.classList.toggle('dark', theme === 'dark');
  };

  const parseTime = (timeStr: string): number => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  };

  return {
    // 状态
    settings,
    loading,
    saving,
    lastSyncTime,
    isDirty,

    // 计算属性
    currentTheme,
    isNotificationEnabled,
    alertThresholds,
    dashboardConfig,
    controlConfig,
    privacyConfig,
    favoriteDevices,
    isQuietHours,

    // 方法
    fetchSettings,
    updateSettings,
    resetSettings,
    exportSettings,
    importSettings,
    updateTheme,
    updateLanguage,
    updateNotificationSettings,
    updateDashboardSettings,
    updateControlSettings,
    updatePrivacySettings,
    addFavoriteDevice,
    removeFavoriteDevice,
    saveChanges,
    discardChanges
  };
});