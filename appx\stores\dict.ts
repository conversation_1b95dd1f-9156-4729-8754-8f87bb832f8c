import { defineStore } from 'pinia'
import { ref } from 'vue'

// 字典项接口定义
interface DictItem {
  label: string
  value: string | number
  color?: string
  icon?: string
}

// 字典数据接口定义
interface DictData {
  [key: string]: DictItem[]
}

// 定义字典store
export const useDictStore = defineStore('dict', () => {
  // 响应式字典数据
  const dictData = ref<DictData>({})
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 初始化字典数据
  const initDictData = async () => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用获取字典数据
      const response = await uni.request({
        url: '/api/dict/all',
        method: 'GET'
      })

      if (response.statusCode === 200 && response.data.success) {
        dictData.value = response.data.data
      } else {
        // 设置默认字典数据
        dictData.value = {
          deviceType: [
            { label: 'LED灯', value: 'led_light', icon: 'light' },
            { label: '荧光灯', value: 'fluorescent', icon: 'light' },
            { label: '卤素灯', value: 'halogen', icon: 'light' },
            { label: '智能灯泡', value: 'smart_bulb', icon: 'smart' }
          ],
          deviceStatus: [
            { label: '在线', value: 'online', color: '#07c160' },
            { label: '离线', value: 'offline', color: '#999999' },
            { label: '故障', value: 'error', color: '#fa5151' },
            { label: '维护', value: 'maintenance', color: '#ff9500' }
          ],
          sceneMode: [
            { label: '居家模式', value: 'home', icon: 'home' },
            { label: '阅读模式', value: 'reading', icon: 'book' },
            { label: '影院模式', value: 'movie', icon: 'movie' },
            { label: '睡眠模式', value: 'sleep', icon: 'sleep' }
          ],
          dateRange: [
            { label: '今日', value: 'today' },
            { label: '昨日', value: 'yesterday' },
            { label: '近7天', value: '7days' },
            { label: '近30天', value: '30days' },
            { label: '本月', value: 'thisMonth' },
            { label: '上月', value: 'lastMonth' }
          ]
        }
      }
    } catch (err) {
      error.value = '字典数据加载失败'
      console.error('Failed to init dict data:', err)
      // 设置默认数据
      dictData.value = {
        deviceType: [
          { label: 'LED灯', value: 'led_light' },
          { label: '荧光灯', value: 'fluorescent' }
        ],
        deviceStatus: [
          { label: '在线', value: 'online' },
          { label: '离线', value: 'offline' }
        ]
      }
    } finally {
      loading.value = false
    }
  }

  // 获取字典数据
  const getDictData = (dictType: string): DictItem[] => {
    return dictData.value[dictType] || []
  }

  // 根据值获取标签
  const getDictLabel = (dictType: string, value: string | number): string => {
    const items = dictData.value[dictType] || []
    const item = items.find(item => item.value === value)
    return item?.label || String(value)
  }

  // 根据值获取颜色
  const getDictColor = (dictType: string, value: string | number): string => {
    const items = dictData.value[dictType] || []
    const item = items.find(item => item.value === value)
    return item?.color || '#333333'
  }

  // 根据值获取图标
  const getDictIcon = (dictType: string, value: string | number): string => {
    const items = dictData.value[dictType] || []
    const item = items.find(item => item.value === value)
    return item?.icon || ''
  }

  // 刷新字典数据
  const refreshDictData = async () => {
    await initDictData()
  }

  return {
    dictData,
    loading,
    error,
    initDictData,
    getDictData,
    getDictLabel,
    getDictColor,
    getDictIcon,
    refreshDictData
  }
})
