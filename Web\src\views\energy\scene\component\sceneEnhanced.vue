<template>
  <div class="scene-enhanced-container">
    <!-- 智能场景推荐 -->
    <el-card class="recommendation-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>智能场景推荐</span>
          <el-button type="text" @click="refreshRecommendations">
            <el-icon><Refresh /></el-icon>
            刷新推荐
          </el-button>
        </div>
      </template>
      
      <div class="recommendation-grid">
        <div 
          v-for="scene in recommendedScenes" 
          :key="scene.id"
          class="recommendation-item"
          @click="applyRecommendation(scene)"
        >
          <div class="recommendation-icon">
            <el-icon :size="24" :color="scene.iconColor">
              <component :is="scene.icon" />
            </el-icon>
          </div>
          <div class="recommendation-content">
            <h4>{{ scene.name }}</h4>
            <p>{{ scene.description }}</p>
            <div class="recommendation-stats">
              <span>节能: {{ scene.energySaving }}%</span>
              <span>设备: {{ scene.deviceCount }}台</span>
            </div>
          </div>
          <div class="recommendation-action">
            <el-button type="primary" size="small">应用</el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 场景模板库 -->
    <el-card class="template-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>场景模板库</span>
          <div class="header-actions">
            <el-select v-model="templateCategory" placeholder="选择分类" style="width: 150px" @change="filterTemplates">
              <el-option label="全部" value="all" />
              <el-option label="办公场所" value="office" />
              <el-option label="商业空间" value="commercial" />
              <el-option label="工业厂房" value="industrial" />
              <el-option label="公共区域" value="public" />
            </el-select>
            <el-button type="primary" @click="openTemplateDialog">创建模板</el-button>
          </div>
        </div>
      </template>
      
      <div class="template-grid">
        <div 
          v-for="template in filteredTemplates" 
          :key="template.id"
          class="template-item"
        >
          <div class="template-preview">
            <div class="template-image">
              <el-icon :size="32" :color="template.color">
                <component :is="template.icon" />
              </el-icon>
            </div>
            <div class="template-overlay">
              <el-button-group>
                <el-button type="primary" size="small" @click="useTemplate(template)">使用</el-button>
                <el-button type="success" size="small" @click="previewTemplate(template)">预览</el-button>
                <el-button type="warning" size="small" @click="editTemplate(template)">编辑</el-button>
              </el-button-group>
            </div>
          </div>
          <div class="template-info">
            <h4>{{ template.name }}</h4>
            <p>{{ template.description }}</p>
            <div class="template-tags">
              <el-tag v-for="tag in template.tags" :key="tag" size="small">{{ tag }}</el-tag>
            </div>
            <div class="template-stats">
              <span><el-icon><User /></el-icon> {{ template.usageCount }}</span>
              <span><el-icon><Star /></el-icon> {{ template.rating }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 场景分析 -->
    <el-card class="analysis-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>场景分析</span>
          <el-date-picker
            v-model="analysisDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="updateAnalysis"
          />
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="analysis-chart">
            <h4>场景执行频率</h4>
            <div ref="frequencyChartRef" style="height: 300px;"></div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="analysis-chart">
            <h4>节能效果分析</h4>
            <div ref="energyChartRef" style="height: 300px;"></div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="8">
          <div class="analysis-stat">
            <div class="stat-icon">
              <el-icon :size="24" color="#409EFF"><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ analysisData.totalExecutions }}</div>
              <div class="stat-label">总执行次数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="analysis-stat">
            <div class="stat-icon">
              <el-icon :size="24" color="#67C23A"><Lightning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ analysisData.energySaved }}kWh</div>
              <div class="stat-label">累计节能</div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="analysis-stat">
            <div class="stat-icon">
              <el-icon :size="24" color="#E6A23C"><Money /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">¥{{ analysisData.costSaved }}</div>
              <div class="stat-label">节省费用</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 场景模板创建/编辑弹窗 -->
    <el-dialog v-model="templateDialogVisible" :title="templateDialogTitle" width="800px" destroy-on-close>
      <el-form ref="templateFormRef" :model="templateForm" :rules="templateRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称" prop="name">
              <el-input v-model="templateForm.name" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板分类" prop="category">
              <el-select v-model="templateForm.category" placeholder="请选择分类" style="width: 100%">
                <el-option label="办公场所" value="office" />
                <el-option label="商业空间" value="commercial" />
                <el-option label="工业厂房" value="industrial" />
                <el-option label="公共区域" value="public" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="模板描述" prop="description">
          <el-input v-model="templateForm.description" type="textarea" :rows="3" placeholder="请输入模板描述" />
        </el-form-item>
        
        <el-form-item label="模板标签" prop="tags">
          <el-tag
            v-for="tag in templateForm.tags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            style="margin-right: 8px;"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="tagInputVisible"
            ref="tagInputRef"
            v-model="tagInputValue"
            size="small"
            style="width: 100px;"
            @keyup.enter="addTag"
            @blur="addTag"
          />
          <el-button v-else size="small" @click="showTagInput">+ 添加标签</el-button>
        </el-form-item>
        
        <el-form-item label="设备配置">
          <el-table :data="templateForm.deviceConfigs" border style="width: 100%">
            <el-table-column prop="deviceType" label="设备类型" width="120">
              <template #default="{ row }">
                <el-select v-model="row.deviceType" placeholder="选择类型" size="small">
                  <el-option label="LED灯" value="LED" />
                  <el-option label="节能灯" value="CFL" />
                  <el-option label="荧光灯" value="FL" />
                  <el-option label="卤素灯" value="HL" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="brightness" label="亮度(%)" width="120">
              <template #default="{ row }">
                <el-input-number v-model="row.brightness" :min="0" :max="100" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="isOn" label="开关状态" width="100">
              <template #default="{ row }">
                <el-switch v-model="row.isOn" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明" min-width="150">
              <template #default="{ row }">
                <el-input v-model="row.description" placeholder="配置说明" size="small" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ $index }">
                <el-button type="danger" size="small" @click="removeDeviceConfig($index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button type="primary" size="small" @click="addDeviceConfig" style="margin-top: 10px;">添加设备配置</el-button>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="templateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitTemplate" :loading="templateSubmitLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 场景预览弹窗 -->
    <el-dialog v-model="previewDialogVisible" title="场景预览" width="600px" destroy-on-close>
      <div class="scene-preview">
        <div class="preview-header">
          <h3>{{ previewScene.name }}</h3>
          <p>{{ previewScene.description }}</p>
        </div>
        
        <div class="preview-devices">
          <h4>设备配置</h4>
          <div class="device-list">
            <div v-for="config in previewScene.deviceConfigs" :key="config.id" class="device-item">
              <div class="device-icon">
                <el-icon :size="20" :color="config.isOn ? '#67C23A' : '#C0C4CC'">
                  <Lamp />
                </el-icon>
              </div>
              <div class="device-info">
                <span class="device-type">{{ config.deviceType }}</span>
                <span class="device-status">{{ config.isOn ? '开启' : '关闭' }}</span>
                <span v-if="config.isOn" class="device-brightness">亮度: {{ config.brightness }}%</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="preview-effects">
          <h4>预期效果</h4>
          <div class="effect-stats">
            <div class="effect-item">
              <span class="effect-label">节能效果:</span>
              <span class="effect-value">{{ previewScene.energySaving }}%</span>
            </div>
            <div class="effect-item">
              <span class="effect-label">影响设备:</span>
              <span class="effect-value">{{ previewScene.deviceCount }}台</span>
            </div>
            <div class="effect-item">
              <span class="effect-label">预计功耗:</span>
              <span class="effect-value">{{ previewScene.estimatedPower }}W</span>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="usePreviewedScene">使用此模板</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="sceneEnhanced">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import {
  Refresh, User, Star, TrendCharts, Lightning, Money, Lamp
} from '@element-plus/icons-vue';

// 组件事件
const emit = defineEmits(['refresh', 'createScene']);

// 响应式数据
const templateCategory = ref('all');
const analysisDateRange = ref([]);
const templateDialogVisible = ref(false);
const templateDialogTitle = ref('');
const templateSubmitLoading = ref(false);
const previewDialogVisible = ref(false);
const tagInputVisible = ref(false);
const tagInputValue = ref('');

const templateFormRef = ref();
const tagInputRef = ref();
const frequencyChartRef = ref();
const energyChartRef = ref();

// 推荐场景数据
const recommendedScenes = ref([
  {
    id: '1',
    name: '智能节能模式',
    description: '根据当前时间和使用情况，自动调节灯光亮度',
    icon: 'Lightning',
    iconColor: '#67C23A',
    energySaving: 35,
    deviceCount: 24,
    confidence: 0.92
  },
  {
    id: '2',
    name: '会议室优化',
    description: '会议期间提供最佳照明，会议结束后自动调暗',
    icon: 'User',
    iconColor: '#409EFF',
    energySaving: 28,
    deviceCount: 8,
    confidence: 0.87
  },
  {
    id: '3',
    name: '夜间安全模式',
    description: '夜间保持最低照明，确保安全的同时节约能源',
    icon: 'Moon',
    iconColor: '#E6A23C',
    energySaving: 45,
    deviceCount: 12,
    confidence: 0.95
  }
]);

// 场景模板数据
const sceneTemplates = ref([
  {
    id: '1',
    name: '办公室标准模式',
    description: '适用于一般办公环境的标准照明配置',
    category: 'office',
    icon: 'OfficeBuilding',
    color: '#409EFF',
    tags: ['办公', '标准', '节能'],
    usageCount: 156,
    rating: 4.8,
    deviceConfigs: [
      { deviceType: 'LED', brightness: 80, isOn: true, description: '主要照明' },
      { deviceType: 'LED', brightness: 60, isOn: true, description: '辅助照明' }
    ],
    energySaving: 25,
    deviceCount: 20,
    estimatedPower: 1200
  },
  {
    id: '2',
    name: '商场营业模式',
    description: '商业空间的高亮度照明配置，突出商品展示',
    category: 'commercial',
    icon: 'Shop',
    color: '#E6A23C',
    tags: ['商业', '高亮', '展示'],
    usageCount: 89,
    rating: 4.6,
    deviceConfigs: [
      { deviceType: 'LED', brightness: 95, isOn: true, description: '展示照明' },
      { deviceType: 'FL', brightness: 85, isOn: true, description: '环境照明' }
    ],
    energySaving: 15,
    deviceCount: 45,
    estimatedPower: 2800
  },
  {
    id: '3',
    name: '工厂生产模式',
    description: '工业环境的高效照明，确保生产安全',
    category: 'industrial',
    icon: 'Factory',
    color: '#F56C6C',
    tags: ['工业', '安全', '高效'],
    usageCount: 67,
    rating: 4.9,
    deviceConfigs: [
      { deviceType: 'FL', brightness: 100, isOn: true, description: '作业照明' },
      { deviceType: 'LED', brightness: 90, isOn: true, description: '安全照明' }
    ],
    energySaving: 20,
    deviceCount: 80,
    estimatedPower: 4500
  },
  {
    id: '4',
    name: '公共走廊模式',
    description: '公共区域的基础照明，兼顾安全和节能',
    category: 'public',
    icon: 'Guide',
    color: '#909399',
    tags: ['公共', '基础', '安全'],
    usageCount: 234,
    rating: 4.7,
    deviceConfigs: [
      { deviceType: 'LED', brightness: 50, isOn: true, description: '基础照明' }
    ],
    energySaving: 40,
    deviceCount: 15,
    estimatedPower: 450
  }
]);

const filteredTemplates = ref([...sceneTemplates.value]);

// 分析数据
const analysisData = reactive({
  totalExecutions: 1247,
  energySaved: 2856,
  costSaved: 1428
});

// 模板表单
const templateForm = reactive({
  id: undefined,
  name: '',
  category: '',
  description: '',
  tags: [],
  deviceConfigs: []
});

const templateRules = reactive({
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择模板分类', trigger: 'change' }],
  description: [{ required: true, message: '请输入模板描述', trigger: 'blur' }]
});

// 预览场景
const previewScene = ref({});

onMounted(() => {
  initCharts();
});

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    initFrequencyChart();
    initEnergyChart();
  });
};

// 初始化频率图表
const initFrequencyChart = () => {
  if (!frequencyChartRef.value) return;
  
  const chart = echarts.init(frequencyChartRef.value);
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['节能模式', '工作模式', '会议模式', '休息模式', '安全模式']
    },
    yAxis: {
      type: 'value',
      name: '执行次数'
    },
    series: [{
      name: '执行次数',
      type: 'bar',
      data: [320, 280, 150, 90, 180],
      itemStyle: {
        color: '#409EFF'
      }
    }]
  };
  chart.setOption(option);
};

// 初始化节能图表
const initEnergyChart = () => {
  if (!energyChartRef.value) return;
  
  const chart = echarts.init(energyChartRef.value);
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      name: '节能效果',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: '照明节能' },
        { value: 735, name: '智能调光' },
        { value: 580, name: '定时控制' },
        { value: 484, name: '场景优化' },
        { value: 300, name: '其他节能' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  chart.setOption(option);
};

// 刷新推荐
const refreshRecommendations = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    ElMessage.success('推荐已刷新');
  } catch (error) {
    ElMessage.error('刷新推荐失败');
  }
};

// 应用推荐
const applyRecommendation = (scene) => {
  emit('createScene', {
    name: scene.name,
    description: scene.description,
    type: 'recommended',
    config: scene
  });
  ElMessage.success(`已应用推荐场景：${scene.name}`);
};

// 筛选模板
const filterTemplates = () => {
  if (templateCategory.value === 'all') {
    filteredTemplates.value = [...sceneTemplates.value];
  } else {
    filteredTemplates.value = sceneTemplates.value.filter(
      template => template.category === templateCategory.value
    );
  }
};

// 使用模板
const useTemplate = (template) => {
  emit('createScene', {
    name: template.name,
    description: template.description,
    type: 'template',
    config: template
  });
  ElMessage.success(`已使用模板：${template.name}`);
};

// 预览模板
const previewTemplate = (template) => {
  previewScene.value = template;
  previewDialogVisible.value = true;
};

// 使用预览的场景
const usePreviewedScene = () => {
  useTemplate(previewScene.value);
  previewDialogVisible.value = false;
};

// 编辑模板
const editTemplate = (template) => {
  templateDialogTitle.value = '编辑场景模板';
  Object.assign(templateForm, {
    ...template,
    deviceConfigs: [...template.deviceConfigs]
  });
  templateDialogVisible.value = true;
};

// 打开模板对话框
const openTemplateDialog = () => {
  templateDialogTitle.value = '创建场景模板';
  Object.assign(templateForm, {
    id: undefined,
    name: '',
    category: '',
    description: '',
    tags: [],
    deviceConfigs: []
  });
  templateDialogVisible.value = true;
};

// 提交模板
const submitTemplate = async () => {
  try {
    await templateFormRef.value?.validate();
    
    templateSubmitLoading.value = true;
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    ElMessage.success(templateForm.id ? '更新模板成功' : '创建模板成功');
    templateDialogVisible.value = false;
  } catch (error) {
    console.error('提交模板失败:', error);
  } finally {
    templateSubmitLoading.value = false;
  }
};

// 添加标签
const addTag = () => {
  if (tagInputValue.value && !templateForm.tags.includes(tagInputValue.value)) {
    templateForm.tags.push(tagInputValue.value);
  }
  tagInputVisible.value = false;
  tagInputValue.value = '';
};

// 移除标签
const removeTag = (tag) => {
  const index = templateForm.tags.indexOf(tag);
  if (index > -1) {
    templateForm.tags.splice(index, 1);
  }
};

// 显示标签输入
const showTagInput = () => {
  tagInputVisible.value = true;
  nextTick(() => {
    tagInputRef.value?.focus();
  });
};

// 添加设备配置
const addDeviceConfig = () => {
  templateForm.deviceConfigs.push({
    deviceType: 'LED',
    brightness: 80,
    isOn: true,
    description: ''
  });
};

// 移除设备配置
const removeDeviceConfig = (index) => {
  templateForm.deviceConfigs.splice(index, 1);
};

// 更新分析
const updateAnalysis = () => {
  // 重新初始化图表
  initCharts();
  ElMessage.success('分析数据已更新');
};
</script>

<style scoped lang="scss">
.scene-enhanced-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }
}

// 推荐场景样式
.recommendation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }
  
  .recommendation-icon {
    margin-right: 16px;
  }
  
  .recommendation-content {
    flex: 1;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      color: #303133;
    }
    
    p {
      margin: 0 0 8px 0;
      font-size: 14px;
      color: #606266;
    }
    
    .recommendation-stats {
      display: flex;
      gap: 16px;
      font-size: 12px;
      color: #909399;
    }
  }
  
  .recommendation-action {
    margin-left: 16px;
  }
}

// 模板库样式
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.template-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.template-preview {
  position: relative;
  height: 120px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  
  .template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
  }
  
  &:hover .template-overlay {
    opacity: 1;
  }
}

.template-info {
  padding: 16px;
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #303133;
  }
  
  p {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #606266;
    line-height: 1.4;
  }
  
  .template-tags {
    margin-bottom: 12px;
    
    .el-tag {
      margin-right: 8px;
      margin-bottom: 4px;
    }
  }
  
  .template-stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #909399;
    
    span {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

// 分析样式
.analysis-chart {
  h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #303133;
  }
}

.analysis-stat {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  
  .stat-icon {
    margin-right: 16px;
  }
  
  .stat-content {
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      line-height: 1;
    }
    
    .stat-label {
      font-size: 14px;
      color: #909399;
      margin-top: 4px;
    }
  }
}

// 预览样式
.scene-preview {
  .preview-header {
    text-align: center;
    margin-bottom: 24px;
    
    h3 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #606266;
    }
  }
  
  .preview-devices {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
    }
  }
  
  .device-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .device-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    
    .device-icon {
      margin-right: 12px;
    }
    
    .device-info {
      display: flex;
      gap: 16px;
      align-items: center;
      
      .device-type {
        font-weight: 500;
        color: #303133;
      }
      
      .device-status {
        font-size: 14px;
        color: #606266;
      }
      
      .device-brightness {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .preview-effects {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
    }
  }
  
  .effect-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .effect-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    
    .effect-label {
      color: #606266;
    }
    
    .effect-value {
      font-weight: 500;
      color: #303133;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>