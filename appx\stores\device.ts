import { defineStore } from 'pinia'
import { ref } from 'vue'

// 设备接口定义
interface Device {
  id: string
  name: string
  type: string
  status: 'on' | 'off'
  brightness: number
  colorTemperature: number
  scene: string
  location: string
  lastActiveTime: string
}

// 定义设备store
export const useDeviceStore = defineStore('device', () => {
  // 响应式设备列表
  const devices = ref<Device[]>([])
  const currentDevice = ref<Device | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取设备列表
  const getDevices = async (params: { page: number, pageSize: number }) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      // 实际项目中应该替换为真实的API调用
      const response = await uni.request({
        url: '/api/devices',
        method: 'GET',
        data: params
      })

      if (response.statusCode === 200 && response.data.success) {
        const data = response.data.data
        devices.value = data.items
        return {
          items: data.items,
          total: data.total
        }
      } else {
        error.value = response.data.message || '获取设备列表失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return {
          items: [],
          total: 0
        }
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to get devices:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return {
        items: [],
        total: 0
      }
    } finally {
      loading.value = false
    }
  }

  // 获取设备详情
  const getDeviceDetail = async (deviceId: string) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}`,
        method: 'GET'
      })

      if (response.statusCode === 200 && response.data.success) {
        const deviceData = response.data.data
        currentDevice.value = deviceData
        return deviceData
      } else {
        error.value = response.data.message || '获取设备详情失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return null
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to get device detail:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return null
    } finally {
      loading.value = false
    }
  }

  // 控制设备开关
  const controlDevicePower = async (deviceId: string, power: boolean) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}/power`,
        method: 'POST',
        data: {
          power
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地设备状态
        const index = devices.value.findIndex(device => device.id === deviceId)
        if (index !== -1) {
          devices.value[index].status = power ? 'on' : 'off'
        }
        if (currentDevice.value && currentDevice.value.id === deviceId) {
          currentDevice.value.status = power ? 'on' : 'off'
        }
        uni.showToast({
          title: `设备已${power ? '开启' : '关闭'}`,
          icon: 'success'
        })
        return true
      } else {
        error.value = response.data.message || '控制设备失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to control device power:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 调节亮度
  const adjustBrightness = async (deviceId: string, brightness: number) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}/brightness`,
        method: 'POST',
        data: {
          brightness
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地设备状态
        const index = devices.value.findIndex(device => device.id === deviceId)
        if (index !== -1) {
          devices.value[index].brightness = brightness
        }
        if (currentDevice.value && currentDevice.value.id === deviceId) {
          currentDevice.value.brightness = brightness
        }
        return true
      } else {
        error.value = response.data.message || '调节亮度失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to adjust brightness:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 调节色温
  const adjustColorTemperature = async (deviceId: string, colorTemperature: number) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}/colorTemperature`,
        method: 'POST',
        data: {
          colorTemperature
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地设备状态
        const index = devices.value.findIndex(device => device.id === deviceId)
        if (index !== -1) {
          devices.value[index].colorTemperature = colorTemperature
        }
        if (currentDevice.value && currentDevice.value.id === deviceId) {
          currentDevice.value.colorTemperature = colorTemperature
        }
        return true
      } else {
        error.value = response.data.message || '调节色温失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to adjust color temperature:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 设置场景
  const setScene = async (deviceId: string, scene: string) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}/scene`,
        method: 'POST',
        data: {
          scene
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地设备状态
        const index = devices.value.findIndex(device => device.id === deviceId)
        if (index !== -1) {
          devices.value[index].scene = scene
        }
        if (currentDevice.value && currentDevice.value.id === deviceId) {
          currentDevice.value.scene = scene
        }
        return true
      } else {
        error.value = response.data.message || '设置场景失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to set scene:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    devices,
    currentDevice,
    loading,
    error,
    getDevices,
    getDeviceDetail,
    controlDevicePower,
    adjustBrightness,
    adjustColorTemperature,
    setScene
  }
})