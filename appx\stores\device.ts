import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { <PERSON>ce, DeviceStats, DeviceControlParams, PageParams, PageResult } from '@/types'
import { deviceApi } from '@/utils/api'

// 定义设备store
export const useDeviceStore = defineStore('device', () => {
  // 响应式设备列表
  const devices = ref<Device[]>([])
  const currentDevice = ref<Device | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取设备统计信息
  const getDeviceStats = async (): Promise<DeviceStats> => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: '/api/devices/stats',
        method: 'GET'
      })

      if (response.statusCode === 200 && response.data.success) {
        const stats = response.data.data
        return stats
      } else {
        // 返回模拟数据
        return {
          total: devices.value.length || 12,
          online: devices.value.filter(d => d.online).length || 8,
          offline: devices.value.filter(d => !d.online).length || 4
        }
      }
    } catch (err) {
      console.error('Failed to get device stats:', err)
      // 返回模拟数据
      return {
        total: 12,
        online: 8,
        offline: 4
      }
    } finally {
      loading.value = false
    }
  }

  // 获取设备列表
  const getDevices = async (params: { page: number, pageSize: number, keyword?: string }) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      // 实际项目中应该替换为真实的API调用
      const response = await uni.request({
        url: '/api/devices',
        method: 'GET',
        data: params
      })

      if (response.statusCode === 200 && response.data.success) {
        const data = response.data.data
        devices.value = data.items
        return {
          items: data.items,
          total: data.total
        }
      } else {
        // 返回模拟数据
        const mockDevices: Device[] = [
          {
            id: '1',
            name: '客厅主灯',
            type: 'led_light',
            status: 'online',
            online: true,
            powerOn: true,
            brightness: 80,
            colorTemp: 4000,
            colorTemperature: 4000,
            scene: 'home',
            location: '客厅',
            lastActiveTime: new Date().toISOString()
          },
          {
            id: '2',
            name: '卧室台灯',
            type: 'led_light',
            status: 'offline',
            online: true,
            powerOn: false,
            brightness: 0,
            colorTemp: 3000,
            colorTemperature: 3000,
            scene: 'sleep',
            location: '卧室',
            lastActiveTime: new Date().toISOString()
          }
        ]
        devices.value = mockDevices
        return {
          items: mockDevices,
          total: mockDevices.length
        }
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to get devices:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return {
        items: [],
        total: 0
      }
    } finally {
      loading.value = false
    }
  }

  // 获取设备详情
  const getDeviceDetail = async (deviceId: string) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}`,
        method: 'GET'
      })

      if (response.statusCode === 200 && response.data.success) {
        const deviceData = response.data.data
        currentDevice.value = deviceData
        return deviceData
      } else {
        error.value = response.data.message || '获取设备详情失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return null
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to get device detail:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return null
    } finally {
      loading.value = false
    }
  }

  // 控制设备开关
  const controlDevicePower = async (deviceId: string, power: boolean) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}/power`,
        method: 'POST',
        data: {
          power
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地设备状态
        const index = devices.value.findIndex(device => device.id === deviceId)
        if (index !== -1) {
          devices.value[index].status = power ? 'online' : 'offline'
          devices.value[index].powerOn = power
        }
        if (currentDevice.value && currentDevice.value.id === deviceId) {
          currentDevice.value.status = power ? 'online' : 'offline'
          currentDevice.value.powerOn = power
        }
        uni.showToast({
          title: `设备已${power ? '开启' : '关闭'}`,
          icon: 'success'
        })
        return true
      } else {
        error.value = response.data.message || '控制设备失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to control device power:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 调节亮度
  const adjustBrightness = async (deviceId: string, brightness: number) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}/brightness`,
        method: 'POST',
        data: {
          brightness
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地设备状态
        const index = devices.value.findIndex(device => device.id === deviceId)
        if (index !== -1) {
          devices.value[index].brightness = brightness
        }
        if (currentDevice.value && currentDevice.value.id === deviceId) {
          currentDevice.value.brightness = brightness
        }
        return true
      } else {
        error.value = response.data.message || '调节亮度失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to adjust brightness:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 调节色温
  const adjustColorTemperature = async (deviceId: string, colorTemperature: number) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}/colorTemperature`,
        method: 'POST',
        data: {
          colorTemperature
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地设备状态
        const index = devices.value.findIndex(device => device.id === deviceId)
        if (index !== -1) {
          devices.value[index].colorTemperature = colorTemperature
        }
        if (currentDevice.value && currentDevice.value.id === deviceId) {
          currentDevice.value.colorTemperature = colorTemperature
        }
        return true
      } else {
        error.value = response.data.message || '调节色温失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to adjust color temperature:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 获取设备状态
  const getDeviceStatus = async (deviceId: string) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}/status`,
        method: 'GET'
      })

      if (response.statusCode === 200 && response.data.success) {
        return response.data.data
      } else {
        // 返回模拟数据
        const device = devices.value.find(d => d.id === deviceId)
        return {
          powerOn: device?.powerOn || false,
          brightness: device?.brightness || 50,
          colorTemp: device?.colorTemp || 4000,
          scene: device?.scene || 'home'
        }
      }
    } catch (err) {
      console.error('Failed to get device status:', err)
      return {
        powerOn: false,
        brightness: 50,
        colorTemp: 4000,
        scene: 'home'
      }
    } finally {
      loading.value = false
    }
  }

  // 控制设备
  const controlDevice = async (deviceId: string, params: any) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}/control`,
        method: 'POST',
        data: params
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地设备状态
        const index = devices.value.findIndex(device => device.id === deviceId)
        if (index !== -1) {
          if (params.powerOn !== undefined) {
            devices.value[index].powerOn = params.powerOn
            devices.value[index].status = params.powerOn ? 'online' : 'offline'
          }
          if (params.brightness !== undefined) {
            devices.value[index].brightness = params.brightness
          }
          if (params.colorTemp !== undefined) {
            devices.value[index].colorTemp = params.colorTemp
            devices.value[index].colorTemperature = params.colorTemp
          }
        }
        return true
      } else {
        error.value = response.data.message || '控制设备失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to control device:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 设置场景
  const setScene = async (deviceId: string, scene: string) => {
    try {
      loading.value = true
      error.value = null

      // 模拟API调用
      const response = await uni.request({
        url: `/api/devices/${deviceId}/scene`,
        method: 'POST',
        data: {
          scene
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        // 更新本地设备状态
        const index = devices.value.findIndex(device => device.id === deviceId)
        if (index !== -1) {
          devices.value[index].scene = scene as any
        }
        if (currentDevice.value && currentDevice.value.id === deviceId) {
          currentDevice.value.scene = scene as any
        }
        return true
      } else {
        error.value = response.data.message || '设置场景失败'
        uni.showToast({
          title: error.value,
          icon: 'none'
        })
        return false
      }
    } catch (err) {
      error.value = '网络错误，请重试'
      console.error('Failed to set scene:', err)
      uni.showToast({
        title: error.value,
        icon: 'none'
      })
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    devices,
    currentDevice,
    loading,
    error,
    getDeviceStats,
    getDevices,
    getDeviceDetail,
    getDeviceStatus,
    controlDevice,
    controlDevicePower,
    adjustBrightness,
    adjustColorTemperature,
    setScene
  }
})