// UniApp 类型声明文件

declare namespace UniApp {
  interface RequestOptions {
    url: string
    data?: any
    header?: any
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
    timeout?: number
    dataType?: string
    responseType?: string
    success?: (result: RequestSuccessCallbackResult) => void
    fail?: (result: any) => void
    complete?: (result: any) => void
  }

  interface RequestSuccessCallbackResult {
    data: any
    statusCode: number
    header: any
    cookies?: string[]
  }

  interface ShowToastOptions {
    title: string
    icon?: 'success' | 'loading' | 'none'
    image?: string
    duration?: number
    mask?: boolean
    success?: () => void
    fail?: () => void
    complete?: () => void
  }

  interface NavigateToOptions {
    url: string
    success?: () => void
    fail?: () => void
    complete?: () => void
  }

  interface NavigateBackOptions {
    delta?: number
    success?: () => void
    fail?: () => void
    complete?: () => void
  }

  interface SetStorageOptions {
    key: string
    data: any
    success?: () => void
    fail?: () => void
    complete?: () => void
  }

  interface GetStorageOptions {
    key: string
    success?: (result: { data: any }) => void
    fail?: () => void
    complete?: () => void
  }

  interface RemoveStorageOptions {
    key: string
    success?: () => void
    fail?: () => void
    complete?: () => void
  }

  interface SetNavigationBarColorOptions {
    frontColor: string
    backgroundColor: string
    animation?: {
      duration?: number
      timingFunc?: string
    }
    success?: () => void
    fail?: () => void
    complete?: () => void
  }
}

declare const uni: {
  request(options: UniApp.RequestOptions): Promise<UniApp.RequestSuccessCallbackResult>
  showToast(options: UniApp.ShowToastOptions): void
  navigateTo(options: UniApp.NavigateToOptions): void
  navigateBack(options?: UniApp.NavigateBackOptions): void
  setStorage(options: UniApp.SetStorageOptions): void
  setStorageSync(key: string, data: any): void
  getStorage(options: UniApp.GetStorageOptions): void
  getStorageSync(key: string): any
  removeStorage(options: UniApp.RemoveStorageOptions): void
  removeStorageSync(key: string): void
  setNavigationBarColor(options: UniApp.SetNavigationBarColorOptions): void
}

// Vue 相关类型扩展
declare module 'vue' {
  interface ComponentCustomProperties {
    $uni: typeof uni
  }
}

// 全局类型
declare global {
  const uni: typeof uni
  
  // 页面生命周期
  function onLaunch(callback: () => void): void
  function onShow(callback: () => void): void
  function onHide(callback: () => void): void
  function onLoad(callback: (options: any) => void): void
  function onReady(callback: () => void): void
  function onUnload(callback: () => void): void
  function onPullDownRefresh(callback: () => void): void
  function onReachBottom(callback: () => void): void
  function onShareAppMessage(callback: () => any): void
  function onPageScroll(callback: (event: { scrollTop: number }) => void): void
  function onResize(callback: (event: { size: { windowWidth: number, windowHeight: number } }) => void): void
  function onTabItemTap(callback: (event: { index: number, pagePath: string, text: string }) => void): void
}

export {}
