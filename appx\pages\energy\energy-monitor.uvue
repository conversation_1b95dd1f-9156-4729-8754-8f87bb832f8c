<template>
  <cl-page>
    <view class="energy-monitor-container">
      <!-- 顶部状态栏 -->
      <view class="status-bar"></view>
      
      <!-- 筛选条件 -->
      <view class="filter-bar">
        <picker @change="onDateRangeChange" :value="dateRangeIndex" :range="dateRangeOptions">
          <view class="picker">
            {{ dateRangeOptions[dateRangeIndex] }}
          </view>
        </picker>
        <picker @change="onDeviceChange" :value="deviceIndex" :range="deviceNames" range-key="name">
          <view class="picker">
            {{ deviceIndex === 0 ? '所有设备' : deviceNames[deviceIndex-1]?.name || '所有设备' }}
          </view>
        </picker>
      </view>
      
      <!-- 能耗概览 -->
      <view class="overview-cards">
        <view class="card">
          <text class="card-title">总能耗</text>
          <text class="card-value">{{ totalEnergy }} kWh</text>
          <text class="card-delta" :class="totalEnergyDelta >= 0 ? 'delta-up' : 'delta-down'">{{ totalEnergyDelta >= 0 ? '+' : '' }}{{ totalEnergyDelta }}%</text>
        </view>
        <view class="card">
          <text class="card-title">平均能耗</text>
          <text class="card-value">{{ avgEnergy }} kWh/天</text>
          <text class="card-delta" :class="avgEnergyDelta >= 0 ? 'delta-up' : 'delta-down'">{{ avgEnergyDelta >= 0 ? '+' : '' }}{{ avgEnergyDelta }}%</text>
        </view>
        <view class="card">
          <text class="card-title">节能效果</text>
          <text class="card-value">{{ savingEffect }} %</text>
          <text class="card-delta delta-up">{{ savingAmount }} kWh</text>
        </view>
      </view>
      
      <!-- 能耗趋势图表 -->
      <view class="chart-container">
        <text class="chart-title">能耗趋势</text>
        <!-- 这里将使用echarts或其他图表组件 -->
        <view class="chart-placeholder">
          <text>图表加载中...</text>
        </view>
      </view>
      
      <!-- 能耗明细 -->
      <view class="detail-section">
        <text class="section-title">能耗明细</text>
        <list-view class="detail-list" @scrolltolower="loadMore">
          <list-item v-for="(item, index) in energyDetails" :key="index" class="detail-item">
            <view class="detail-info">
              <text class="detail-date">{{ item.date }}</text>
              <text class="detail-device">{{ item.deviceName }}</text>
            </view>
            <view class="detail-value">
              <text class="value">{{ item.energy }} kWh</text>
            </view>
          </list-item>
        </list-view>
      </view>
    </view>
  </cl-page>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useEnergyStore } from '@/stores/energy'
import { useDeviceStore } from '@/stores/device'

// 响应式数据
const dateRangeOptions = ['今日', '本周', '本月', '今年', '自定义']
const dateRangeIndex = ref(0)
const devices = ref<any[]>([])
const deviceNames = computed(() => [{'name': '所有设备'}, ...devices.value])
const deviceIndex = ref(0)
const totalEnergy = ref(0)
const totalEnergyDelta = ref(0)
const avgEnergy = ref(0)
const avgEnergyDelta = ref(0)
const savingEffect = ref(0)
const savingAmount = ref(0)
const energyDetails = ref<any[]>([])
const energyTrend = ref<any[]>([])
const loading = ref(false)
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)

// stores
const energyStore = useEnergyStore()
const deviceStore = useDeviceStore()

// 生命周期
onMounted(() => {
  console.log('Energy monitor page mounted')
  // 初始化设备列表
  initDevices()
  // 初始化能耗数据
  initEnergyData()
})

// 初始化设备列表
const initDevices = async () => {
  try {
    const data = await deviceStore.getDevices({ page: 1, pageSize: 100 })
    devices.value = data.items
  } catch (error) {
    console.error('Failed to load devices:', error)
    uni.showToast({
      title: '设备加载失败',
      icon: 'none'
    })
  }
}

// 初始化能耗数据
const initEnergyData = async () => {
  try {
    loading.value = true
    const deviceId = deviceIndex.value === 0 ? '' : devices.value[deviceIndex.value - 1]?.id || ''
    const dateRange = dateRangeOptions[dateRangeIndex.value]

    // 获取能耗概览
    const overviewData = await energyStore.getEnergyOverview(deviceId, dateRange)
    totalEnergy.value = overviewData.totalEnergy
    totalEnergyDelta.value = overviewData.totalEnergyDelta
    avgEnergy.value = overviewData.avgEnergy
    avgEnergyDelta.value = overviewData.avgEnergyDelta
    savingEffect.value = overviewData.savingEffect
    savingAmount.value = overviewData.savingAmount

    // 获取能耗趋势
    const trendData = await energyStore.getEnergyTrend(deviceId, dateRange)
    energyTrend.value = trendData

    // 获取能耗明细
    const detailData = await energyStore.getEnergyDetails(deviceId, dateRange, { page: 1, pageSize: pageSize.value })
    energyDetails.value = detailData.items
    hasMore.value = detailData.total > energyDetails.value.length
    page.value = 1
  } catch (error) {
    console.error('Failed to load energy data:', error)
    uni.showToast({
      title: '能耗数据加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载更多能耗明细
const loadMore = async () => {
  if (loading.value || !hasMore.value) return

  try {
    loading.value = true
    page.value += 1
    const deviceId = deviceIndex.value === 0 ? '' : devices.value[deviceIndex.value - 1]?.id || ''
    const dateRange = dateRangeOptions[dateRangeIndex.value]

    const detailData = await energyStore.getEnergyDetails(deviceId, dateRange, { page: page.value, pageSize: pageSize.value })
    energyDetails.value = [...energyDetails.value, ...detailData.items]
    hasMore.value = energyDetails.value.length < detailData.total
  } catch (error) {
    console.error('Failed to load more energy details:', error)
    page.value -= 1
  } finally {
    loading.value = false
  }
}

// 切换日期范围
const onDateRangeChange = (e: any) => {
  const index = e.detail.value
  dateRangeIndex.value = index
  // 如果是自定义日期范围，这里可以打开日期选择器
  if (index === 4) {
    // 这里可以实现自定义日期选择逻辑
    uni.showToast({
      title: '自定义日期功能待实现',
      icon: 'none'
    })
    // 暂时切换回'本月'
    dateRangeIndex.value = 2
  }
  // 重新加载数据
  initEnergyData()
}

// 切换设备
const onDeviceChange = (e: any) => {
  const index = e.detail.value
  deviceIndex.value = index
  // 重新加载数据
  initEnergyData()
}
</script>

<style>
.energy-monitor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.status-bar {
  height: var(--status-bar-height);
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
}

.filter-bar {
  margin-top: var(--status-bar-height);
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.picker {
  background-color: #ffffff;
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
  font-size: 14px;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 48%;
  text-align: center;
}

.overview-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.card {
  flex: 1;
  margin: 0 10rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 14px;
  color: #666;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-top: 10rpx;
}

.card-delta {
  font-size: 12px;
  margin-top: 5rpx;
  display: block;
}

.delta-up {
  color: #f5222d;
}

.delta-down {
  color: #07c160;
}

.chart-container {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.chart-placeholder {
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
}

.detail-section {
  flex: 1;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.detail-list {
  flex: 1;
}

.detail-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-info {
  flex: 1;
}

.detail-date {
  font-size: 14px;
  color: #333;
}

.detail-device {
  font-size: 12px;
  color: #666;
  margin-top: 5rpx;
}

.detail-value {
  text-align: right;
}

.value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
</style>