// 本地存储工具类
import { StorageKeys } from '@/types'

// 存储接口
interface StorageItem<T = any> {
  value: T
  timestamp: number
  expire?: number
}

// 存储工具类
class Storage {
  // 设置存储项
  set<T = any>(key: string, value: T, expire?: number): void {
    try {
      const item: StorageItem<T> = {
        value,
        timestamp: Date.now(),
        expire: expire ? Date.now() + expire * 1000 : undefined
      }
      uni.setStorageSync(key, JSON.stringify(item))
    } catch (error) {
      console.error('Storage set error:', error)
    }
  }

  // 获取存储项
  get<T = any>(key: string, defaultValue?: T): T | undefined {
    try {
      const itemStr = uni.getStorageSync(key)
      if (!itemStr) {
        return defaultValue
      }

      const item: StorageItem<T> = JSON.parse(itemStr)
      
      // 检查是否过期
      if (item.expire && Date.now() > item.expire) {
        this.remove(key)
        return defaultValue
      }

      return item.value
    } catch (error) {
      console.error('Storage get error:', error)
      return defaultValue
    }
  }

  // 移除存储项
  remove(key: string): void {
    try {
      uni.removeStorageSync(key)
    } catch (error) {
      console.error('Storage remove error:', error)
    }
  }

  // 清空所有存储
  clear(): void {
    try {
      uni.clearStorageSync()
    } catch (error) {
      console.error('Storage clear error:', error)
    }
  }

  // 检查存储项是否存在
  has(key: string): boolean {
    try {
      const value = uni.getStorageSync(key)
      return value !== null && value !== undefined && value !== ''
    } catch (error) {
      console.error('Storage has error:', error)
      return false
    }
  }

  // 获取所有存储键
  keys(): string[] {
    try {
      const info = uni.getStorageInfoSync()
      return info.keys || []
    } catch (error) {
      console.error('Storage keys error:', error)
      return []
    }
  }

  // 获取存储信息
  info(): { keys: string[]; currentSize: number; limitSize: number } {
    try {
      return uni.getStorageInfoSync()
    } catch (error) {
      console.error('Storage info error:', error)
      return { keys: [], currentSize: 0, limitSize: 0 }
    }
  }

  // 批量设置
  setMultiple(items: Record<string, any>): void {
    Object.entries(items).forEach(([key, value]) => {
      this.set(key, value)
    })
  }

  // 批量获取
  getMultiple<T = any>(keys: string[]): Record<string, T> {
    const result: Record<string, T> = {}
    keys.forEach(key => {
      result[key] = this.get<T>(key)
    })
    return result
  }

  // 批量移除
  removeMultiple(keys: string[]): void {
    keys.forEach(key => {
      this.remove(key)
    })
  }
}

// 创建存储实例
const storage = new Storage()

// 便捷方法
export const setStorage = storage.set.bind(storage)
export const getStorage = storage.get.bind(storage)
export const removeStorage = storage.remove.bind(storage)
export const clearStorage = storage.clear.bind(storage)
export const hasStorage = storage.has.bind(storage)

// 特定业务存储方法
export const setUserInfo = (userInfo: any) => {
  storage.set(StorageKeys.USER_INFO, userInfo)
}

export const getUserInfo = () => {
  return storage.get(StorageKeys.USER_INFO)
}

export const removeUserInfo = () => {
  storage.remove(StorageKeys.USER_INFO)
}

export const setToken = (token: string) => {
  storage.set('token', token)
}

export const getToken = () => {
  return storage.get<string>('token')
}

export const removeToken = () => {
  storage.remove('token')
}

export const setTheme = (theme: string) => {
  storage.set(StorageKeys.THEME, theme)
}

export const getTheme = () => {
  return storage.get<string>(StorageKeys.THEME, 'light')
}

export const setLanguage = (language: string) => {
  storage.set(StorageKeys.LANGUAGE, language)
}

export const getLanguage = () => {
  return storage.get<string>(StorageKeys.LANGUAGE, 'zh-CN')
}

// 缓存相关方法
export const setCache = <T = any>(key: string, value: T, expire: number = 3600) => {
  storage.set(key, value, expire)
}

export const getCache = <T = any>(key: string, defaultValue?: T) => {
  return storage.get<T>(key, defaultValue)
}

export const removeCache = (key: string) => {
  storage.remove(key)
}

export const clearCache = () => {
  const keys = storage.keys()
  const cacheKeys = keys.filter(key => key.endsWith('_cache') || key.startsWith('cache_'))
  storage.removeMultiple(cacheKeys)
}

// 导出存储类和实例
export { Storage }
export default storage
