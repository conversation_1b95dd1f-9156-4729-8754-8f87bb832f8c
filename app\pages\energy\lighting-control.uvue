<template>
	<cl-page>
		<cl-topbar title="照明控制">
			<template #right>
				<cl-icon name="cl-icon-setting" size="20" @tap="openGlobalSettings" />
			</template>
		</cl-topbar>

		<view class="lighting-control">
			<!-- 设备选择器 -->
			<view class="device-selector">
				<view class="section-title">
					<cl-icon name="cl-icon-device" size="18" color="#1890ff" />
					<text>选择设备</text>
					<view class="device-count">共{{ deviceList.length }}台设备</view>
				</view>
				<cl-select
					v-model="selectedDeviceId"
					placeholder="请选择设备"
					@change="handleDeviceChange"
				>
					<cl-option v-for="device in deviceList" :key="device.id" :value="device.id">
						<view class="device-option">
							<view class="device-info">
								<text class="device-name">{{ device.name }}</text>
								<text class="device-location">{{ device.location }}</text>
							</view>
							<view class="device-status" :class="device.status">
								<cl-icon
									:name="
										device.status === 'online'
											? 'cl-icon-check-circle'
											: 'cl-icon-close-circle'
									"
									size="14"
								/>
								<text>{{ device.status === "online" ? "在线" : "离线" }}</text>
							</view>
						</view>
					</cl-option>
				</cl-select>

				<!-- 当前设备状态 -->
				<view v-if="currentDevice" class="current-device-status">
					<view class="status-item">
						<text class="status-label">状态</text>
						<view class="status-value" :class="currentDevice.status">
							<cl-icon
								:name="currentDevice.isOn ? 'cl-icon-bulb' : 'cl-icon-bulb-off'"
								size="16"
							/>
							<text>{{ currentDevice.isOn ? "开启" : "关闭" }}</text>
						</view>
					</view>
					<view class="status-item">
						<text class="status-label">亮度</text>
						<text class="status-value">{{ currentDevice.brightness }}%</text>
					</view>
					<view class="status-item">
						<text class="status-label">色温</text>
						<text class="status-value">{{ currentDevice.colorTemp }}K</text>
					</view>
					<view class="status-item">
						<text class="status-label">功率</text>
						<text class="status-value">{{ currentDevice.power }}W</text>
					</view>
				</view>
			</view>

			<!-- 主控制面板 -->
			<view v-if="currentDevice" class="main-control">
				<lighting-panel
					:device="currentDevice"
					@brightness-change="handleBrightnessChange"
					@color-temp-change="handleColorTempChange"
					@scene-change="handleSceneChange"
					@color-change="handleColorChange"
				/>
			</view>

			<!-- 场景模式 -->
			<view class="scene-modes">
				<view class="section-title">
					<cl-icon name="cl-icon-palette" size="18" color="#1890ff" />
					<text>场景模式</text>
					<cl-button size="small" type="text" @tap="openSceneManager">
						<cl-icon name="cl-icon-setting" size="14" />
						管理
					</cl-button>
				</view>

				<!-- 快捷场景 -->
				<view class="scene-grid">
					<view
						v-for="scene in quickScenes"
						:key="scene.id"
						class="scene-item"
						:class="{ active: currentScene === scene.id }"
						@tap="selectScene(scene)"
					>
						<cl-icon
							:name="scene.icon"
							size="24"
							:color="currentScene === scene.id ? '#1890ff' : '#666'"
						/>
						<text class="scene-name">{{ scene.name }}</text>
						<text class="scene-desc">{{ scene.description }}</text>
						<view v-if="currentScene === scene.id" class="scene-active-indicator">
							<cl-icon name="cl-icon-check" size="12" color="#fff" />
						</view>
					</view>
				</view>

				<!-- 自定义场景 -->
				<view v-if="customScenes.length > 0" class="custom-scenes">
					<view class="custom-scene-title">自定义场景</view>
					<view class="scene-list">
						<view
							v-for="scene in customScenes"
							:key="scene.id"
							class="custom-scene-item"
							:class="{ active: currentScene === scene.id }"
							@tap="selectScene(scene)"
						>
							<view
								class="scene-preview"
								:style="{ backgroundColor: scene.color || '#f0f0f0' }"
							></view>
							<view class="scene-info">
								<text class="scene-name">{{ scene.name }}</text>
								<text class="scene-params"
									>亮度{{ scene.brightness }}% · 色温{{ scene.colorTemp }}K</text
								>
							</view>
							<cl-icon
								name="cl-icon-more"
								size="16"
								color="#999"
								@tap.stop="editCustomScene(scene)"
							/>
						</view>
					</view>
				</view>
			</view>

			<!-- 高级控制 -->
			<view class="advanced-control">
				<view class="section-title">
					<cl-icon name="cl-icon-tool" size="18" color="#1890ff" />
					<text>高级控制</text>
				</view>

				<!-- 渐变控制 -->
				<view class="control-group">
					<view class="control-label">
						<cl-icon name="cl-icon-trending-up" size="16" color="#666" />
						<text>渐变效果</text>
					</view>
					<view class="gradient-controls">
						<cl-button
							size="small"
							:type="gradientMode === 'smooth' ? 'primary' : 'default'"
							@tap="setGradientMode('smooth')"
							>平滑</cl-button
						>
						<cl-button
							size="small"
							:type="gradientMode === 'breath' ? 'primary' : 'default'"
							@tap="setGradientMode('breath')"
							>呼吸</cl-button
						>
						<cl-button
							size="small"
							:type="gradientMode === 'flash' ? 'primary' : 'default'"
							@tap="setGradientMode('flash')"
							>闪烁</cl-button
						>
						<cl-button
							size="small"
							:type="gradientMode === 'off' ? 'primary' : 'default'"
							@tap="setGradientMode('off')"
							>关闭</cl-button
						>
					</view>
				</view>

				<!-- 速度控制 -->
				<view v-if="gradientMode !== 'off'" class="control-group">
					<view class="control-label">
						<cl-icon name="cl-icon-zap" size="16" color="#666" />
						<text>变化速度</text>
						<text class="control-value">{{ gradientSpeed }}%</text>
					</view>
					<cl-slider
						v-model="gradientSpeed"
						min="10"
						max="100"
						@change="handleGradientSpeedChange"
					/>
				</view>

				<!-- 自动模式 -->
				<view class="control-group">
					<view class="control-label">
						<cl-icon name="cl-icon-cpu" size="16" color="#666" />
						<text>智能模式</text>
					</view>
					<view class="auto-modes">
						<cl-button
							size="small"
							:type="autoMode === 'time' ? 'primary' : 'default'"
							@tap="setAutoMode('time')"
							>时间自适应</cl-button
						>
						<cl-button
							size="small"
							:type="autoMode === 'light' ? 'primary' : 'default'"
							@tap="setAutoMode('light')"
							>光感自适应</cl-button
						>
						<cl-button
							size="small"
							:type="autoMode === 'off' ? 'primary' : 'default'"
							@tap="setAutoMode('off')"
							>关闭</cl-button
						>
					</view>
				</view>
			</view>

			<!-- 定时控制 -->
			<view class="timer-control">
				<view class="section-title">
					<cl-icon name="cl-icon-clock" size="18" color="#1890ff" />
					<text>定时控制</text>
					<cl-button size="small" type="text" @tap="openTimerManager">
						<cl-icon name="cl-icon-plus" size="14" />
						添加
					</cl-button>
				</view>

				<view v-if="timerList.length === 0" class="empty-timer">
					<cl-icon name="cl-icon-clock" size="32" color="#ccc" />
					<text class="empty-text">暂无定时任务</text>
					<cl-button size="small" type="primary" plain @tap="openTimerSetting()"
						>创建定时</cl-button
					>
				</view>

				<view v-else class="timer-list">
					<view v-for="timer in timerList" :key="timer.id" class="timer-item">
						<view class="timer-info">
							<view class="timer-main">
								<text class="timer-time">{{ timer.time }}</text>
								<text class="timer-action">{{
									timer.action === "on" ? "开启" : "关闭"
								}}</text>
							</view>
							<view class="timer-detail">
								<text class="timer-repeat">{{ getRepeatText(timer.repeat) }}</text>
								<text v-if="timer.scene" class="timer-scene"
									>场景: {{ timer.scene }}</text
								>
							</view>
						</view>
						<view class="timer-controls">
							<cl-switch v-model="timer.enabled" @change="toggleTimer(timer)" />
							<cl-icon
								name="cl-icon-edit"
								size="16"
								color="#666"
								@tap="editTimer(timer)"
							/>
							<cl-icon
								name="cl-icon-delete"
								size="16"
								color="#ff4d4f"
								@tap="deleteTimer(timer)"
							/>
						</view>
					</view>
				</view>
			</view>

			<!-- 批量控制 -->
			<view class="batch-control">
				<view class="section-title">
					<cl-icon name="cl-icon-layers" size="18" color="#1890ff" />
					<text>批量控制</text>
					<view class="device-selection">
						<text class="selection-text">已选{{ selectedDeviceIds.length }}台设备</text>
						<cl-button size="mini" type="text" @tap="selectAllDevices">全选</cl-button>
						<cl-button size="mini" type="text" @tap="clearSelection">清空</cl-button>
					</view>
				</view>

				<!-- 设备选择列表 -->
				<view class="device-selection-list">
					<view
						v-for="device in deviceList"
						:key="device.id"
						class="device-select-item"
						@tap="toggleDeviceSelection(device.id)"
					>
						<view class="device-select-info">
							<cl-icon
								:name="device.isOn ? 'cl-icon-bulb' : 'cl-icon-bulb-off'"
								size="16"
								:color="device.isOn ? '#1890ff' : '#ccc'"
							/>
							<text class="device-name">{{ device.name }}</text>
							<text class="device-status">{{
								device.status === "online" ? "在线" : "离线"
							}}</text>
						</view>
						<cl-checkbox :checked="selectedDeviceIds.includes(device.id)" />
					</view>
				</view>

				<view class="batch-actions">
					<cl-button
						class="batch-btn"
						type="primary"
						:disabled="selectedDeviceIds.length === 0"
						@tap="batchControl('on')"
						>批量开启</cl-button
					>
					<cl-button
						class="batch-btn"
						type="default"
						:disabled="selectedDeviceIds.length === 0"
						@tap="batchControl('off')"
						>批量关闭</cl-button
					>
				</view>

				<view class="batch-scenes">
					<view class="batch-scene-title">批量应用场景</view>
					<view class="scene-buttons">
						<cl-button
							v-for="scene in quickScenes"
							:key="scene.id"
							class="scene-btn"
							size="small"
							type="default"
							:disabled="selectedDeviceIds.length === 0"
							@tap="batchScene(scene)"
						>
							<cl-icon :name="scene.icon" size="14" />
							{{ scene.name }}
						</cl-button>
					</view>
				</view>
			</view>
		</view>

		<!-- 时间选择弹窗 -->
		<cl-popup v-model="showTimePicker" direction="bottom">
			<view class="time-picker-popup">
				<view class="popup-header">
					<text class="popup-title"
						>设置{{ currentTimerType === "on" ? "开启" : "关闭" }}时间</text
					>
					<cl-button text @click="showTimePicker = false">取消</cl-button>
				</view>

				<picker-view v-model="pickerValue" class="time-picker" @change="handlePickerChange">
					<picker-view-column>
						<view v-for="hour in hours" :key="hour" class="picker-item">
							{{ hour.toString().padStart(2, "0") }}
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="minute in minutes" :key="minute" class="picker-item">
							{{ minute.toString().padStart(2, "0") }}
						</view>
					</picker-view-column>
				</picker-view>

				<view class="popup-footer">
					<cl-button type="primary" @click="confirmTime">确定</cl-button>
				</view>
			</view>
		</cl-popup>
	</cl-page>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import { router } from "@/cool";
import { energyDeviceStore } from "@/stores/energy-device";
import LightingPanel from "@/components/business/lighting-panel.uvue";

// 响应式数据
const selectedDeviceId = ref<number | null>(null);
const currentScene = ref<string>("");
const showTimePicker = ref(false);
const currentTimerType = ref<'on' | 'off'>('on');
const pickerValue = ref([0, 0]);
const selectedDeviceIds = ref<number[]>([]);

// 高级控制
const gradientMode = ref<string>('off');
const gradientSpeed = ref<number>(50);
const autoMode = ref<string>('off');

// 定时设置
const timerSettings = reactive({
	onEnabled: false,
	onTime: "",
	offEnabled: false,
	offTime: ""
});

// 定时任务列表
const timerList = ref<any[]>([]);

// 自定义场景
const customScenes = ref<any[]>([]);

// 弹窗状态
const showSceneManager = ref<boolean>(false);
const showTimerManager = ref<boolean>(false);
const showGlobalSettings = ref<boolean>(false);

// 设备列表
const deviceList = computed(() => {
	return energyDeviceStore.devices.filter(device => device.status === 1);
});

// 当前选中设备
const currentDevice = computed(() => {
	return deviceList.value.find(device => device.id === selectedDeviceId.value);
});

// 场景列表
const sceneList = ref([
	{
		id: "work",
		name: "工作模式",
		description: "明亮白光，提高专注度",
		icon: "cl-icon-briefcase",
		brightness: 90,
		colorTemp: 5000
	},
	{
		id: "relax",
		name: "休闲模式",
		description: "温暖黄光，营造舒适氛围",
		icon: "cl-icon-coffee",
		brightness: 60,
		colorTemp: 3000
	},
	{
		id: "sleep",
		name: "睡眠模式",
		description: "柔和暖光，助于入眠",
		icon: "cl-icon-moon",
		brightness: 20,
		colorTemp: 2700
	},
	{
		id: "reading",
		name: "阅读模式",
		description: "护眼光线，减少疲劳",
		icon: "cl-icon-book",
		brightness: 80,
		colorTemp: 4000
	},
	{
		id: "party",
		name: "聚会模式",
		description: "彩色灯光，活跃气氛",
		icon: "cl-icon-gift",
		brightness: 100,
		colorTemp: 6500
	},
	{
		id: "energy",
		name: "节能模式",
		description: "低功耗运行，节约用电",
		icon: "cl-icon-leaf",
		brightness: 40,
		colorTemp: 4000
	}
]);

// 快捷场景
const quickScenes = computed(() => {
	return sceneList.value.slice(0, 4);
});

// 在线设备列表
const onlineDeviceList = computed(() => {
	return deviceList.value.filter(device => device.status === 'online');
});

// 时间选择器数据
const hours = Array.from({ length: 24 }, (_, i) => i);
const minutes = Array.from({ length: 60 }, (_, i) => i);

// 设备切换
const handleDeviceChange = (deviceId: number) => {
	selectedDeviceId.value = deviceId;
	loadDeviceSettings();
};

// 加载状态
const isLoading = ref<boolean>(false);
const operationLoading = ref<boolean>(false);

// 控制事件处理
const handleBrightnessChange = async (brightness: number) => {
	if (!currentDevice.value) {
		uni.showToast({
			title: "请先选择设备",
			icon: "none"
		});
		return;
	}

	if (currentDevice.value.status !== 'online') {
		uni.showToast({
			title: "设备离线，无法控制",
			icon: "none"
		});
		return;
	}

	operationLoading.value = true;
	try {
		await energyDeviceStore.controlDevice(currentDevice.value.id, "brightness", brightness);
		uni.showToast({
			title: `亮度已调节至${brightness}%`,
			icon: "success",
			duration: 1500
		});
		// 添加震动反馈
		uni.vibrateShort();
	} catch (error: any) {
		uni.showToast({
			title: error.message || "亮度调节失败，请重试",
			icon: "none",
			duration: 2000
		});
	} finally {
		operationLoading.value = false;
	}
};

const handleColorTempChange = async (colorTemp: number) => {
	if (!currentDevice.value) {
		uni.showToast({
			title: "请先选择设备",
			icon: "none"
		});
		return;
	}

	if (currentDevice.value.status !== 'online') {
		uni.showToast({
			title: "设备离线，无法控制",
			icon: "none"
		});
		return;
	}

	operationLoading.value = true;
	try {
		await energyDeviceStore.controlDevice(currentDevice.value.id, "colorTemp", colorTemp);
		const tempDesc = colorTemp < 3000 ? '暖光' : colorTemp > 5000 ? '冷光' : '中性光';
		uni.showToast({
			title: `色温已调节至${colorTemp}K(${tempDesc})`,
			icon: "success",
			duration: 1500
		});
		// 添加震动反馈
		uni.vibrateShort();
	} catch (error: any) {
		uni.showToast({
			title: error.message || "色温调节失败，请重试",
			icon: "none",
			duration: 2000
		});
	} finally {
		operationLoading.value = false;
	}
};

const handleSceneChange = async (scene: string) => {
	if (!currentDevice.value) return;

	try {
		await energyDeviceStore.controlDevice(currentDevice.value.id, "scene", scene);
		currentScene.value = scene;
		uni.showToast({
			title: "场景切换成功",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "场景切换失败",
			icon: "none"
		});
	}
};

const handleColorChange = async (color: string) => {
	if (!currentDevice.value) return;

	try {
		await energyDeviceStore.controlDevice(currentDevice.value.id, "color", color);
		uni.showToast({
			title: "颜色设置成功",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "颜色设置失败",
			icon: "none"
		});
	}
};

// 场景选择
const selectScene = async (scene: any) => {
	if (!currentDevice.value) {
		uni.showToast({
			title: "请先选择设备",
			icon: "none"
		});
		return;
	}

	if (currentDevice.value.status !== 'online') {
		uni.showToast({
			title: "设备离线，无法切换场景",
			icon: "none"
		});
		return;
	}

	// 显示加载提示
	uni.showLoading({
		title: `正在切换至${scene.name}...`,
		mask: true
	});

	try {
		// 应用场景设置
		await energyDeviceStore.controlDevice(currentDevice.value.id, "brightness", scene.brightness);
		await energyDeviceStore.controlDevice(currentDevice.value.id, "colorTemp", scene.colorTemp);

		currentScene.value = scene.id;
		uni.hideLoading();
		uni.showToast({
			title: `已切换至${scene.name}`,
			icon: "success",
			duration: 1500
		});
		// 添加震动反馈
		uni.vibrateShort();
	} catch (error: any) {
		uni.hideLoading();
		uni.showToast({
			title: error.message || "场景切换失败，请重试",
			icon: "none",
			duration: 2000
		});
	}
};

// 定时控制
const handleTimerChange = (type: 'on' | 'off') => {
	if (type === 'on' && timerSettings.onEnabled && !timerSettings.onTime) {
		openTimerSetting('on');
	} else if (type === 'off' && timerSettings.offEnabled && !timerSettings.offTime) {
		openTimerSetting('off');
	}
};

const openTimerSetting = (type: 'on' | 'off' = 'on') => {
	currentTimerType.value = type;
	showTimePicker.value = true;
};

const handlePickerChange = (e: any) => {
	pickerValue.value = e.detail.value;
};

const confirmTime = () => {
	const hour = hours[pickerValue.value[0]];
	const minute = minutes[pickerValue.value[1]];
	const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

	if (currentTimerType.value === 'on') {
		timerSettings.onTime = timeString;
		timerSettings.onEnabled = true;
	} else {
		timerSettings.offTime = timeString;
		timerSettings.offEnabled = true;
	}

	showTimePicker.value = false;
	const actionText = currentTimerType.value === 'on' ? '开启' : '关闭';
	uni.showToast({
		title: `定时${actionText}已设置为${timeString}`,
		icon: "success",
		duration: 2000
	});
	// 添加震动反馈
	uni.vibrateShort();
};

// 高级控制方法
const setGradientMode = async (mode: string) => {
	if (!currentDevice.value) {
		uni.showToast({
			title: "请先选择设备",
			icon: "none"
		});
		return;
	}

	if (currentDevice.value.status !== 'online') {
		uni.showToast({
			title: "设备离线，无法设置渐变模式",
			icon: "none"
		});
		return;
	}

	operationLoading.value = true;
	try {
		gradientMode.value = mode;
		await energyDeviceStore.controlDevice(currentDevice.value.id, "gradientMode", mode);
		const modeText = {
			'smooth': '平滑渐变',
			'breath': '呼吸效果',
			'flash': '闪烁效果',
			'off': '关闭'
		}[mode] || mode;
		uni.showToast({
			title: `渐变模式已设置为${modeText}`,
			icon: "success",
			duration: 1500
		});
		// 添加震动反馈
		uni.vibrateShort();
	} catch (error: any) {
		uni.showToast({
			title: error.message || "设置失败，请重试",
			icon: "none",
			duration: 2000
		});
	} finally {
		operationLoading.value = false;
	}
};

const handleGradientSpeedChange = async (speed: number) => {
	if (!currentDevice.value) return;

	try {
		await energyDeviceStore.controlDevice(currentDevice.value.id, "gradientSpeed", speed);
	} catch (error: any) {
		console.error('设置渐变速度失败:', error);
	}
};

const setAutoMode = async (mode: string) => {
	if (!currentDevice.value) {
		uni.showToast({
			title: "请先选择设备",
			icon: "none"
		});
		return;
	}

	if (currentDevice.value.status !== 'online') {
		uni.showToast({
			title: "设备离线，无法设置智能模式",
			icon: "none"
		});
		return;
	}

	operationLoading.value = true;
	try {
		autoMode.value = mode;
		await energyDeviceStore.controlDevice(currentDevice.value.id, "autoMode", mode);
		const modeText = {
			'time': '时间自适应',
			'light': '光感自适应',
			'off': '关闭'
		}[mode] || mode;
		uni.showToast({
			title: `智能模式已设置为${modeText}`,
			icon: "success",
			duration: 1500
		});
		// 添加震动反馈
		uni.vibrateShort();
	} catch (error: any) {
		uni.showToast({
			title: error.message || "设置失败，请重试",
			icon: "none",
			duration: 2000
		});
	} finally {
		operationLoading.value = false;
	}
};

// 场景管理
const openSceneManager = () => {
	uni.showToast({
		title: "场景管理功能开发中",
		icon: "none",
		duration: 2000
	});
	// showSceneManager.value = true;
};

const editCustomScene = (scene: any) => {
	// 编辑自定义场景
	uni.showToast({
		title: "编辑场景功能开发中",
		icon: "none"
	});
};

// 定时管理
const openTimerManager = () => {
	showTimerManager.value = true;
};

const getRepeatText = (repeat: string) => {
	const repeatMap: Record<string, string> = {
		'once': '仅一次',
		'daily': '每天',
		'weekly': '每周',
		'weekdays': '工作日',
		'weekends': '周末'
	};
	return repeatMap[repeat] || '自定义';
};

const toggleTimer = async (timer: any) => {
	try {
		// 这里调用API切换定时器状态
		uni.showToast({
			title: `定时任务已${timer.enabled ? '启用' : '禁用'}`,
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
	}
};

const editTimer = (timer: any) => {
	// 编辑定时器
	currentTimerType.value = timer.action;
	showTimePicker.value = true;
};

const deleteTimer = (timer: any) => {
	uni.showModal({
		title: '确认删除',
		content: '确定要删除这个定时任务吗？',
		success: (res) => {
			if (res.confirm) {
				const index = timerList.value.findIndex(t => t.id === timer.id);
				if (index > -1) {
					timerList.value.splice(index, 1);
					uni.showToast({
						title: "删除成功",
						icon: "success"
					});
				}
			}
		}
	});
};

// 设备选择
const toggleDeviceSelection = (deviceId: number) => {
	const index = selectedDeviceIds.value.indexOf(deviceId);
	if (index > -1) {
		selectedDeviceIds.value.splice(index, 1);
	} else {
		selectedDeviceIds.value.push(deviceId);
	}
};

const selectAllDevices = () => {
	selectedDeviceIds.value = onlineDeviceList.value.map(device => device.id);
};

const clearSelection = () => {
	selectedDeviceIds.value = [];
};

// 批量控制
const batchControl = async (action: 'on' | 'off') => {
	if (selectedDeviceIds.value.length === 0) {
		uni.showToast({
			title: "请先选择设备",
			icon: "none"
		});
		return;
	}

	// 检查选中设备的在线状态
	const onlineDevices = selectedDeviceIds.value.filter(id => {
		const device = deviceList.value.find(d => d.id === id);
		return device && device.status === 'online';
	});

	if (onlineDevices.length === 0) {
		uni.showToast({
			title: "所选设备均离线，无法控制",
			icon: "none"
		});
		return;
	}

	if (onlineDevices.length < selectedDeviceIds.value.length) {
		uni.showModal({
			title: "提示",
			content: `共选择${selectedDeviceIds.value.length}台设备，其中${onlineDevices.length}台在线，是否继续操作？`,
			success: async (res) => {
				if (res.confirm) {
					await performBatchControl(action, onlineDevices);
				}
			}
		});
	} else {
		await performBatchControl(action, onlineDevices);
	}
};

const performBatchControl = async (action: 'on' | 'off', deviceIds: number[]) => {
	uni.showLoading({
		title: `正在批量${action === 'on' ? '开启' : '关闭'}设备...`,
		mask: true
	});

	try {
		await energyDeviceStore.batchControl(deviceIds, action === 'on' ? 'toggle' : 'toggle');
		uni.hideLoading();
		uni.showToast({
			title: `批量${action === 'on' ? '开启' : '关闭'}成功(${deviceIds.length}台设备)`,
			icon: "success",
			duration: 2000
		});
		// 添加震动反馈
		uni.vibrateShort();
		// 清空选择
		selectedDeviceIds.value = [];
	} catch (error: any) {
		uni.hideLoading();
		uni.showToast({
			title: error.message || "批量操作失败，请重试",
			icon: "none",
			duration: 2000
		});
	}
};

const batchScene = async (scene: any) => {
	if (selectedDeviceIds.value.length === 0) {
		uni.showToast({
			title: "请先选择设备",
			icon: "none"
		});
		return;
	}

	// 检查选中设备的在线状态
	const onlineDevices = selectedDeviceIds.value.filter(id => {
		const device = deviceList.value.find(d => d.id === id);
		return device && device.status === 'online';
	});

	if (onlineDevices.length === 0) {
		uni.showToast({
			title: "所选设备均离线，无法切换场景",
			icon: "none"
		});
		return;
	}

	uni.showLoading({
		title: `正在批量应用${scene.name}...`,
		mask: true
	});

	try {
		await energyDeviceStore.batchControl(onlineDevices, "scene", scene.id);
		uni.hideLoading();
		uni.showToast({
			title: `批量应用${scene.name}成功(${onlineDevices.length}台设备)`,
			icon: "success",
			duration: 2000
		});
		// 添加震动反馈
		uni.vibrateShort();
		// 清空选择
		selectedDeviceIds.value = [];
		if (onlineDevices.length < selectedDeviceIds.value.length) {
			uni.showToast({
				title: `${selectedDeviceIds.value.length - onlineDevices.length}台离线设备未操作`,
				icon: "none",
				duration: 1500
			});
		}
	} catch (error: any) {
		uni.hideLoading();
		uni.showToast({
			title: error.message || "批量场景切换失败，请重试",
			icon: "none",
			duration: 2000
		});
	}
};

// 设备选择相关方法
const toggleDeviceSelection = (deviceId: number) => {
	const index = selectedDeviceIds.value.indexOf(deviceId);
	if (index > -1) {
		selectedDeviceIds.value.splice(index, 1);
	} else {
		selectedDeviceIds.value.push(deviceId);
	}
	// 添加轻微震动反馈
	uni.vibrateShort();
};

const selectAllDevices = () => {
	if (selectedDeviceIds.value.length === deviceList.value.length) {
		// 如果已全选，则取消全选
		selectedDeviceIds.value = [];
		uni.showToast({
			title: "已取消全选",
			icon: "none",
			duration: 1000
		});
	} else {
		// 全选所有设备
		selectedDeviceIds.value = deviceList.value.map(device => device.id);
		uni.showToast({
			title: `已选择${deviceList.value.length}台设备`,
			icon: "success",
			duration: 1000
		});
	}
	// 添加震动反馈
	uni.vibrateShort();
};

const clearSelection = () => {
	if (selectedDeviceIds.value.length === 0) {
		uni.showToast({
			title: "暂无选中设备",
			icon: "none",
			duration: 1000
		});
		return;
	}
	selectedDeviceIds.value = [];
	uni.showToast({
		title: "已清空选择",
		icon: "none",
		duration: 1000
	});
};

// 全局设置
const openGlobalSettings = () => {
	uni.showToast({
		title: "全局设置功能开发中",
		icon: "none",
		duration: 2000
	});
	// showGlobalSettings.value = true;
};

// 定时管理相关方法
const openTimerManager = () => {
	uni.showToast({
		title: "定时管理功能开发中",
		icon: "none",
		duration: 2000
	});
};

const toggleTimer = (timer: any) => {
	uni.showToast({
		title: `定时任务已${timer.enabled ? '启用' : '禁用'}`,
		icon: "success",
		duration: 1500
	});
	// 添加震动反馈
	uni.vibrateShort();
};

const editTimer = (timer: any) => {
	uni.showToast({
		title: "编辑定时功能开发中",
		icon: "none",
		duration: 2000
	});
};

const deleteTimer = (timer: any) => {
	uni.showModal({
		title: "确认删除",
		content: `确定要删除定时任务"${timer.time} ${timer.action === 'on' ? '开启' : '关闭'}"吗？`,
		success: (res) => {
			if (res.confirm) {
				const index = timerList.value.findIndex(t => t.id === timer.id);
				if (index > -1) {
					timerList.value.splice(index, 1);
					uni.showToast({
						title: "定时任务已删除",
						icon: "success",
						duration: 1500
					});
				}
			}
		}
	});
};

const getRepeatText = (repeat: string) => {
	const repeatMap: Record<string, string> = {
		'once': '仅一次',
		'daily': '每天',
		'weekdays': '工作日',
		'weekends': '周末',
		'custom': '自定义'
	};
	return repeatMap[repeat] || repeat;
};

const editCustomScene = (scene: any) => {
	uni.showToast({
		title: "编辑自定义场景功能开发中",
		icon: "none",
		duration: 2000
	});
};

// 加载设备设置
const loadDeviceSettings = async () => {
	if (currentDevice.value) {
		isLoading.value = true;
		try {
			// 加载当前设备的定时设置等
			// 这里可以调用API获取设备的定时设置
			await new Promise(resolve => setTimeout(resolve, 500)); // 模拟加载
		} catch (error) {
			console.error('加载设备设置失败:', error);
		} finally {
			isLoading.value = false;
		}
	}
};

onMounted(async () => {
	isLoading.value = true;
	try {
		// 加载设备列表
		await energyDeviceStore.getDeviceList();

		// 如果有路由参数指定设备，则选中该设备
		const { deviceId } = router.query;
		if (deviceId) {
			selectedDeviceId.value = parseInt(deviceId as string);
			await loadDeviceSettings();
		} else if (deviceList.value.length > 0) {
			// 默认选中第一个设备
			selectedDeviceId.value = deviceList.value[0].id;
			await loadDeviceSettings();
		}

		if (deviceList.value.length === 0) {
			uni.showToast({
				title: "暂无可控制的设备",
				icon: "none",
				duration: 2000
			});
		}
	} catch (error) {
		console.error('初始化失败:', error);
		uni.showToast({
			title: "加载设备列表失败",
			icon: "none",
			duration: 2000
		});
	} finally {
		isLoading.value = false;
	}
});}],"thought":"为批量控制和其他功能添加更完善的用户反馈和交互提示。"}}}
</script>

<style scoped>
.lighting-control {
	padding: 20rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.device-selector {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.device-count {
	font-size: 12px;
	color: #999;
	margin-left: auto;
}

.device-option {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10rpx 0;
}

.device-info {
	flex: 1;
}

.device-name {
	font-size: 14px;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.device-location {
	font-size: 12px;
	color: #666;
	display: block;
}

.device-status {
	display: flex;
	align-items: center;
	gap: 5rpx;
	font-size: 12px;
}

.device-status.online {
	color: #52c41a;
}

.device-status.offline {
	color: #ff4d4f;
}

.current-device-status {
	margin-top: 20rpx;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15rpx;
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.status-label {
	font-size: 12px;
	color: #666;
}

.status-value {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	display: flex;
	align-items: center;
	gap: 5rpx;
}

.status-value.online {
	color: #52c41a;
}

.status-value.offline {
	color: #ff4d4f;
}

.main-control {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.scene-modes {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.scene-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.scene-item {
	padding: 30rpx 20rpx;
	text-align: center;
	background-color: #f9f9f9;
	border-radius: 12rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	position: relative;
}

.scene-item.active {
	background-color: #e6f7ff;
	border-color: #1890ff;
}

.scene-active-indicator {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	width: 24rpx;
	height: 24rpx;
	background-color: #1890ff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.custom-scenes {
	margin-top: 30rpx;
}

.custom-scene-title {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	margin-bottom: 15rpx;
}

.scene-list {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.custom-scene-item {
	display: flex;
	align-items: center;
	padding: 15rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.custom-scene-item.active {
	background-color: #e6f7ff;
	border-color: #1890ff;
}

.scene-preview {
	width: 40rpx;
	height: 40rpx;
	border-radius: 6rpx;
	margin-right: 15rpx;
}

.scene-info {
	flex: 1;
}

.scene-params {
	font-size: 12px;
	color: #666;
	display: block;
	margin-top: 5rpx;
}

.scene-name {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	display: block;
	margin: 10rpx 0 5rpx;
}

.scene-desc {
	font-size: 12px;
	color: #666;
	display: block;
}

.advanced-control {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.control-group {
	margin-bottom: 25rpx;
}

.control-group:last-child {
	margin-bottom: 0;
}

.control-label {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 15rpx;
	font-size: 14px;
	color: #333;
}

.control-value {
	margin-left: auto;
	font-size: 12px;
	color: #1890ff;
	font-weight: 500;
}

.gradient-controls,
.auto-modes {
	display: flex;
	gap: 10rpx;
	flex-wrap: wrap;
}

.timer-control {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.empty-timer {
	text-align: center;
	padding: 40rpx 20rpx;
}

.empty-text {
	font-size: 14px;
	color: #999;
	display: block;
	margin: 15rpx 0 20rpx;
}

.timer-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.timer-settings {
	margin-bottom: 20rpx;
}

.timer-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1px solid #f0f0f0;
}

.timer-item:last-child {
	border-bottom: none;
}

.timer-info {
	flex: 1;
}

.timer-label {
	font-size: 14px;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.timer-time {
	font-size: 12px;
	color: #666;
	display: block;
}

.timer-btn {
	width: 100%;
}

.timer-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background-color: #f9f9f9;
	border-radius: 8rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.timer-item.active {
	background-color: #e6f7ff;
	border-color: #1890ff;
}

.timer-info {
	flex: 1;
}

.timer-time {
	font-size: 16px;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 5rpx;
}

.timer-repeat {
	font-size: 12px;
	color: #666;
	display: block;
}

.timer-actions {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.batch-control {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.device-selection {
	margin-bottom: 20rpx;
}

.selection-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.selection-title {
	font-size: 14px;
	color: #333;
	font-weight: 500;
}

.selection-actions {
	display: flex;
	gap: 15rpx;
}

.selection-action {
	font-size: 12px;
	color: #1890ff;
	padding: 5rpx 10rpx;
	border-radius: 4rpx;
	background-color: #f0f8ff;
	border: 1rpx solid #1890ff;
}

.device-list {
	max-height: 300rpx;
	overflow-y: auto;
	border: 1rpx solid #e8e8e8;
	border-radius: 8rpx;
	padding: 10rpx;
}

.device-item {
	display: flex;
	align-items: center;
	padding: 10rpx;
	border-radius: 6rpx;
	transition: all 0.3s ease;
}

.device-item:hover {
	background-color: #f9f9f9;
}

.device-checkbox {
	margin-right: 15rpx;
}

.batch-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 30rpx;
}

.batch-actions .cl-button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

/* 弹窗样式 */
.popup-content {
	padding: 30rpx;
	max-height: 80vh;
	overflow-y: auto;
}

.popup-title {
	font-size: 16px;
	color: #333;
	font-weight: 500;
	margin-bottom: 20rpx;
	text-align: center;
}

.popup-form {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.form-item {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.form-label {
	font-size: 14px;
	color: #333;
	font-weight: 500;
}

.form-input {
	padding: 15rpx;
	border: 1rpx solid #e8e8e8;
	border-radius: 8rpx;
	font-size: 14px;
}

.form-input:focus {
	border-color: #1890ff;
	outline: none;
}

.popup-actions {
	display: flex;
	gap: 15rpx;
	margin-top: 30rpx;
	justify-content: center;
}

.popup-actions .cl-button {
	flex: 1;
	max-width: 120rpx;
}

.batch-btn {
	flex: 1;
}

.batch-scenes {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.scene-btn {
	flex: 1;
	min-width: 120rpx;
}

.time-picker-popup {
	background-color: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 30rpx;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.popup-title {
	font-size: 18px;
	font-weight: 500;
	color: #333;
}

.time-picker {
	height: 400rpx;
	margin-bottom: 30rpx;
}

.picker-item {
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	font-size: 16px;
}

.popup-footer {
	text-align: center;
}
</style>
