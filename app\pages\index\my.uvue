<template>
	<cl-page>
		<cl-topbar
			fixed
			:height="100"
			:show-back="false"
			safe-area-top
			background-color="transparent"
		>
			<view class="flex flex-row items-center w-full flex-1 px-3">
				<view class="top-icon dark:!bg-surface-700" @tap="toSet">
					<cl-icon name="settings-line"></cl-icon>
				</view>

				<view class="top-icon dark:!bg-surface-700" @tap="toTest">
					<cl-icon name="notification-4-line"></cl-icon>
				</view>
			</view>
		</cl-topbar>

		<view class="p-3">
			<view class="flex flex-col justify-center items-center pt-6 pb-3">
				<view class="relative overflow-visible" @tap="toEdit">
					<cl-avatar
						:src="user.info.avatarUrl"
						:size="150"
						:pt="{ className: '!rounded-3xl', icon: { size: 60 } }"
					>
					</cl-avatar>

					<view
						class="flex flex-col justify-center items-center absolute bottom-0 right-[-6rpx] bg-black rounded-full p-1"
						v-if="!user.isNull()"
					>
						<cl-icon name="edit-line" color="white" :size="24"></cl-icon>
					</view>
				</view>

				<view class="flex-1 flex flex-col justify-center items-center w-full" @tap="toEdit">
					<cl-text :pt="{ className: '!text-xl mt-5 mb-1 font-bold' }">{{
						user.info.nickName ?? t("未登录")
					}}</cl-text>
					<cl-text color="info" v-if="!user.isNull()">{{ user.info.phone }}</cl-text>
				</view>
			</view>

			<cl-row
				:pt="{
					className: 'pt-3 pb-6'
				}"
			>
				<cl-col :span="6">
					<view class="flex flex-col items-center justify-center">
						<cl-rolling-number
							:pt="{ className: '!text-xl' }"
							:value="171"
						></cl-rolling-number>
						<cl-text :pt="{ className: 'mt-1 !text-xs' }" color="info">{{
							t("总点击")
						}}</cl-text>
					</view>
				</cl-col>

				<cl-col :span="6">
					<view class="flex flex-col items-center justify-center">
						<cl-rolling-number
							:pt="{ className: '!text-xl' }"
							:value="24"
						></cl-rolling-number>
						<cl-text :pt="{ className: 'mt-1 !text-xs' }" color="info">{{
							t("赞")
						}}</cl-text>
					</view>
				</cl-col>

				<cl-col :span="6">
					<view class="flex flex-col items-center justify-center">
						<cl-rolling-number
							:pt="{ className: '!text-xl' }"
							:value="89"
						></cl-rolling-number>
						<cl-text :pt="{ className: 'mt-1 !text-xs' }" color="info">{{
							t("收藏")
						}}</cl-text>
					</view>
				</cl-col>

				<cl-col :span="6">
					<view class="flex flex-col items-center justify-center">
						<cl-rolling-number
							:pt="{ className: '!text-xl' }"
							:value="653"
						></cl-rolling-number>
						<cl-text :pt="{ className: 'mt-1 !text-xs' }" color="info">{{
							t("粉丝")
						}}</cl-text>
					</view>
				</cl-col>
			</cl-row>

			<cl-row :gutter="20" :pt="{ className: 'mb-3' }">
				<cl-col :span="12">
					<view class="bg-white dark:!bg-surface-800 p-4 rounded-2xl flex flex-row">
						<view class="flex flex-col mr-auto">
							<cl-text
								ellipsis
								:pt="{
									className: '!w-[180rpx]'
								}"
								>{{ t("接单模式") }}</cl-text
							>
							<cl-text :pt="{ className: '!text-xs mt-1' }" color="info">{{
								t("已关闭")
							}}</cl-text>
						</view>

						<cl-switch></cl-switch>
					</view>
				</cl-col>

				<cl-col :span="12">
					<view class="bg-white dark:!bg-surface-800 p-4 rounded-2xl flex flex-row">
						<view class="flex flex-col mr-auto">
							<cl-text
								ellipsis
								:pt="{
									className: '!w-[180rpx]'
								}"
								>{{ t("消息通知") }}</cl-text
							>
							<cl-text :pt="{ className: '!text-xs mt-1' }" color="info">{{
								t("已关闭")
							}}</cl-text>
						</view>

						<cl-switch></cl-switch>
					</view>
				</cl-col>
			</cl-row>

			<view class="bg-white dark:!bg-surface-800 py-5 rounded-2xl mb-3 h-[160rpx]">
				<cl-row :pt="{ className: 'overflow-visible' }">
					<cl-col :span="6">
						<view class="flex flex-col justify-center items-center px-2">
							<cl-icon name="money-cny-circle-line" :size="46"></cl-icon>
							<cl-text
								:pt="{ className: '!text-xs mt-2 text-center' }"
								color="info"
								>{{ t("待支付") }}</cl-text
							>
						</view>
					</cl-col>

					<cl-col :span="6">
						<view class="flex flex-col justify-center items-center px-2">
							<cl-icon name="box-1-line" :size="46"></cl-icon>
							<cl-text
								:pt="{ className: '!text-xs mt-2 text-center' }"
								color="info"
								>{{ t("未发货") }}</cl-text
							>
						</view>
					</cl-col>

					<cl-col :span="6">
						<view
							class="flex flex-col justify-center items-center relative overflow-visible px-2"
						>
							<cl-icon name="flight-takeoff-line" :size="46"></cl-icon>
							<cl-text
								:pt="{ className: '!text-xs mt-2 text-center' }"
								color="info"
								>{{ t("已发货") }}</cl-text
							>

							<cl-badge
								type="primary"
								:value="3"
								position
								:pt="{ className: '!right-6' }"
							></cl-badge>
						</view>
					</cl-col>

					<cl-col :span="6">
						<view class="flex flex-col justify-center items-center px-2">
							<cl-icon name="exchange-cny-line" :size="46"></cl-icon>
							<cl-text
								:pt="{ className: '!text-xs mt-2 text-center' }"
								color="info"
								>{{ t("售后 / 退款") }}</cl-text
							>
						</view>
					</cl-col>
				</cl-row>
			</view>

			<cl-list :pt="{ className: 'mb-3' }">
				<cl-list-item
					:label="t('我的钱包')"
					icon="wallet-line"
					arrow
					hoverable
					@tap="toTest"
				>
				</cl-list-item>
				<cl-list-item
					:label="t('数据看板')"
					icon="pie-chart-line"
					arrow
					hoverable
					@tap="toTest"
				>
				</cl-list-item>
				<cl-list-item
					:label="t('历史记录')"
					icon="history-line"
					arrow
					hoverable
					@tap="toTest"
				>
				</cl-list-item>
				<cl-list-item
					:label="t('邀请好友')"
					icon="share-line"
					arrow
					hoverable
					@tap="toTest"
				>
				</cl-list-item>
			</cl-list>

			<cl-list>
				<cl-list-item :label="t('设置')" icon="settings-line" arrow hoverable @tap="toSet">
				</cl-list-item>
			</cl-list>
		</view>

		<!-- 自定义底部导航栏 -->
		<tabbar></tabbar>
	</cl-page>
</template>

<script setup lang="ts">
import { router, useStore } from "@/cool";
import { t } from "@/locale";
import { useUi } from "@/uni_modules/cool-ui";
import Tabbar from "@/components/tabbar.uvue";

const { user } = useStore();
const ui = useUi();

function toTest() {
	ui.showToast({
		message: t("开发中，敬请期待")
	});
}

function toSet() {
	router.to("/pages/set/index");
}

function toEdit() {
	router.to("/pages/user/edit");
}

onReady(() => {
	user.get();
});
</script>

<style lang="scss" scoped>
.top-icon {
	@apply flex items-center justify-center rounded-lg bg-white mr-3 p-2;
}
</style>
