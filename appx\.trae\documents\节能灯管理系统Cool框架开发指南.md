# 节能灯管理系统Cool框架开发指南

## 📋 目录

1. [框架架构概述](#1-框架架构概述)
2. [Cool框架集成](#2-cool框架集成)
3. [业务模块开发](#3-业务模块开发)
4. [开发流程规范](#4-开发流程规范)
5. [性能优化指南](#5-性能优化指南)
6. [部署与运维](#6-部署与运维)

---

## 1. 框架架构概述

### 1.1 技术栈

**核心框架：**
- **Uni-App X** - 跨平台移动应用开发框架
- **Vue 3** - 前端框架，支持组合式API
- **TypeScript** - 类型安全的JavaScript超集
- **Cool Framework** - 企业级开发脚手架
- **Cool-UI** - 内置UI组件库
- **Tailwind CSS** - 原子化CSS框架
- **Vite** - 现代化构建工具

**支持平台：**
- Android App
- iOS App
- H5网页
- 微信小程序
- 鸿蒙应用

### 1.2 Cool框架核心架构

```mermaid
graph TD
    A[App.uvue 应用入口] --> B[Cool Framework 核心层]
    B --> C[Service 服务层]
    B --> D[Store 状态管理]
    B --> E[Router 路由系统]
    B --> F[Utils 工具库]
    
    C --> G[HTTP请求封装]
    C --> H[Token管理]
    C --> I[错误处理]
    
    D --> J[用户状态]
    D --> K[字典数据]
    D --> L[业务状态]
    
    E --> M[页面跳转]
    E --> N[路由守卫]
    E --> O[参数传递]
    
    F --> P[本地存储]
    F --> Q[设备信息]
    F --> R[通用工具]
```

### 1.3 项目目录结构

```
app/
├── cool/                    # Cool框架核心
│   ├── service/            # API服务层
│   │   └── index.ts       # 统一请求封装
│   ├── store/             # 状态管理
│   │   ├── user.ts        # 用户状态
│   │   ├── dict.ts        # 字典状态
│   │   └── index.ts       # 状态导出
│   ├── router/            # 路由管理
│   │   └── index.ts       # 路由封装
│   ├── utils/             # 工具函数
│   │   ├── storage.ts     # 存储工具
│   │   ├── device.ts      # 设备工具
│   │   └── comm.ts        # 通用工具
│   ├── hooks/             # 组合式API
│   │   ├── cache.ts       # 缓存钩子
│   │   ├── pager.ts       # 分页钩子
│   │   └── refs.ts        # 引用钩子
│   └── types/             # 类型定义
│       └── index.ts       # 通用类型
├── pages/                  # 页面文件
│   ├── index/             # 主页面
│   │   ├── home.uvue      # 智能仪表板
│   │   └── my.uvue        # 个人中心
│   ├── energy/            # 节能灯业务页面
│   │   ├── device-list.uvue    # 设备列表
│   │   ├── device-detail.uvue  # 设备详情
│   │   ├── lighting-control.uvue # 照明控制
│   │   ├── energy-monitor.uvue   # 能耗监控
│   │   └── fault-manage.uvue     # 故障管理
│   └── user/              # 用户模块
│       ├── login.uvue     # 登录页面
│       └── edit.uvue      # 用户编辑
├── components/            # 组件库
│   ├── common/           # 通用组件
│   │   ├── device-card.uvue     # 设备卡片
│   │   ├── energy-chart.uvue    # 能耗图表
│   │   └── status-indicator.uvue # 状态指示器
│   └── business/         # 业务组件
│       ├── lighting-panel.uvue  # 照明控制面板
│       └── fault-handler.uvue   # 故障处理器
├── config/               # 配置文件
│   ├── index.ts         # 主配置
│   ├── dev.ts           # 开发环境
│   └── prod.ts          # 生产环境
└── uni_modules/         # uni-app模块
    ├── cool-ui/         # UI组件库
    ├── cool-canvas/     # 画布组件
    └── cool-vibrate/    # 震动组件
```

---

## 2. Cool框架集成

### 2.1 框架初始化

**main.ts 应用入口：**
```typescript
import { createSSRApp } from "vue";
import { cool } from "./cool";
import App from "./App.uvue";
import "./router";

export function createApp() {
	const app = createSSRApp(App);

	// 初始化Cool框架
	cool(app);

	return {
		app
	};
}
```

**Cool框架核心配置：**
```typescript
// cool/index.ts
import { scroller } from "./scroller";
import { initTheme, setH5 } from "./theme";
import { initLocale } from "@/locale";

export function cool(app: VueApp) {
	// 页面滚动监听
	app.mixin({
		onPageScroll(e) {
			scroller.emit(e.scrollTop);
		},
		onShow() {
			// H5适配
			// #ifdef H5
			setTimeout(() => {
				setH5();
			}, 0);
			// #endif
		}
	});

	// 初始化主题
	initTheme();
	// 初始化国际化
	initLocale();
}

// 导出所有模块
export * from "./ctx";
export * from "./hooks";
export * from "./router";
export * from "./service";
export * from "./store";
export * from "./utils";
```

### 2.2 服务层集成

**统一请求封装：**
```typescript
// cool/service/index.ts
import { isDev, config } from "@/config";
import { useStore } from "../store";

export function request<T = any>(options: RequestOptions): Promise<T> {
	let { url, method = "GET", data = {}, header = {}, timeout = 60000 } = options;

	const { user } = useStore();

	// 拼接基础URL
	if (!url.startsWith("http")) {
		url = config.baseUrl + url;
	}

	// 获取token
	let Authorization: string | null = user.token;

	return new Promise((resolve, reject) => {
		uni.request({
			url,
			method,
			data,
			header: {
				Authorization,
				...(header as UTSJSONObject)
			},
			timeout,
			success(res) {
				if (res.statusCode == 200) {
					const { code, message, data } = res.data as any;
					switch (code) {
						case 1000:
							resolve(data as T);
							break;
						default:
							reject({ message, code });
							break;
					}
				} else if (res.statusCode == 401) {
					// 未授权处理
					user.logout();
					reject({ message: "无权限" });
				}
			},
			fail(err) {
				reject({ message: err.errMsg });
			}
		});
	});
}
```

### 2.3 状态管理集成

**用户状态管理：**
```typescript
// cool/store/user.ts
import { reactive } from "vue";
import { storage } from "../utils";
import { service } from "../service";
import { router } from "../router";

export class User {
	info = reactive<UserInfoEntity>({});
	token: string | null = null;

	constructor() {
		// 初始化用户信息
		const userInfo = storage.get("userInfo");
		const token = storage.get("token") as string | null;
		
		this.token = token == "" ? null : token;
		this.set(userInfo!);
	}

	// 获取用户信息
	async get() {
		if (this.token != null) {
			await service.user.info.person({})
				.then((res) => {
					this.set(res);
				})
				.catch(() => {
					// this.logout();
				});
		}
	}

	// 设置用户信息
	set(data: any) {
		if (!data) return;
		
		// 清空原有信息
		this.remove();
		
		// 设置新信息
		Object.keys(data).forEach(key => {
			this.info[key] = data[key];
		});
		
		// 持久化存储
		storage.set("userInfo", data, 0);
	}

	// 退出登录
	logout() {
		this.clear();
		router.login();
	}

	// 清除信息
	clear() {
		storage.remove("userInfo");
		storage.remove("token");
		this.token = null;
		this.remove();
	}
}

export const user = new User();
```

---

## 3. 业务模块开发

### 3.1 节能灯设备管理

**设备状态管理：**
```typescript
// stores/energy-device.ts
import { reactive, ref } from "vue";
import { request } from "@/cool";

// 设备信息类型
interface EnergyDevice {
	id: number;
	deviceName: string;
	deviceCode: string;
	deviceType: string;
	location: string;
	status: number; // 1:在线 2:故障 3:离线 4:维护
	isOn: boolean;
	brightness: number;
	colorTemp: number;
	powerConsumption: number;
	createTime: string;
}

class EnergyDeviceStore {
	// 设备列表
	devices = reactive<EnergyDevice[]>([]);
	// 当前选中设备
	currentDevice = ref<EnergyDevice | null>(null);
	// 加载状态
	loading = ref(false);

	// 获取设备列表
	async getDeviceList(params?: {
		page?: number;
		pageSize?: number;
		keyword?: string;
		status?: number;
	}) {
		try {
			this.loading.value = true;
			const res = await request<{
				list: EnergyDevice[];
				total: number;
			}>({
				url: "/api/energy/device/page",
				method: "GET",
				data: params
			});
			
			if (params?.page === 1) {
				this.devices.splice(0, this.devices.length, ...res.list);
			} else {
				this.devices.push(...res.list);
			}
			
			return res;
		} finally {
			this.loading.value = false;
		}
	}

	// 控制设备
	async controlDevice(deviceId: number, action: string, value?: any) {
		const res = await request({
			url: "/api/energy/device/control",
			method: "POST",
			data: {
				deviceId,
				action,
				value
			}
		});
		
		// 更新本地状态
		const device = this.devices.find(d => d.id === deviceId);
		if (device) {
			switch (action) {
				case "toggle":
					device.isOn = !device.isOn;
					break;
				case "brightness":
					device.brightness = value;
					break;
				case "colorTemp":
					device.colorTemp = value;
					break;
			}
		}
		
		return res;
	}

	// 批量控制
	async batchControl(deviceIds: number[], action: string, value?: any) {
		return await request({
			url: "/api/energy/device/batch-control",
			method: "POST",
			data: {
				deviceIds,
				action,
				value
			}
		});
	}

	// 获取设备详情
	async getDeviceDetail(deviceId: number) {
		const res = await request<EnergyDevice>({
			url: `/api/energy/device/detail/${deviceId}`,
			method: "GET"
		});
		
		this.currentDevice.value = res;
		return res;
	}
}

export const energyDeviceStore = new EnergyDeviceStore();
```

**设备列表页面：**
```vue
<!-- pages/energy/device-list.uvue -->
<template>
	<view class="device-list-page">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<input 
				v-model="searchKeyword" 
				placeholder="搜索设备名称或编码"
				class="search-input"
				@input="handleSearch"
			/>
		</view>
		
		<!-- 设备统计 -->
		<view class="device-stats">
			<view class="stat-item">
				<text class="stat-number">{{ onlineCount }}</text>
				<text class="stat-label">在线设备</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ totalPower }}</text>
				<text class="stat-label">总功率(W)</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ faultCount }}</text>
				<text class="stat-label">故障设备</text>
			</view>
		</view>
		
		<!-- 设备列表 -->
		<list-view class="device-list" @scrolltolower="loadMore">
			<list-item 
				v-for="device in deviceList" 
				:key="device.id"
				class="device-item"
				@click="goToDetail(device)"
			>
				<energy-device-card 
					:device="device" 
					@control="handleControl"
					@settings="openSettings"
				/>
			</list-item>
		</list-view>
		
		<!-- 加载更多 -->
		<view v-if="loading" class="loading">
			<text>加载中...</text>
		</view>
		
		<!-- 批量控制面板 -->
		<view v-if="selectedDevices.length > 0" class="batch-control">
			<view class="batch-info">
				<text>已选择 {{ selectedDevices.length }} 个设备</text>
			</view>
			<view class="batch-actions">
				<button @click="batchToggle" class="batch-btn">批量开关</button>
				<button @click="batchBrightness" class="batch-btn">调节亮度</button>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { energyDeviceStore } from "@/stores/energy-device";
import { debounce } from "@/cool";
import EnergyDeviceCard from "@/components/business/energy-device-card.uvue";

const searchKeyword = ref("");
const loading = ref(false);
const page = ref(1);
const pageSize = 20;
const selectedDevices = ref<number[]>([]);

// 设备列表
const deviceList = computed(() => {
	return energyDeviceStore.devices.filter(device => {
		if (!searchKeyword.value) return true;
		return device.deviceName.includes(searchKeyword.value) ||
			   device.deviceCode.includes(searchKeyword.value);
	});
});

// 统计数据
const onlineCount = computed(() => {
	return deviceList.value.filter(d => d.status === 1).length;
});

const totalPower = computed(() => {
	return deviceList.value
		.filter(d => d.isOn)
		.reduce((sum, d) => sum + d.powerConsumption, 0)
		.toFixed(1);
});

const faultCount = computed(() => {
	return deviceList.value.filter(d => d.status === 2).length;
});

// 搜索处理
const handleSearch = debounce(() => {
	page.value = 1;
	loadDevices();
}, 300);

// 设备控制
const handleControl = async (device: any, action: string, value?: any) => {
	try {
		await energyDeviceStore.controlDevice(device.id, action, value);
		uni.showToast({
			title: "操作成功",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
	}
};

// 跳转详情
const goToDetail = (device: any) => {
	uni.navigateTo({
		url: `/pages/energy/device-detail?id=${device.id}`
	});
};

// 打开设置
const openSettings = (device: any) => {
	uni.navigateTo({
		url: `/pages/energy/device-settings?id=${device.id}`
	});
};

// 加载设备列表
const loadDevices = async () => {
	try {
		loading.value = true;
		await energyDeviceStore.getDeviceList({
			page: page.value,
			pageSize,
			keyword: searchKeyword.value
		});
	} finally {
		loading.value = false;
	}
};

// 加载更多
const loadMore = async () => {
	if (loading.value) return;
	
	page.value++;
	await loadDevices();
};

// 批量操作
const batchToggle = async () => {
	try {
		await energyDeviceStore.batchControl(selectedDevices.value, "toggle");
		selectedDevices.value = [];
		uni.showToast({ title: "批量操作成功", icon: "success" });
	} catch (error: any) {
		uni.showToast({ title: error.message, icon: "none" });
	}
};

const batchBrightness = () => {
	// 打开亮度调节弹窗
	uni.showModal({
		title: "调节亮度",
		content: "请输入亮度值(0-100)",
		editable: true,
		placeholderText: "50",
		success: async (res) => {
			if (res.confirm && res.content) {
				const brightness = parseInt(res.content);
				if (brightness >= 0 && brightness <= 100) {
					await energyDeviceStore.batchControl(
						selectedDevices.value, 
						"brightness", 
						brightness
					);
					selectedDevices.value = [];
				}
			}
		}
	});
};

onMounted(() => {
	loadDevices();
});
</script>

<style scoped>
.device-list-page {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.search-bar {
	padding: 20rpx;
	background-color: white;
	margin-bottom: 20rpx;
}

.search-input {
	width: 100%;
	height: 80rpx;
	padding: 0 20rpx;
	border: 1px solid #e8e8e8;
	border-radius: 8rpx;
	background-color: #f9f9f9;
}

.device-stats {
	display: flex;
	justify-content: space-around;
	padding: 30rpx 20rpx;
	background-color: white;
	margin-bottom: 20rpx;
}

.stat-item {
	text-align: center;
}

.stat-number {
	font-size: 24px;
	font-weight: bold;
	color: #1890ff;
	margin-bottom: 8rpx;
}

.stat-label {
	font-size: 12px;
	color: #666;
}

.device-list {
	flex: 1;
	padding: 0 20rpx;
}

.device-item {
	margin-bottom: 20rpx;
}

.loading {
	padding: 40rpx;
	text-align: center;
	color: #666;
}

.batch-control {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: white;
	padding: 20rpx;
	border-top: 1px solid #e8e8e8;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.batch-info {
	font-size: 14px;
	color: #333;
}

.batch-actions {
	display: flex;
	gap: 20rpx;
}

.batch-btn {
	padding: 12rpx 24rpx;
	font-size: 14px;
	border-radius: 6rpx;
	background-color: #1890ff;
	color: white;
	border: none;
}
</style>
```

### 3.2 能耗监控模块

**能耗数据管理：**
```typescript
// stores/energy-monitor.ts
import { reactive, ref } from "vue";
import { request } from "@/cool";

// 能耗数据类型
interface EnergyData {
	time: string;
	power: number;
	consumption: number;
	voltage: number;
	current: number;
	deviceId?: number;
}

interface EnergyStats {
	totalConsumption: number;
	averagePower: number;
	peakPower: number;
	savingRate: number;
	cost: number;
}

class EnergyMonitorStore {
	// 实时数据
	realtimeData = reactive<EnergyData[]>([]);
	// 历史数据
	historyData = reactive<EnergyData[]>([]);
	// 统计数据
	stats = reactive<EnergyStats>({
		totalConsumption: 0,
		averagePower: 0,
		peakPower: 0,
		savingRate: 0,
		cost: 0
	});
	
	loading = ref(false);
	currentPeriod = ref("today");

	// 获取实时能耗数据
	async getRealtimeData(deviceId?: number) {
		const res = await request<EnergyData[]>({
			url: "/api/energy/realtime",
			method: "GET",
			data: { deviceId }
		});
		
		this.realtimeData.splice(0, this.realtimeData.length, ...res);
		return res;
	}

	// 获取历史能耗数据
	async getHistoryData(params: {
		deviceId?: number;
		startTime: string;
		endTime: string;
		period: "hour" | "day" | "month";
	}) {
		try {
			this.loading.value = true;
			const res = await request<EnergyData[]>({
				url: "/api/energy/history",
				method: "GET",
				data: params
			});
			
			this.historyData.splice(0, this.historyData.length, ...res);
			return res;
		} finally {
			this.loading.value = false;
		}
	}

	// 获取能耗统计
	async getEnergyStats(params: {
		deviceId?: number;
		period: string;
	}) {
		const res = await request<EnergyStats>({
			url: "/api/energy/stats",
			method: "GET",
			data: params
		});
		
		Object.assign(this.stats, res);
		return res;
	}

	// 获取节能分析
	async getSavingAnalysis(params: {
		deviceId?: number;
		period: string;
	}) {
		return await request({
			url: "/api/energy/analysis",
			method: "GET",
			data: params
		});
	}
}

export const energyMonitorStore = new EnergyMonitorStore();
```

---

## 4. 开发流程规范

### 4.1 开发环境配置

**1. 环境要求：**
- Node.js >= 16.0.0
- HBuilderX 最新版本
- 微信开发者工具（小程序开发）
- Android Studio（Android开发）
- Xcode（iOS开发）

**2. 项目启动：**
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev:h5

# 构建小程序
pnpm build:mp-weixin

# 构建App
pnpm build:app
```

**3. 开发配置：**
```typescript
// config/dev.ts
export const dev = () => ({
	host: "localhost:8080",
	baseUrl: "http://localhost:8080"
});

// config/prod.ts
export const prod = () => ({
	host: "api.energylight.com",
	baseUrl: "https://api.energylight.com"
});
```

### 4.2 代码规范

**1. 命名规范：**
- 页面文件：kebab-case（如：device-list.uvue）
- 组件文件：PascalCase（如：DeviceCard.uvue）
- 变量/函数：camelCase（如：deviceList）
- 常量：UPPER_SNAKE_CASE（如：API_BASE_URL）
- 类型：PascalCase（如：DeviceInfo）

**2. 文件结构规范：**
```vue
<template>
  <!-- 模板内容 -->
</template>

<script lang="ts" setup>
// 1. 导入依赖
import { ref, computed, onMounted } from "vue";
import { useStore } from "@/cool";

// 2. 类型定义
interface Props {
  // props类型
}

// 3. Props和Emits
const props = defineProps<Props>();
const emit = defineEmits<{
  // emit类型
}>();

// 4. 响应式数据
const loading = ref(false);
const data = ref([]);

// 5. 计算属性
const filteredData = computed(() => {
  // 计算逻辑
});

// 6. 方法定义
const handleClick = () => {
  // 方法实现
};

// 7. 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>

<style scoped>
/* 样式定义 */
</style>
```

**3. TypeScript规范：**
```typescript
// 类型定义文件 types/energy.ts
export interface DeviceInfo {
	id: number;
	deviceName: string;
	deviceCode: string;
	deviceType: "LED灯" | "节能灯" | "智能灯";
	location: string;
	status: 1 | 2 | 3 | 4; // 1:在线 2:故障 3:离线 4:维护
	isOn: boolean;
	brightness: number;
	colorTemp: number;
	powerConsumption: number;
	createTime: string;
}

export interface DeviceControlRequest {
	deviceId: number;
	action: "toggle" | "brightness" | "colorTemp" | "scene";
	value?: number | string;
}

export interface EnergyData {
	time: string;
	power: number;
	consumption: number;
	voltage: number;
	current: number;
	deviceId?: number;
}
```

### 4.3 组件开发规范

**1. 组件设计原则：**
- 单一职责：每个组件只负责一个功能
- 可复用性：组件应该可以在多个场景中使用
- 可配置性：通过props提供灵活的配置选项
- 可扩展性：预留扩展接口和插槽

**2. 组件模板：**
```vue
<!-- components/business/energy-device-card.uvue -->
<template>
	<view class="energy-device-card" :class="cardClass">
		<!-- 设备图标 -->
		<view class="device-icon">
			<image :src="deviceIcon" class="icon-image" />
			<view v-if="device.status === 2" class="fault-badge">
				<text class="fault-text">故障</text>
			</view>
		</view>
		
		<!-- 设备信息 -->
		<view class="device-info">
			<text class="device-name">{{ device.deviceName }}</text>
			<text class="device-location">{{ device.location }}</text>
			<view class="device-status">
				<view class="status-dot" :class="statusClass"></view>
				<text class="status-text">{{ statusText }}</text>
				<text class="power-text">{{ device.powerConsumption }}W</text>
			</view>
		</view>
		
		<!-- 控制区域 -->
		<view class="device-controls" v-if="showControls">
			<!-- 开关按钮 -->
			<button 
				class="control-btn toggle-btn"
				:class="{ active: device.isOn }"
				@click.stop="handleToggle"
			>
				{{ device.isOn ? '关' : '开' }}
			</button>
			
			<!-- 亮度控制 -->
			<view class="brightness-control" v-if="device.isOn">
				<slider 
					:value="device.brightness"
					:min="0"
					:max="100"
					class="brightness-slider"
					@change="handleBrightnessChange"
				/>
				<text class="brightness-text">{{ device.brightness }}%</text>
			</view>
			
			<!-- 更多设置 -->
			<button class="control-btn settings-btn" @click.stop="handleSettings">
				⚙️
			</button>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import type { DeviceInfo } from "@/types/energy";

// Props定义
interface Props {
	device: DeviceInfo;
	showControls?: boolean;
	selectable?: boolean;
	selected?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	showControls: true,
	selectable: false,
	selected: false
});

// Emits定义
interface Emits {
	control: [device: DeviceInfo, action: string, value?: any];
	settings: [device: DeviceInfo];
	select: [device: DeviceInfo, selected: boolean];
}

const emit = defineEmits<Emits>();

// 计算属性
const cardClass = computed(() => ({
	"device-card--online": props.device.status === 1,
	"device-card--offline": props.device.status === 3,
	"device-card--fault": props.device.status === 2,
	"device-card--maintenance": props.device.status === 4,
	"device-card--selected": props.selected
}));

const deviceIcon = computed(() => {
	const iconMap = {
		"LED灯": "/static/images/device/led.png",
		"节能灯": "/static/images/device/energy.png",
		"智能灯": "/static/images/device/smart.png"
	};
	return iconMap[props.device.deviceType] || "/static/images/device/default.png";
});

const statusClass = computed(() => ({
	"status-dot--online": props.device.status === 1,
	"status-dot--offline": props.device.status === 3,
	"status-dot--fault": props.device.status === 2,
	"status-dot--maintenance": props.device.status === 4
}));

const statusText = computed(() => {
	const statusMap = {
		1: "在线",
		2: "故障",
		3: "离线",
		4: "维护"
	};
	return statusMap[props.device.status] || "未知";
});

// 方法定义
const handleToggle = () => {
	if (props.device.status !== 1) {
		uni.showToast({
			title: "设备离线，无法操作",
			icon: "none"
		});
		return;
	}
	
	emit("control", props.device, "toggle");
};

const handleBrightnessChange = (e: any) => {
	const brightness = e.detail.value;
	emit("control", props.device, "brightness", brightness);
};

const handleSettings = () => {
	emit("settings", props.device);
};

const handleCardClick = () => {
	if (props.selectable) {
		emit("select", props.device, !props.selected);
	}
};
</script>

<style scoped>
.energy-device-card {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background-color: white;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
	position: relative;
}

.energy-device-card:active {
	transform: scale(0.98);
}

.device-card--online {
	border-left: 6rpx solid #52c41a;
}

.device-card--offline {
	border-left: 6rpx solid #d9d9d9;
	opacity: 0.7;
}

.device-card--fault {
	border-left: 6rpx solid #ff4d4f;
}

.device-card--maintenance {
	border-left: 6rpx solid #faad14;
}

.device-card--selected {
	background-color: #e6f7ff;
	border: 2rpx solid #1890ff;
}

.device-icon {
	width: 96rpx;
	height: 96rpx;
	margin-right: 24rpx;
	position: relative;
}

.icon-image {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}

.fault-badge {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	background-color: #ff4d4f;
	border-radius: 12rpx;
	padding: 4rpx 8rpx;
}

.fault-text {
	font-size: 10px;
	color: white;
}

.device-info {
	flex: 1;
	min-width: 0;
}

.device-name {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.device-location {
	font-size: 14px;
	color: #666;
	margin-bottom: 12rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.device-status {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
}

.status-dot--online {
	background-color: #52c41a;
}

.status-dot--offline {
	background-color: #d9d9d9;
}

.status-dot--fault {
	background-color: #ff4d4f;
}

.status-dot--maintenance {
	background-color: #faad14;
}

.status-text {
	font-size: 12px;
	color: #666;
}

.power-text {
	font-size: 12px;
	color: #1890ff;
	font-weight: bold;
}

.device-controls {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	min-width: 120rpx;
}

.control-btn {
	padding: 12rpx 20rpx;
	font-size: 14px;
	border-radius: 8rpx;
	border: 1px solid #d9d9d9;
	background-color: white;
	color: #666;
	transition: all 0.2s ease;
}

.toggle-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	font-weight: bold;
}

.toggle-btn.active {
	background-color: #52c41a;
	color: white;
	border-color: #52c41a;
}

.settings-btn {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	font-size: 16px;
}

.brightness-control {
	display: flex;
	align-items: center;
	gap: 8rpx;
	width: 100%;
}

.brightness-slider {
	flex: 1;
	height: 6rpx;
}

.brightness-text {
	font-size: 10px;
	color: #666;
	min-width: 40rpx;
	text-align: center;
}
</style>
```

---

## 5. 性能优化指南

### 5.1 页面性能优化

**1. 列表优化：**
```vue
<!-- 使用list-view替代scroll-view -->
<list-view class="device-list" @scrolltolower="loadMore">
	<list-item 
		v-for="device in deviceList" 
		:key="device.id"
		class="device-item"
	>
		<energy-device-card :device="device" />
	</list-item>
</list-view>
```

**2. 图片优化：**
```vue
<template>
	<!-- 使用懒加载 -->
	<image 
		:src="imageSrc" 
		class="device-image"
		mode="aspectFill"
		lazy-load
		@load="handleImageLoad"
		@error="handleImageError"
	/>
</template>

<script setup>
// 图片预加载
const preloadImages = (urls: string[]) => {
	urls.forEach(url => {
		uni.preloadPage({ url });
	});
};
</script>
```

**3. 数据缓存：**
```typescript
// utils/cache.ts
class CacheManager {
	private cache = new Map<string, { data: any; expire: number }>();

	set(key: string, data: any, ttl: number = 300000) { // 默认5分钟
		this.cache.set(key, {
			data,
			expire: Date.now() + ttl
		});
	}

	get(key: string) {
		const item = this.cache.get(key);
		if (!item) return null;
		
		if (Date.now() > item.expire) {
			this.cache.delete(key);
			return null;
		}
		
		return item.data;
	}

	clear() {
		this.cache.clear();
	}
}

export const cacheManager = new CacheManager();
```

### 5.2 网络请求优化

**1. 请求防抖：**
```typescript
// utils/debounce.ts
export function debounce<T extends (...args: any[]) => any>(
	func: T,
	wait: number
): (...args: Parameters<T>) => void {
	let timeout: number | null = null;
	
	return (...args: Parameters<T>) => {
		if (timeout !== null) {
			clearTimeout(timeout);
		}
		
		timeout = setTimeout(() => {
			func(...args);
		}, wait);
	};
}

// 使用示例
const debouncedSearch = debounce((keyword: string) => {
	searchDevices(keyword);
}, 300);
```

**2. 请求合并：**
```typescript
// utils/request-batch.ts
class RequestBatcher {
	private batches = new Map<string, {
		requests: Array<{ resolve: Function; reject: Function; params: any }>;
		timer: number;
	}>();

	batch<T>(key: string, params: any, batchFn: (params: any[]) => Promise<T[]>): Promise<T> {
		return new Promise((resolve, reject) => {
			if (!this.batches.has(key)) {
				this.batches.set(key, {
					requests: [],
					timer: 0
				});
			}
			
			const batch = this.batches.get(key)!;
			batch.requests.push({ resolve, reject, params });
			
			if (batch.timer) {
				clearTimeout(batch.timer);
			}
			
			batch.timer = setTimeout(async () => {
				const requests = batch.requests.splice(0);
				const allParams = requests.map(r => r.params);
				
				try {
					const results = await batchFn(allParams);
					requests.forEach((req, index) => {
						req.resolve(results[index]);
					});
				} catch (error) {
					requests.forEach(req => {
						req.reject(error);
					});
				}
				
				this.batches.delete(key);
			}, 50); // 50ms内的请求合并
		});
	}
}

export const requestBatcher = new RequestBatcher();
```

### 5.3 内存管理

**1. 组件销毁清理：**
```vue
<script setup>
import { onUnmounted, ref } from "vue";

const timer = ref<number | null>(null);
const subscription = ref<any>(null);

// 定时器清理
onUnmounted(() => {
	if (timer.value) {
		clearInterval(timer.value);
		timer.value = null;
	}
	
	// 取消订阅
	if (subscription.value) {
		subscription.value.unsubscribe();
		subscription.value = null;
	}
});
</script>
```

**2. 大数据处理：**
```typescript
// utils/virtual-list.ts
export class VirtualList {
	private itemHeight: number;
	private containerHeight: number;
	private scrollTop: number = 0;
	private data: any[] = [];

	constructor(itemHeight: number, containerHeight: number) {
		this.itemHeight = itemHeight;
		this.containerHeight = containerHeight;
	}

	setData(data: any[]) {
		this.data = data;
	}

	getVisibleItems() {
		const startIndex = Math.floor(this.scrollTop / this.itemHeight);
		const endIndex = Math.min(
			startIndex + Math.ceil(this.containerHeight / this.itemHeight) + 1,
			this.data.length
		);
		
		return {
			items: this.data.slice(startIndex, endIndex),
			startIndex,
			endIndex,
			offsetY: startIndex * this.itemHeight
		};
	}

	updateScrollTop(scrollTop: number) {
		this.scrollTop = scrollTop;
	}
}
```

---

## 6. 部署与运维

### 6.1 构建配置

**1. 环境配置：**
```typescript
// vite.config.ts
import { defineConfig } from "vite";
import { cool } from "@cool-vue/vite-plugin";
import { proxy } from "./config/proxy";
import uni from "@dcloudio/vite-plugin-uni";

export default defineConfig({
	plugins: [
		uni(),
		cool({
			proxy,
			tailwindcss: true,
			eps: {
				dir: "../backend/src",
				dist: "./cool/service",
				api: "/api"
			},
			clean: ["cool/service"]
		})
	],
	server: {
		port: 9000,
		proxy
	},
	css: {
		postcss: {
			plugins: [
				require("tailwindcss"),
				require("autoprefixer")
			]
		}
	},
	resolve: {
		alias: {
			"@": path.resolve(__dirname, "./")
		}
	}
});
```

**2. 多端构建脚本：**
```json
// package.json
{
	"scripts": {
		"dev:h5": "uni build --target h5 --mode development",
		"dev:mp-weixin": "uni build --target mp-weixin --mode development",
		"dev:app": "uni build --target app --mode development",
		"build:h5": "uni build --target h5 --mode production",
		"build:mp-weixin": "uni build --target mp-weixin --mode production",
		"build:app": "uni build --target app --mode production",
		"build:app-android": "uni build --target app-android --mode production",
		"build:app-ios": "uni build --target app-ios --mode production"
	}
}
```

### 6.2 发布流程

**1. H5发布：**
```bash
# 构建H5版本
pnpm build:h5

# 部署到服务器
scp -r dist/build/h5/* user@server:/var/www/energy-light/

# 配置Nginx
server {
    listen 80;
    server_name energy-light.com;
    root /var/www/energy-light;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

**2. 小程序发布：**
```bash
# 构建微信小程序
pnpm build:mp-weixin

# 使用微信开发者工具打开 dist/build/mp-weixin
# 上传代码到微信后台
# 提交审核
```

**3. App发布：**
```bash
# Android打包
pnpm build:app-android
# 使用Android Studio打开项目
# 生成签名APK

# iOS打包
pnpm build:app-ios
# 使用Xcode打开项目
# Archive并上传到App Store
```

### 6.3 监控与维护

**1. 错误监控：**
```typescript
// utils/error-monitor.ts
class ErrorMonitor {
	private errorQueue: any[] = [];
	private isReporting = false;

	init() {
		// 全局错误捕获
		uni.onError((error) => {
			this.reportError({
				type: "runtime",
				message: error.message,
				stack: error.stack,
				timestamp: Date.now()
			});
		});

		// Promise错误捕获
		uni.onUnhandledRejection((event) => {
			this.reportError({
				type: "promise",
				message: event.reason,
				timestamp: Date.now()
			});
		});
	}

	reportError(error: any) {
		this.errorQueue.push(error);
		
		if (!this.isReporting) {
			this.flushErrors();
		}
	}

	private async flushErrors() {
		if (this.errorQueue.length === 0) return;
		
		this.isReporting = true;
		
		try {
			await request({
				url: "/api/monitor/errors",
				method: "POST",
				data: {
					errors: this.errorQueue.splice(0)
				}
			});
		} catch (e) {
			// 上报失败，重新加入队列
			console.error("Error reporting failed:", e);
		} finally {
			this.isReporting = false;
		}
	}
}

export const errorMonitor = new ErrorMonitor();
```

**2. 性能监控：**
```typescript
// utils/performance-monitor.ts
class PerformanceMonitor {
	private metrics: any[] = [];

	// 页面加载时间
	trackPageLoad(pageName: string) {
		const startTime = Date.now();
		
		return () => {
			const loadTime = Date.now() - startTime;
			this.reportMetric({
				type: "page_load",
				page: pageName,
				duration: loadTime,
				timestamp: Date.now()
			});
		};
	}

	// API请求时间
	trackApiCall(url: string, method: string) {
		const startTime = Date.now();
		
		return (success: boolean) => {
			const duration = Date.now() - startTime;
			this.reportMetric({
				type: "api_call",
				url,
				method,
				duration,
				success,
				timestamp: Date.now()
			});
		};
	}

	private reportMetric(metric: any) {
		this.metrics.push(metric);
		
		// 批量上报
		if (this.metrics.length >= 10) {
			this.flushMetrics();
		}
	}

	private async flushMetrics() {
		if (this.metrics.length === 0) return;
		
		try {
			await request({
				url: "/api/monitor/metrics",
				method: "POST",
				data: {
					metrics: this.metrics.splice(0)
				}
			});
		} catch (e) {
			console.error("Metrics reporting failed:", e);
		}
	}
}

export const performanceMonitor = new PerformanceMonitor();
```

---

## 📚 总结

本开发指南整合了Cool框架的技术架构与节能灯管理系统的业务需求，提供了：

### ✅ 核心优势

1. **统一架构**：基于Cool框架的企业级架构，确保代码质量和可维护性
2. **类型安全**：全面的TypeScript支持，减少运行时错误
3. **组件复用**：丰富的业务组件库，提高开发效率
4. **多端兼容**：一套代码，多端运行，降低维护成本
5. **性能优化**：内置性能优化方案，确保用户体验

### 🚀 快速开始

1. 克隆项目并安装依赖
2. 配置开发环境和API地址
3. 基于现有组件快速开发业务页面
4. 遵循代码规范和最佳实践
5. 使用内置工具进行测试和部署

### 📖 相关文档

- [Cool框架官方文档](https://cool-js.com)
- [Uni-App X文档](https://uniapp.dcloud.net.cn/uni-app-x/)
- [Vue 3文档](https://vuejs.org)
- [TypeScript文档](https://www.typescriptlang.org)

通过本指南，开发团队可以在现有Cool框架基础上高效开发节能灯管理系统的各项功能，确保项目的技术先进性和业务完整性。