// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 场景分页查询输入参数
/// </summary>
public class EnergySceneInput : BasePageInput
{
    /// <summary>
    /// 场景名称
    /// </summary>
    public string? SceneName { get; set; }

    /// <summary>
    /// 场景类型
    /// </summary>
    public string? SceneType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 触发条件
    /// </summary>
    public string? TriggerCondition { get; set; }
}

/// <summary>
/// 场景ID查询输入参数
/// </summary>
public class QueryByIdEnergySceneInput : DeleteEnergySceneInput
{
}

/// <summary>
/// 添加场景输入参数
/// </summary>
public class AddEnergySceneInput
{
    /// <summary>
    /// 场景名称
    /// </summary>
    [Required(ErrorMessage = "场景名称不能为空")]
    public string SceneName { get; set; }

    /// <summary>
    /// 场景类型
    /// </summary>
    [Required(ErrorMessage = "场景类型不能为空")]
    public string SceneType { get; set; }

    /// <summary>
    /// 场景描述
    /// </summary>
    public string? SceneDescription { get; set; }

    /// <summary>
    /// 场景图标
    /// </summary>
    public string? SceneIcon { get; set; }

    /// <summary>
    /// 执行条件
    /// </summary>
    public string? ExecuteCondition { get; set; }

    /// <summary>
    /// 定时执行
    /// </summary>
    public bool IsScheduled { get; set; } = false;

    /// <summary>
    /// 定时表达式
    /// </summary>
    public string? ScheduleExpression { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; } = 100;

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; } = 1;

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// 更新场景输入参数
/// </summary>
public class UpdateEnergySceneInput : AddEnergySceneInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 删除场景输入参数
/// </summary>
public class DeleteEnergySceneInput : BaseIdInput
{
}

/// <summary>
/// 场景状态输入参数
/// </summary>
public class EnergySceneStatusInput : BaseIdInput
{
    /// <summary>
    /// 状态
    /// </summary>
    [Required(ErrorMessage = "状态不能为空")]
    public int Status { get; set; }
}

/// <summary>
/// 执行场景输入参数
/// </summary>
public class ExecuteSceneInput : BaseIdInput
{
    /// <summary>
    /// 执行来源
    /// </summary>
    public string ExecuteSource { get; set; } = "Manual";

    /// <summary>
    /// 执行参数
    /// </summary>
    public string? ExecuteParams { get; set; }
}