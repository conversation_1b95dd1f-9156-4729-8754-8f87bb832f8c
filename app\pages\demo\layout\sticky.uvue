<template>
	<cl-page back-top>
		<cl-sticky>
			<view class="bg-primary-500 p-3 h-[40px] flex flex-row items-center">
				<cl-text color="white">Header - 1</cl-text>
			</view>
		</cl-sticky>

		<view class="p-3">
			<cl-list>
				<cl-list-item :label="`${i}`" v-for="i in 50" :key="i"> </cl-list-item>
			</cl-list>
		</view>

		<cl-sticky :offset-top="40">
			<view class="bg-red-500 p-3 h-[40px] flex flex-row items-center">
				<cl-text color="white">Header - 2</cl-text>
			</view>
		</cl-sticky>

		<view class="p-3">
			<cl-list>
				<cl-list-item :label="`${i}`" v-for="i in 50" :key="i"> </cl-list-item>
			</cl-list>
		</view>

		<cl-sticky :offset-top="80">
			<view class="bg-purple-500 p-3 h-[40px] flex flex-row items-center">
				<cl-text color="white">Header - 3</cl-text>
			</view>
		</cl-sticky>

		<view class="p-3">
			<cl-list>
				<cl-list-item :label="`${i}`" v-for="i in 50" :key="i"> </cl-list-item>
			</cl-list>
		</view>
	</cl-page>
</template>

<script lang="ts" setup>
import { t } from "@/locale";
import DemoItem from "../components/item.uvue";
</script>
