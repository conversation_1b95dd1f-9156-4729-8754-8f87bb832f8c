// 统一导出所有 stores
import { useEnergyDeviceStore } from './energy-device';
import { useEnergyMonitorStore } from './energy-monitor';
import { useEnergySettingsStore } from './energy-settings';

// 导出 store 实例（用于兼容性）
const energyDeviceStore = useEnergyDeviceStore();
const energyMonitorStore = useEnergyMonitorStore();
const energySettingsStore = useEnergySettingsStore();

// 为了兼容页面中的调用方式，添加控制方法到 energyDeviceStore
Object.assign(energyDeviceStore, {
  // 直接控制方法
  toggle: energyDeviceStore.deviceControls.toggle,
  brightness: energyDeviceStore.deviceControls.brightness,
  colorTemp: energyDeviceStore.deviceControls.colorTemp,
  scene: energyDeviceStore.deviceControls.scene,
  color: energyDeviceStore.deviceControls.color,
  gradientMode: energyDeviceStore.deviceControls.gradientMode,
  gradientSpeed: energyDeviceStore.deviceControls.gradientSpeed,
  autoMode: energyDeviceStore.deviceControls.autoMode
});

export { energyDeviceStore, energyMonitorStore, energySettingsStore };

// 同时导出原始的 composable 函数
export {
  useEnergyDeviceStore,
  useEnergyMonitorStore,
  useEnergySettingsStore
};

// 导出类型定义
export type {
  EnergyDevice,
  DeviceControlParams,
  BatchControlParams,
  DeviceFilterParams,
  DeviceStats
} from './energy-device';

export type {
  EnergyData,
  EnergyStats,
  EnergyReport,
  EnergySuggestion,
  EnergyQueryParams,
  RealTimeData
} from './energy-monitor';

export type {
  UserSettings,
  NotificationSettings,
  DashboardSettings,
  ControlSettings,
  PrivacySettings,
  SettingsUpdateParams
} from './energy-settings';