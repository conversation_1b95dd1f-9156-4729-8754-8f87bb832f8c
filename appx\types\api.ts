// API相关类型定义

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求配置接口
export interface RequestOptions {
  url: string
  method?: HttpMethod
  data?: any
  params?: Record<string, any>
  headers?: Record<string, string>
  timeout?: number
  responseType?: 'json' | 'text' | 'arraybuffer'
}

// 响应接口
export interface Response<T = any> {
  statusCode: number
  data: ApiResponse<T>
  header: Record<string, string>
  cookies?: string[]
}

// API响应基础结构
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp?: string
  traceId?: string
}

// 错误响应接口
export interface ErrorResponse {
  success: false
  code: number
  message: string
  details?: any
  timestamp: string
  path?: string
}

// 分页请求参数
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

// 分页响应数据
export interface PaginationResult<T = any> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// 排序参数
export interface SortParams {
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

// 筛选参数
export interface FilterParams {
  [key: string]: any
}

// 搜索参数
export interface SearchParams extends PaginationParams, Partial<SortParams> {
  keyword?: string
  filters?: FilterParams
}

// API端点枚举
export enum ApiEndpoints {
  // 用户相关
  USER_LOGIN = '/api/user/login',
  USER_LOGOUT = '/api/user/logout',
  USER_INFO = '/api/user/info',
  USER_UPDATE = '/api/user/update',
  
  // 设备相关
  DEVICE_LIST = '/api/devices',
  DEVICE_DETAIL = '/api/devices/:id',
  DEVICE_STATS = '/api/devices/stats',
  DEVICE_CONTROL = '/api/devices/:id/control',
  DEVICE_STATUS = '/api/devices/:id/status',
  
  // 能耗相关
  ENERGY_TODAY = '/api/energy/today',
  ENERGY_OVERVIEW = '/api/energy/overview',
  ENERGY_TREND = '/api/energy/trend',
  ENERGY_DETAILS = '/api/energy/details',
  ENERGY_REPORT = '/api/energy/report',
  
  // 字典相关
  DICT_ALL = '/api/dict/all',
  DICT_BY_TYPE = '/api/dict/:type',
  
  // 通知相关
  NOTIFICATION_LIST = '/api/notifications',
  NOTIFICATION_READ = '/api/notifications/:id/read',
  NOTIFICATION_DELETE = '/api/notifications/:id'
}

// 请求拦截器类型
export type RequestInterceptor = (config: RequestOptions) => RequestOptions | Promise<RequestOptions>

// 响应拦截器类型
export type ResponseInterceptor<T = any> = (response: Response<T>) => Response<T> | Promise<Response<T>>

// 错误拦截器类型
export type ErrorInterceptor = (error: any) => any | Promise<any>

// 拦截器配置
export interface InterceptorConfig {
  request?: RequestInterceptor[]
  response?: ResponseInterceptor[]
  error?: ErrorInterceptor[]
}

// HTTP客户端配置
export interface HttpClientConfig {
  baseURL: string
  timeout: number
  headers: Record<string, string>
  interceptors?: InterceptorConfig
}

// 上传文件参数
export interface UploadFileParams {
  url: string
  filePath: string
  name: string
  formData?: Record<string, any>
  header?: Record<string, string>
}

// 下载文件参数
export interface DownloadFileParams {
  url: string
  header?: Record<string, string>
}

// WebSocket消息类型
export interface WebSocketMessage<T = any> {
  type: string
  data: T
  timestamp: number
  id?: string
}

// WebSocket配置
export interface WebSocketConfig {
  url: string
  protocols?: string[]
  timeout?: number
  heartbeat?: {
    interval: number
    message: string
  }
}

// 缓存配置
export interface CacheConfig {
  key: string
  ttl?: number // 过期时间（秒）
  storage?: 'memory' | 'localStorage' | 'sessionStorage'
}

// 重试配置
export interface RetryConfig {
  times: number
  delay: number
  condition?: (error: any) => boolean
}

// 请求队列项
export interface RequestQueueItem {
  id: string
  config: RequestOptions
  resolve: (value: any) => void
  reject: (reason: any) => void
  timestamp: number
  retryCount?: number
}

// 批量请求配置
export interface BatchRequestConfig {
  requests: RequestOptions[]
  concurrent?: number
  failFast?: boolean
}

// 批量请求结果
export interface BatchRequestResult<T = any> {
  success: Response<T>[]
  failed: { error: any; config: RequestOptions }[]
  total: number
  successCount: number
  failedCount: number
}

// Mock数据配置
export interface MockConfig {
  enabled: boolean
  delay?: number
  rules: MockRule[]
}

// Mock规则
export interface MockRule {
  url: string | RegExp
  method?: HttpMethod
  response: any | ((config: RequestOptions) => any)
  delay?: number
  status?: number
}

// 日志配置
export interface LogConfig {
  enabled: boolean
  level: 'debug' | 'info' | 'warn' | 'error'
  maxSize?: number
  format?: (log: LogEntry) => string
}

// 日志条目
export interface LogEntry {
  level: string
  message: string
  timestamp: number
  data?: any
  url?: string
  method?: string
  duration?: number
}
