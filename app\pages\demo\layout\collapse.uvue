<template>
	<cl-page>
		<view class="p-3">
			<demo-item :label="t('基础用法')">
				<cl-button @click="toggle">{{ visible ? t("点击收起") : t("点击展开") }}</cl-button>

				<cl-collapse v-model="visible">
					<cl-text
						>云想衣裳花想容，春风拂槛露华浓，若非群玉山头见，会向瑶台月下逢。</cl-text
					>
				</cl-collapse>
			</demo-item>

			<demo-item :label="t('ref 方式调用')">
				<cl-button @click="refToggle">{{ t("点击展开") }}</cl-button>

				<cl-collapse ref="collapseRef">
					<view class="bg-surface-100 p-3 rounded-xl dark:!bg-surface-700">
						<cl-text
							>云想衣裳花想容，春风拂槛露华浓，若非群玉山头见，会向瑶台月下逢。</cl-text
						>
					</view>
				</cl-collapse>
			</demo-item>
		</view>
	</cl-page>
</template>

<script lang="ts" setup>
import { t } from "@/locale";
import DemoItem from "../components/item.uvue";
import { ref } from "vue";

const visible = ref(false);

function toggle() {
	visible.value = !visible.value;
}

const collapseRef = ref<ClCollapseComponentPublicInstance | null>(null);

function refToggle() {
	collapseRef.value!.toggle();
}
</script>
