<template>
	<cl-page>
		<cl-topbar title="设备列表">
			<template #right>
				<cl-button size="small" @click="showFilter = !showFilter">
					<cl-icon name="cl-icon-filter" />
				</cl-button>
			</template>
		</cl-topbar>

		<!-- 搜索栏 -->
		<view class="search-bar">
			<cl-input
				v-model="searchKeyword"
				placeholder="搜索设备名称或编码"
				clearable
				@input="handleSearch"
			>
				<template #prepend>
					<cl-icon name="cl-icon-search" />
				</template>
			</cl-input>
		</view>

		<!-- 筛选器 -->
		<view v-if="showFilter" class="filter-bar">
			<view class="filter-item">
				<text class="filter-label">状态:</text>
				<cl-select v-model="filterStatus" placeholder="全部状态">
					<cl-option label="全部" value="" />
					<cl-option label="在线" value="1" />
					<cl-option label="故障" value="2" />
					<cl-option label="离线" value="3" />
					<cl-option label="维护" value="4" />
				</cl-select>
			</view>
			<view class="filter-item">
				<text class="filter-label">类型:</text>
				<cl-select v-model="filterType" placeholder="全部类型">
					<cl-option label="全部" value="" />
					<cl-option label="LED灯" value="LED灯" />
					<cl-option label="节能灯" value="节能灯" />
					<cl-option label="智能灯" value="智能灯" />
				</cl-select>
			</view>
			<view class="filter-actions">
				<cl-button size="small" @click="resetFilter">重置</cl-button>
				<cl-button size="small" type="primary" @click="applyFilter">应用</cl-button>
			</view>
		</view>

		<!-- 设备统计 -->
		<view class="device-stats">
			<view class="stat-item">
				<text class="stat-number">{{ onlineCount }}</text>
				<text class="stat-label">在线设备</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ totalPower }}</text>
				<text class="stat-label">总功率(W)</text>
			</view>
			<view class="stat-item">
				<text class="stat-number">{{ faultCount }}</text>
				<text class="stat-label">故障设备</text>
			</view>
		</view>

		<!-- 操作栏 -->
		<view class="action-bar">
			<view class="action-left">
				<cl-button
					size="small"
					:type="selectMode ? 'primary' : 'default'"
					@click="toggleSelectMode"
				>
					{{ selectMode ? "取消选择" : "批量选择" }}
				</cl-button>
				<text v-if="selectMode" class="select-info">
					已选择 {{ selectedDevices.length }} 个设备
				</text>
			</view>
			<view class="action-right">
				<text class="device-count">共 {{ deviceList.length }} 个设备</text>
			</view>
		</view>

		<!-- 设备列表 -->
		<cl-list-view
			class="device-list"
			@scrolltolower="loadMore"
			:refresher-enabled="true"
			@refresherrefresh="onRefresh"
			:refresher-triggered="refreshing"
		>
			<cl-list-item
				v-for="device in deviceList"
				:key="device.id"
				class="device-item"
				:class="{ selected: selectedDevices.includes(device.id) }"
				@click="handleDeviceClick(device)"
			>
				<view v-if="selectMode" class="select-checkbox">
					<cl-checkbox
						:model-value="selectedDevices.includes(device.id)"
						@change="toggleDeviceSelect(device.id)"
					/>
				</view>
				<energy-device-card
					:device="device"
					:select-mode="selectMode"
					@control="handleControl"
					@settings="openSettings"
				/>
			</cl-list-item>

			<!-- 空状态 -->
			<view v-if="deviceList.length === 0 && !loading" class="empty-state">
				<cl-icon name="cl-icon-device" size="80" color="#ccc" />
				<text class="empty-text">暂无设备数据</text>
				<cl-button size="small" @click="loadDevices">重新加载</cl-button>
			</view>
		</cl-list-view>

		<!-- 加载更多 -->
		<view v-if="loading" class="loading">
			<cl-loading-mask :loading="loading" text="加载中..." />
		</view>

		<!-- 批量控制面板 -->
		<view v-if="selectMode && selectedDevices.length > 0" class="batch-control">
			<view class="batch-info">
				<text>已选择 {{ selectedDevices.length }} 个设备</text>
				<cl-button size="mini" @click="selectAll">全选</cl-button>
				<cl-button size="mini" @click="clearSelection">清空</cl-button>
			</view>
			<view class="batch-actions">
				<cl-button size="small" @click="batchToggle">批量开关</cl-button>
				<cl-button size="small" @click="batchBrightness">调节亮度</cl-button>
				<cl-button size="small" @click="batchScene">场景模式</cl-button>
			</view>
		</view>
	</cl-page>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { router, debounce } from "@/cool";
import { energyDeviceStore } from "@/stores/energy-device";
import EnergyDeviceCard from "@/components/business/energy-device-card.uvue";

const searchKeyword = ref("");
const loading = ref(false);
const refreshing = ref(false);
const page = ref(1);
const pageSize = 20;
const selectedDevices = ref<number[]>([]);
const showFilter = ref(false);
const filterStatus = ref("");
const filterType = ref("");
const selectMode = ref(false);

// 设备列表
const deviceList = computed(() => {
	return energyDeviceStore.devices.filter((device) => {
		// 搜索过滤
		if (searchKeyword.value) {
			const keyword = searchKeyword.value.toLowerCase();
			if (
				!device.deviceName.toLowerCase().includes(keyword) &&
				!device.deviceCode.toLowerCase().includes(keyword) &&
				!device.location.toLowerCase().includes(keyword)
			) {
				return false;
			}
		}

		// 状态过滤
		if (filterStatus.value && device.status.toString() !== filterStatus.value) {
			return false;
		}

		// 类型过滤
		if (filterType.value && device.deviceType !== filterType.value) {
			return false;
		}

		return true;
	});
});

// 统计数据
const onlineCount = computed(() => {
	return deviceList.value.filter((d) => d.status === 1).length;
});

const totalPower = computed(() => {
	return deviceList.value
		.filter((d) => d.isOn)
		.reduce((sum, d) => sum + d.powerConsumption, 0)
		.toFixed(1);
});

const faultCount = computed(() => {
	return deviceList.value.filter((d) => d.status === 2).length;
});

// 搜索处理
const handleSearch = debounce(() => {
	page.value = 1;
	loadDevices();
}, 300);

// 筛选处理
const applyFilter = () => {
	page.value = 1;
	loadDevices();
	showFilter.value = false;
};

const resetFilter = () => {
	filterStatus.value = "";
	filterType.value = "";
	applyFilter();
};

// 选择模式
const toggleSelectMode = () => {
	selectMode.value = !selectMode.value;
	if (!selectMode.value) {
		selectedDevices.value = [];
	}
};

// 设备点击处理
const handleDeviceClick = (device: any) => {
	if (selectMode.value) {
		toggleDeviceSelect(device.id);
	} else {
		goToDetail(device);
	}
};

// 切换设备选择
const toggleDeviceSelect = (deviceId: number) => {
	const index = selectedDevices.value.indexOf(deviceId);
	if (index > -1) {
		selectedDevices.value.splice(index, 1);
	} else {
		selectedDevices.value.push(deviceId);
	}
};

// 全选
const selectAll = () => {
	selectedDevices.value = deviceList.value.map((d) => d.id);
};

// 清空选择
const clearSelection = () => {
	selectedDevices.value = [];
};

// 设备控制
const handleControl = async (device: any, action: string, value?: any) => {
	try {
		await energyDeviceStore.controlDevice(device.id, action, value);
		uni.showToast({
			title: "操作成功",
			icon: "success"
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "操作失败",
			icon: "none"
		});
	}
};

// 跳转详情
const goToDetail = (device: any) => {
	router.push("/pages/energy/device-detail", {
		id: device.id
	});
};

// 打开设置
const openSettings = (device: any) => {
	// 显示设备设置弹窗
	uni.showActionSheet({
		itemList: ["重命名设备", "修改位置", "设置分组", "删除设备"],
		success: (res) => {
			switch (res.tapIndex) {
				case 0:
					// 重命名设备
					uni.showModal({
						title: "重命名设备",
						editable: true,
						placeholderText: device.deviceName,
						success: (modalRes) => {
							if (modalRes.confirm && modalRes.content) {
								// 更新设备名称
								device.deviceName = modalRes.content;
								uni.showToast({ title: "重命名成功", icon: "success" });
							}
						}
					});
					break;
				case 1:
					// 修改位置
					uni.showModal({
						title: "修改位置",
						editable: true,
						placeholderText: device.location,
						success: (modalRes) => {
							if (modalRes.confirm && modalRes.content) {
								// 更新设备位置
								device.location = modalRes.content;
								uni.showToast({ title: "位置更新成功", icon: "success" });
							}
						}
					});
					break;
				case 2:
					// 设置分组
					uni.showActionSheet({
						itemList: ["办公区域", "会议室", "走廊", "休息区"],
						success: (groupRes) => {
							const groups = ["办公区域", "会议室", "走廊", "休息区"];
							device.group = groups[groupRes.tapIndex];
							uni.showToast({ title: "分组设置成功", icon: "success" });
						}
					});
					break;
				case 3:
					// 删除设备
					uni.showModal({
						title: "确认删除",
						content: `确定要删除设备"${device.deviceName}"吗？此操作不可恢复。`,
						success: (confirmRes) => {
							if (confirmRes.confirm) {
								// 从store中删除设备
								energyDeviceStore.removeDevice(device.id);
								uni.showToast({ title: "设备已删除", icon: "success" });
							}
						}
					});
					break;
			}
		}
	});
};

// 加载设备列表
const loadDevices = async () => {
	try {
		loading.value = true;
		await energyDeviceStore.getDeviceList({
			page: page.value,
			pageSize,
			keyword: searchKeyword.value,
			status: filterStatus.value ? parseInt(filterStatus.value) : undefined,
			deviceType: filterType.value || undefined
		});
	} catch (error: any) {
		uni.showToast({
			title: error.message || "加载失败",
			icon: "none"
		});
	} finally {
		loading.value = false;
	}
};

// 下拉刷新
const onRefresh = async () => {
	refreshing.value = true;
	page.value = 1;
	try {
		await loadDevices();
	} finally {
		refreshing.value = false;
	}
};

// 加载更多
const loadMore = async () => {
	if (loading.value) return;

	page.value++;
	await loadDevices();
};

// 批量操作
const batchToggle = async () => {
	try {
		await energyDeviceStore.batchControl(selectedDevices.value, "toggle");
		selectedDevices.value = [];
		uni.showToast({ title: "批量操作成功", icon: "success" });
	} catch (error: any) {
		uni.showToast({ title: error.message, icon: "none" });
	}
};

const batchBrightness = () => {
	// 打开亮度调节弹窗
	uni.showModal({
		title: "调节亮度",
		content: "请输入亮度值(0-100)",
		editable: true,
		placeholderText: "50",
		success: async (res) => {
			if (res.confirm && res.content) {
				const brightness = parseInt(res.content);
				if (brightness >= 0 && brightness <= 100) {
					try {
						await energyDeviceStore.batchControl(
							selectedDevices.value,
							"brightness",
							brightness
						);
						uni.showToast({ title: "亮度调节成功", icon: "success" });
					} catch (error: any) {
						uni.showToast({ title: error.message, icon: "none" });
					}
				} else {
					uni.showToast({ title: "请输入有效的亮度值", icon: "none" });
				}
			}
		}
	});
};

// 批量场景模式
const batchScene = () => {
	uni.showActionSheet({
		itemList: ["温馨模式", "工作模式", "节能模式", "夜间模式"],
		success: async (res) => {
			const scenes = ["warm", "work", "energy_saving", "night"];
			const scene = scenes[res.tapIndex];
			try {
				await energyDeviceStore.batchControl(selectedDevices.value, "scene", scene);
				uni.showToast({ title: "场景设置成功", icon: "success" });
			} catch (error: any) {
				uni.showToast({ title: error.message, icon: "none" });
			}
		}
	});
};

onMounted(() => {
	loadDevices();
});
</script>

<style scoped>
.search-bar {
	padding: 20rpx;
	background: #fff;
	border-bottom: 1px solid #f0f0f0;
}

.filter-bar {
	padding: 20rpx;
	background: #f8f9fa;
	border-bottom: 1px solid #e9ecef;
	animation: slideDown 0.3s ease;
}

.filter-item {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.filter-item:last-child {
	margin-bottom: 0;
}

.filter-label {
	width: 100rpx;
	font-size: 28rpx;
	color: #333;
	margin-right: 20rpx;
}

.filter-actions {
	display: flex;
	justify-content: flex-end;
	gap: 20rpx;
	margin-top: 20rpx;
}

.device-stats {
	padding: 20rpx;
	background: #fff;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: space-around;
}

.stat-item {
	text-align: center;
}

.stat-number {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	display: block;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
}

.action-bar {
	padding: 20rpx;
	background: #fff;
	border-bottom: 1px solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.action-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.select-info {
	font-size: 24rpx;
	color: #666;
}

.device-count {
	font-size: 24rpx;
	color: #999;
}

.device-list {
	flex: 1;
	padding: 0 20rpx;
}

.device-item {
	margin-bottom: 20rpx;
	position: relative;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
}

.device-item.selected {
	background: #e3f2fd;
	border-radius: 12rpx;
}

.select-checkbox {
	margin-right: 20rpx;
}

.empty-state {
	padding: 100rpx 40rpx;
	text-align: center;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	margin: 30rpx 0;
	display: block;
}

.loading {
	padding: 40rpx;
	text-align: center;
}

.batch-control {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 20rpx;
	border-top: 1px solid #f0f0f0;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
	animation: slideUp 0.3s ease;
}

.batch-info {
	font-size: 28rpx;
	color: #333;
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.batch-actions {
	display: flex;
	gap: 20rpx;
	justify-content: center;
}

@keyframes slideDown {
	from {
		transform: translateY(-100%);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}
</style>
