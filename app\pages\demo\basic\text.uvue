<template>
	<cl-page>
		<view class="p-3">
			<demo-item :label="t('基础用法')">
				<cl-text>云想衣裳花想容，春风拂槛露华浓。</cl-text>
			</demo-item>

			<demo-item :label="t('自定义颜色')">
				<cl-text color="primary">云想衣裳花想容，春风拂槛露华浓。</cl-text>
			</demo-item>

			<demo-item :label="t('手机号脱敏')">
				<cl-text type="phone" mask value="13800138000"></cl-text>
			</demo-item>

			<demo-item :label="t('姓名脱敏')">
				<cl-text type="name" mask value="张三"></cl-text>
			</demo-item>

			<demo-item :label="t('邮箱脱敏')">
				<cl-text type="email" mask value="<EMAIL>"></cl-text>
			</demo-item>

			<demo-item :label="t('银行卡脱敏')">
				<cl-text type="card" mask value="1234 5678 9012 3456"></cl-text>
			</demo-item>

			<demo-item :label="t('金额')">
				<cl-text type="amount" :value="100.0"></cl-text>
			</demo-item>

			<demo-item :label="t('自定义脱敏字符')">
				<cl-text type="phone" value="13800138000" mask mask-char="~"></cl-text>
			</demo-item>

			<demo-item :label="t('省略号')">
				<cl-text ellipsis
					>云想衣裳花想容，春风拂槛露华浓。若非群玉山头见，会向瑶台月下逢。</cl-text
				>
			</demo-item>
		</view>
	</cl-page>
</template>

<script lang="ts" setup>
import { t } from "@/locale";
import DemoItem from "../components/item.uvue";
</script>
