---
description: Uni-App X API 使用规范
globs: *.uts,*.uvue
alwaysApply: false
---
# API 使用规范

## 基础 API 使用原则
- 可以使用 UTS 的 API，但注意版本和平台的兼容性
- 可以使用 uni-app x 的 API，但注意版本和平台的兼容性
- 可以使用 Vue3 的 API，但注意版本和平台的兼容性
- 可以使用操作系统的 API，但注意版本和平台的兼容性
- 尽量在 UTS 插件中调用系统原生 API，而不是在 uvue 页面中直接调用系统原生 API
- 特定平台或特定版本以上才能使用的代码，需使用条件编译包围这些代码，或者放置在平台专用的目录文件中

## 常用 API 分类

### 系统信息 API
- `uni.getSystemInfo()` - 获取系统信息
- `uni.getWindowInfo()` - 获取窗口信息
- `uni.getDeviceInfo()` - 获取设备信息
- `uni.getAppBaseInfo()` - 获取应用基础信息

### 网络请求 API
- `uni.request()` - 发起网络请求
- `uni.uploadFile()` - 上传文件
- `uni.downloadFile()` - 下载文件
- `uni.connectSocket()` - 创建 WebSocket 连接

### 数据存储 API
- `uni.setStorage()` / `uni.setStorageSync()` - 存储数据
- `uni.getStorage()` / `uni.getStorageSync()` - 获取数据
- `uni.removeStorage()` / `uni.removeStorageSync()` - 删除数据
- `uni.clearStorage()` / `uni.clearStorageSync()` - 清空数据

### 界面交互 API
- `uni.showToast()` - 显示消息提示框
- `uni.showModal()` - 显示模态弹窗
- `uni.showActionSheet()` - 显示操作菜单
- `uni.showLoading()` / `uni.hideLoading()` - 显示/隐藏加载提示

### 导航 API
- `uni.navigateTo()` - 保留当前页面，跳转到应用内的某个页面
- `uni.redirectTo()` - 关闭当前页面，跳转到应用内的某个页面
- `uni.navigateBack()` - 关闭当前页面，返回上一页面或多级页面
- `uni.switchTab()` - 跳转到 tabBar 页面
- `uni.reLaunch()` - 关闭所有页面，打开到应用内的某个页面

### 媒体 API
- `uni.chooseImage()` - 从本地相册选择图片或使用相机拍照
- `uni.previewImage()` - 预览图片
- `uni.chooseVideo()` - 拍摄视频或从手机相册中选视频
- `uni.createVideoContext()` - 创建 video 上下文

## 插件和工具
- 通过 mcp 工具查询项目下可用的插件
- 跨页面通信优先使用 eventbus
- 使用 uni.$emit() 和 uni.$on() 进行全局事件通信

## API 使用最佳实践
- 异步 API 优先使用 Promise 形式
- 对于可能失败的 API 调用，务必添加错误处理
- 在组件销毁时及时清理事件监听器
- 使用条件编译确保 API 在目标平台可用