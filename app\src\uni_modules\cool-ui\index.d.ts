import type { ClActionSheetItem, ClActionSheetOptions, PassThroughProps, Type, ClButtonType, Size, ClListViewItem, ClFormLabelPosition, ClFormRule, ClFormValidateError, ClInputType, ClListItem, Justify, ClListViewGroup, ClListViewVirtualItem, ClListViewRefresherStatus, ClConfirmAction, ClConfirmOptions, ClToastOptions, ClSelectOption, ClPopupDirection, ClQrcodeMode, ClSelectDateShortcut, ClTabsItem, ClTextType, ClUploadItem } from "./types";
import { type UiInstance } from "./hooks";
import { type QrcodeOptions } from "./draw";

import type { ClActionSheetProps, ClActionSheetPassThrough } from "./components/cl-action-sheet/props";
import type { ClAvatarProps, ClAvatarPassThrough } from "./components/cl-avatar/props";
import type { ClBackTopProps } from "./components/cl-back-top/props";
import type { ClBadgeProps, ClBadgePassThrough } from "./components/cl-badge/props";
import type { ClBannerProps, ClBannerPassThrough } from "./components/cl-banner/props";
import type { ClButtonProps, ClButtonPassThrough } from "./components/cl-button/props";
import type { ClCascaderProps, ClCascaderPassThrough } from "./components/cl-cascader/props";
import type { ClCheckboxProps, ClCheckboxPassThrough } from "./components/cl-checkbox/props";
import type { ClColProps, ClColPassThrough } from "./components/cl-col/props";
import type { ClCollapseProps, ClCollapsePassThrough } from "./components/cl-collapse/props";
import type { ClCountdownProps, ClCountdownPassThrough } from "./components/cl-countdown/props";
import type { ClCropperProps, ClCropperPassThrough } from "./components/cl-cropper/props";
import type { ClDraggableProps, ClDraggablePassThrough } from "./components/cl-draggable/props";
import type { ClFloatViewProps } from "./components/cl-float-view/props";
import type { ClFooterProps, ClFooterPassThrough } from "./components/cl-footer/props";
import type { ClFormProps, ClFormPassThrough } from "./components/cl-form/props";
import type { ClFormItemProps, ClFormItemPassThrough } from "./components/cl-form-item/props";
import type { ClIconProps, ClIconPassThrough } from "./components/cl-icon/props";
import type { ClImageProps, ClImagePassThrough } from "./components/cl-image/props";
import type { ClIndexBarProps, ClIndexBarPassThrough } from "./components/cl-index-bar/props";
import type { ClInputProps, ClInputPassThrough } from "./components/cl-input/props";
import type { ClInputNumberProps, ClInputNumberPassThrough, ClInputNumberValuePassThrough, ClInputNumberOpPassThrough } from "./components/cl-input-number/props";
import type { ClInputOtpProps, ClInputOtpPassThrough } from "./components/cl-input-otp/props";
import type { ClKeyboardCarProps, ClKeyboardCarPassThrough } from "./components/cl-keyboard-car/props";
import type { ClKeyboardNumberProps, ClKeyboardNumberPassThrough } from "./components/cl-keyboard-number/props";
import type { ClKeyboardPasswordProps, ClKeyboardPasswordPassThrough } from "./components/cl-keyboard-password/props";
import type { ClListProps, ClListPassThrough } from "./components/cl-list/props";
import type { ClListItemProps, ClListItemPassThrough } from "./components/cl-list-item/props";
import type { ClListViewProps, ClListViewPassThrough } from "./components/cl-list-view/props";
import type { ClLoadingProps, ClLoadingPassThrough } from "./components/cl-loading/props";
import type { ClLoadmoreProps, ClLoadmorePassThrough } from "./components/cl-loadmore/props";
import type { ClNoticebarProps, ClNoticebarPassThrough } from "./components/cl-noticebar/props";
import type { ClPageProps } from "./components/cl-page/props";
import type { ClPageThemeProps } from "./components/cl-page-theme/props";
import type { ClPageUiProps } from "./components/cl-page-ui/props";
import type { ClPaginationProps, ClPaginationPassThrough } from "./components/cl-pagination/props";
import type { ClSelectPickerViewProps } from "./components/cl-select-picker-view/props";
import type { ClPopupProps, ClPopupPassThrough, ClPopupHeaderPassThrough } from "./components/cl-popup/props";
import type { ClProgressProps, ClProgressPassThrough } from "./components/cl-progress/props";
import type { ClProgressCircleProps, ClProgressCirclePassThrough } from "./components/cl-progress-circle/props";
import type { ClQrcodeProps } from "./components/cl-qrcode/props";
import type { ClRadioProps, ClRadioPassThrough } from "./components/cl-radio/props";
import type { ClRateProps, ClRatePassThrough } from "./components/cl-rate/props";
import type { ClRowProps, ClRowPassThrough } from "./components/cl-row/props";
import type { ClSafeAreaProps, ClSafeAreaPassThrough } from "./components/cl-safe-area/props";
import type { ClSelectProps, ClSelectPassThrough } from "./components/cl-select/props";
import type { ClSelectDateProps, ClSelectDatePassThrough } from "./components/cl-select-date/props";
import type { ClSelectTimeProps, ClSelectTimePassThrough } from "./components/cl-select-time/props";
import type { ClSelectTriggerProps, ClSelectTriggerPassThrough } from "./components/cl-select-trigger/props";
import type { ClSignProps, ClSignPassThrough } from "./components/cl-sign/props";
import type { ClSkeletonProps, ClSkeletonPassThrough } from "./components/cl-skeleton/props";
import type { ClSliderProps, ClSliderPassThrough } from "./components/cl-slider/props";
import type { ClStickyProps } from "./components/cl-sticky/props";
import type { ClSwitchProps, ClSwitchPassThrough } from "./components/cl-switch/props";
import type { ClTabsProps, ClTabsPassThrough } from "./components/cl-tabs/props";
import type { ClTagProps, ClTagPassThrough } from "./components/cl-tag/props";
import type { ClTextProps, ClTextPassThrough } from "./components/cl-text/props";
import type { ClTextareaProps, ClTextareaPassThrough } from "./components/cl-textarea/props";
import type { ClTimelineProps, ClTimelinePassThrough } from "./components/cl-timeline/props";
import type { ClTimelineItemProps, ClTimelineItemPassThrough } from "./components/cl-timeline-item/props";
import type { ClToastProps } from "./components/cl-toast/props";
import type { ClTopbarProps, ClTopbarPassThrough } from "./components/cl-topbar/props";
import type { ClUploadProps, ClUploadPassThrough } from "./components/cl-upload/props";
import type { ClWaterfallProps, ClWaterfallPassThrough } from "./components/cl-waterfall/props";

export {};

// 自动生成的组件全局类型声明
declare module "vue" {
	export interface GlobalComponents {
		"cl-action-sheet": (typeof import('./components/cl-action-sheet/cl-action-sheet.uvue')['default']) & import('vue').DefineComponent<ClActionSheetProps>;
		"cl-avatar": (typeof import('./components/cl-avatar/cl-avatar.uvue')['default']) & import('vue').DefineComponent<ClAvatarProps>;
		"cl-back-top": (typeof import('./components/cl-back-top/cl-back-top.uvue')['default']) & import('vue').DefineComponent<ClBackTopProps>;
		"cl-badge": (typeof import('./components/cl-badge/cl-badge.uvue')['default']) & import('vue').DefineComponent<ClBadgeProps>;
		"cl-banner": (typeof import('./components/cl-banner/cl-banner.uvue')['default']) & import('vue').DefineComponent<ClBannerProps>;
		"cl-button": (typeof import('./components/cl-button/cl-button.uvue')['default']) & import('vue').DefineComponent<ClButtonProps>;
		"cl-cascader": (typeof import('./components/cl-cascader/cl-cascader.uvue')['default']) & import('vue').DefineComponent<ClCascaderProps>;
		"cl-checkbox": (typeof import('./components/cl-checkbox/cl-checkbox.uvue')['default']) & import('vue').DefineComponent<ClCheckboxProps>;
		"cl-col": (typeof import('./components/cl-col/cl-col.uvue')['default']) & import('vue').DefineComponent<ClColProps>;
		"cl-collapse": (typeof import('./components/cl-collapse/cl-collapse.uvue')['default']) & import('vue').DefineComponent<ClCollapseProps>;
		"cl-countdown": (typeof import('./components/cl-countdown/cl-countdown.uvue')['default']) & import('vue').DefineComponent<ClCountdownProps>;
		"cl-cropper": (typeof import('./components/cl-cropper/cl-cropper.uvue')['default']) & import('vue').DefineComponent<ClCropperProps>;
		"cl-draggable": (typeof import('./components/cl-draggable/cl-draggable.uvue')['default']) & import('vue').DefineComponent<ClDraggableProps>;
		"cl-float-view": (typeof import('./components/cl-float-view/cl-float-view.uvue')['default']) & import('vue').DefineComponent<ClFloatViewProps>;
		"cl-footer": (typeof import('./components/cl-footer/cl-footer.uvue')['default']) & import('vue').DefineComponent<ClFooterProps>;
		"cl-form": (typeof import('./components/cl-form/cl-form.uvue')['default']) & import('vue').DefineComponent<ClFormProps>;
		"cl-form-item": (typeof import('./components/cl-form-item/cl-form-item.uvue')['default']) & import('vue').DefineComponent<ClFormItemProps>;
		"cl-icon": (typeof import('./components/cl-icon/cl-icon.uvue')['default']) & import('vue').DefineComponent<ClIconProps>;
		"cl-image": (typeof import('./components/cl-image/cl-image.uvue')['default']) & import('vue').DefineComponent<ClImageProps>;
		"cl-index-bar": (typeof import('./components/cl-index-bar/cl-index-bar.uvue')['default']) & import('vue').DefineComponent<ClIndexBarProps>;
		"cl-input": (typeof import('./components/cl-input/cl-input.uvue')['default']) & import('vue').DefineComponent<ClInputProps>;
		"cl-input-number": (typeof import('./components/cl-input-number/cl-input-number.uvue')['default']) & import('vue').DefineComponent<ClInputNumberProps>;
		"cl-input-otp": (typeof import('./components/cl-input-otp/cl-input-otp.uvue')['default']) & import('vue').DefineComponent<ClInputOtpProps>;
		"cl-keyboard-car": (typeof import('./components/cl-keyboard-car/cl-keyboard-car.uvue')['default']) & import('vue').DefineComponent<ClKeyboardCarProps>;
		"cl-keyboard-number": (typeof import('./components/cl-keyboard-number/cl-keyboard-number.uvue')['default']) & import('vue').DefineComponent<ClKeyboardNumberProps>;
		"cl-keyboard-password": (typeof import('./components/cl-keyboard-password/cl-keyboard-password.uvue')['default']) & import('vue').DefineComponent<ClKeyboardPasswordProps>;
		"cl-list": (typeof import('./components/cl-list/cl-list.uvue')['default']) & import('vue').DefineComponent<ClListProps>;
		"cl-list-item": (typeof import('./components/cl-list-item/cl-list-item.uvue')['default']) & import('vue').DefineComponent<ClListItemProps>;
		"cl-list-view": (typeof import('./components/cl-list-view/cl-list-view.uvue')['default']) & import('vue').DefineComponent<ClListViewProps>;
		"cl-loading": (typeof import('./components/cl-loading/cl-loading.uvue')['default']) & import('vue').DefineComponent<ClLoadingProps>;
		"cl-loadmore": (typeof import('./components/cl-loadmore/cl-loadmore.uvue')['default']) & import('vue').DefineComponent<ClLoadmoreProps>;
		"cl-noticebar": (typeof import('./components/cl-noticebar/cl-noticebar.uvue')['default']) & import('vue').DefineComponent<ClNoticebarProps>;
		"cl-page": (typeof import('./components/cl-page/cl-page.uvue')['default']) & import('vue').DefineComponent<ClPageProps>;
		"cl-page-theme": (typeof import('./components/cl-page-theme/cl-page-theme.uvue')['default']) & import('vue').DefineComponent<ClPageThemeProps>;
		"cl-page-ui": (typeof import('./components/cl-page-ui/cl-page-ui.uvue')['default']) & import('vue').DefineComponent<ClPageUiProps>;
		"cl-pagination": (typeof import('./components/cl-pagination/cl-pagination.uvue')['default']) & import('vue').DefineComponent<ClPaginationProps>;
		"cl-select-picker-view": (typeof import('./components/cl-select-picker-view/cl-select-picker-view.uvue')['default']) & import('vue').DefineComponent<ClSelectPickerViewProps>;
		"cl-popup": (typeof import('./components/cl-popup/cl-popup.uvue')['default']) & import('vue').DefineComponent<ClPopupProps>;
		"cl-progress": (typeof import('./components/cl-progress/cl-progress.uvue')['default']) & import('vue').DefineComponent<ClProgressProps>;
		"cl-progress-circle": (typeof import('./components/cl-progress-circle/cl-progress-circle.uvue')['default']) & import('vue').DefineComponent<ClProgressCircleProps>;
		"cl-qrcode": (typeof import('./components/cl-qrcode/cl-qrcode.uvue')['default']) & import('vue').DefineComponent<ClQrcodeProps>;
		"cl-radio": (typeof import('./components/cl-radio/cl-radio.uvue')['default']) & import('vue').DefineComponent<ClRadioProps>;
		"cl-rate": (typeof import('./components/cl-rate/cl-rate.uvue')['default']) & import('vue').DefineComponent<ClRateProps>;
		"cl-row": (typeof import('./components/cl-row/cl-row.uvue')['default']) & import('vue').DefineComponent<ClRowProps>;
		"cl-safe-area": (typeof import('./components/cl-safe-area/cl-safe-area.uvue')['default']) & import('vue').DefineComponent<ClSafeAreaProps>;
		"cl-select": (typeof import('./components/cl-select/cl-select.uvue')['default']) & import('vue').DefineComponent<ClSelectProps>;
		"cl-select-date": (typeof import('./components/cl-select-date/cl-select-date.uvue')['default']) & import('vue').DefineComponent<ClSelectDateProps>;
		"cl-select-time": (typeof import('./components/cl-select-time/cl-select-time.uvue')['default']) & import('vue').DefineComponent<ClSelectTimeProps>;
		"cl-select-trigger": (typeof import('./components/cl-select-trigger/cl-select-trigger.uvue')['default']) & import('vue').DefineComponent<ClSelectTriggerProps>;
		"cl-sign": (typeof import('./components/cl-sign/cl-sign.uvue')['default']) & import('vue').DefineComponent<ClSignProps>;
		"cl-skeleton": (typeof import('./components/cl-skeleton/cl-skeleton.uvue')['default']) & import('vue').DefineComponent<ClSkeletonProps>;
		"cl-slider": (typeof import('./components/cl-slider/cl-slider.uvue')['default']) & import('vue').DefineComponent<ClSliderProps>;
		"cl-sticky": (typeof import('./components/cl-sticky/cl-sticky.uvue')['default']) & import('vue').DefineComponent<ClStickyProps>;
		"cl-switch": (typeof import('./components/cl-switch/cl-switch.uvue')['default']) & import('vue').DefineComponent<ClSwitchProps>;
		"cl-tabs": (typeof import('./components/cl-tabs/cl-tabs.uvue')['default']) & import('vue').DefineComponent<ClTabsProps>;
		"cl-tag": (typeof import('./components/cl-tag/cl-tag.uvue')['default']) & import('vue').DefineComponent<ClTagProps>;
		"cl-text": (typeof import('./components/cl-text/cl-text.uvue')['default']) & import('vue').DefineComponent<ClTextProps>;
		"cl-textarea": (typeof import('./components/cl-textarea/cl-textarea.uvue')['default']) & import('vue').DefineComponent<ClTextareaProps>;
		"cl-timeline": (typeof import('./components/cl-timeline/cl-timeline.uvue')['default']) & import('vue').DefineComponent<ClTimelineProps>;
		"cl-timeline-item": (typeof import('./components/cl-timeline-item/cl-timeline-item.uvue')['default']) & import('vue').DefineComponent<ClTimelineItemProps>;
		"cl-toast": (typeof import('./components/cl-toast/cl-toast.uvue')['default']) & import('vue').DefineComponent<ClToastProps>;
		"cl-topbar": (typeof import('./components/cl-topbar/cl-topbar.uvue')['default']) & import('vue').DefineComponent<ClTopbarProps>;
		"cl-upload": (typeof import('./components/cl-upload/cl-upload.uvue')['default']) & import('vue').DefineComponent<ClUploadProps>;
		"cl-waterfall": (typeof import('./components/cl-waterfall/cl-waterfall.uvue')['default']) & import('vue').DefineComponent<ClWaterfallProps>;
	}
}
