# API接口测试脚本
# 测试Energy相关的API接口

Write-Host "=== API接口测试开始 ===" -ForegroundColor Green

# 测试基础连接
Write-Host "1. 测试API服务连接..." -ForegroundColor Yellow
try {
    $healthCheck = Invoke-WebRequest -Uri 'http://localhost:5005' -Method GET -TimeoutSec 10
    Write-Host "✓ API服务连接正常 (状态码: $($healthCheck.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ API服务连接失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试Swagger文档
Write-Host "2. 测试Swagger文档访问..." -ForegroundColor Yellow
try {
    $swaggerCheck = Invoke-WebRequest -Uri 'http://localhost:5005/kapi' -Method GET -TimeoutSec 10
    Write-Host "✓ Swagger文档访问正常 (状态码: $($swaggerCheck.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ Swagger文档访问失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试登录接口
Write-Host "3. 测试登录接口..." -ForegroundColor Yellow
$loginBody = @{
    account = "superadmin"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-WebRequest -Uri 'http://localhost:5005/api/sysAuth/login' -Method POST -Body $loginBody -ContentType 'application/json' -TimeoutSec 10
    $loginResult = $loginResponse.Content | ConvertFrom-Json
    
    if ($loginResult.code -eq 200) {
        Write-Host "✓ 登录成功" -ForegroundColor Green
        $accessToken = $loginResult.result.accessToken
        Write-Host "  获取到访问令牌: $($accessToken.Substring(0, 20))..." -ForegroundColor Cyan
        
        # 测试需要认证的API接口
        Write-Host "4. 测试Energy设备接口..." -ForegroundColor Yellow
        $headers = @{
            'Authorization' = "Bearer $accessToken"
            'Content-Type' = 'application/json'
        }
        
        try {
            $deviceResponse = Invoke-WebRequest -Uri 'http://localhost:5005/api/energyDevice/page' -Method GET -Headers $headers -TimeoutSec 10
            $deviceResult = $deviceResponse.Content | ConvertFrom-Json
            Write-Host "✓ Energy设备接口测试成功 (状态码: $($deviceResponse.StatusCode))" -ForegroundColor Green
            Write-Host "  返回数据: $($deviceResult.message)" -ForegroundColor Cyan
        } catch {
            Write-Host "✗ Energy设备接口测试失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # 测试Energy消耗接口
        Write-Host "5. 测试Energy消耗接口..." -ForegroundColor Yellow
        try {
            $consumptionResponse = Invoke-WebRequest -Uri 'http://localhost:5005/api/energyConsumption/page' -Method GET -Headers $headers -TimeoutSec 10
            $consumptionResult = $consumptionResponse.Content | ConvertFrom-Json
            Write-Host "✓ Energy消耗接口测试成功 (状态码: $($consumptionResponse.StatusCode))" -ForegroundColor Green
            Write-Host "  返回数据: $($consumptionResult.message)" -ForegroundColor Cyan
        } catch {
            Write-Host "✗ Energy消耗接口测试失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # 测试Energy控制接口
        Write-Host "6. 测试Energy控制接口..." -ForegroundColor Yellow
        try {
            $controlResponse = Invoke-WebRequest -Uri 'http://localhost:5005/api/energyControl/page' -Method GET -Headers $headers -TimeoutSec 10
            $controlResult = $controlResponse.Content | ConvertFrom-Json
            Write-Host "✓ Energy控制接口测试成功 (状态码: $($controlResponse.StatusCode))" -ForegroundColor Green
            Write-Host "  返回数据: $($controlResult.message)" -ForegroundColor Cyan
        } catch {
            Write-Host "✗ Energy控制接口测试失败: $($_.Exception.Message)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "✗ 登录失败: $($loginResult.message)" -ForegroundColor Red
        Write-Host "  可能需要验证码或其他认证方式" -ForegroundColor Yellow
        
        # 测试无需认证的接口
        Write-Host "7. 测试无认证接口..." -ForegroundColor Yellow
        try {
            $publicResponse = Invoke-WebRequest -Uri 'http://localhost:5005/api/energyDevice/page' -Method GET -TimeoutSec 10
            Write-Host "✓ 无认证接口可访问 (状态码: $($publicResponse.StatusCode))" -ForegroundColor Green
        } catch {
            Write-Host "✗ 无认证接口也无法访问: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "✗ 登录接口测试失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "  尝试无认证的接口测试..." -ForegroundColor Yellow
    
    # 测试无需认证的接口
    Write-Host "7. 测试无认证接口..." -ForegroundColor Yellow
    try {
        $publicResponse = Invoke-WebRequest -Uri 'http://localhost:5005/api/energyDevice/page' -Method GET -TimeoutSec 10
        Write-Host "✓ 无认证接口可访问 (状态码: $($publicResponse.StatusCode))" -ForegroundColor Green
    } catch {
        Write-Host "✗ 无认证接口也无法访问: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== API接口测试完成 ===" -ForegroundColor Green
Write-Host "请查看上述测试结果，确认API接口的可用性" -ForegroundColor Cyan