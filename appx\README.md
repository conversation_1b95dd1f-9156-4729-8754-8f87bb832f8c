# 节能灯控制APP客户端

## 项目介绍
这是一个基于UniApp X开发的节能灯控制APP客户端，用于监控和控制节能灯设备，查看能耗数据，以及管理设备场景等功能。

## 技术栈
- 框架：UniApp X
- 前端框架：Vue 3
- 编程语言：TypeScript
- 状态管理：Pinia
- UI组件：UniUI
- 构建工具：HBuilderX

## 项目结构
```
appx/
├── app/                 # 应用配置
│   └── config.ts        # 全局配置
├── pages/               # 页面文件
│   ├── index/           # 首页
│   │   └── home.uvue
│   ├── energy/          # 能耗相关页面
│   │   ├── device-list.uvue  # 设备列表
│   │   ├── lighting-control.uvue  # 照明控制
│   │   └── energy-monitor.uvue  # 能耗监控
│   └── user/            # 用户相关页面
│       └── my.uvue      # 我的页面
├── components/          # 组件
├── utils/               # 工具函数
├── types/               # 类型定义
├── stores/              # 状态管理
│   ├── user.ts          # 用户store
│   ├── device.ts        # 设备store
│   ├── energy.ts        # 能耗store
│   └── notification.ts  # 通知store
├── styles/              # 全局样式
├── App.uvue             # 应用入口
├── package.json         # 项目依赖
└── README.md            # 项目说明
```

## 功能模块
1. **设备管理**：查看所有设备列表，控制设备开关
2. **照明控制**：调节灯光亮度、色温，设置场景模式
3. **能耗监控**：查看能耗数据，生成能耗报表
4. **用户中心**：用户信息管理，系统设置

## 安装与运行
1. 克隆项目到本地
2. 安装依赖
```bash
npm install
```
3. 使用HBuilderX打开项目
4. 运行到指定平台（微信小程序、App等）

## 开发规范
请参考项目中的开发文档和规则：
- 开发指南：`.trae/documents/开发指南.md`
- 产品需求文档：`.trae/documents/产品需求文档.md`
- 技术架构文档：`.trae/documents/技术架构文档.md`
- 开发规则：`.trae/rules/`目录下的所有规则文档

## 注意事项
1. 开发前请确保已阅读并理解所有开发文档和规则
2. 项目使用TypeScript开发，请确保代码符合TypeScript规范
3. 提交代码前请进行代码检查和格式化
4. 如需添加新功能，请先确认是否符合项目架构和需求

## 联系方式
如有问题或建议，请联系项目负责人。