import { defineStore } from 'pinia'
import { ref } from 'vue'

// 用户信息接口定义
interface UserInfo {
  id: string
  name: string
  role: string
  avatar: string
  token: string
}

// 默认用户信息
const defaultUserInfo: UserInfo = {
  id: '',
  name: '',
  role: '',
  avatar: '',
  token: ''
}

// 定义用户store
export const useUserStore = defineStore('user', () => {
  // 响应式用户信息
  const userInfo = ref<UserInfo>(defaultUserInfo)
  const isLoggedIn = ref(false)

  // 初始化用户信息
  const initUserInfo = () => {
    try {
      // 从本地存储获取用户信息
      const storedUserInfo = uni.getStorageSync('userInfo')
      if (storedUserInfo) {
        userInfo.value = JSON.parse(storedUserInfo)
        isLoggedIn.value = true
      }
    } catch (error) {
      console.error('Failed to initialize user info:', error)
      resetUserInfo()
    }
  }

  // 获取用户信息
  const getUserInfo = () => {
    if (!isLoggedIn.value) {
      initUserInfo()
    }
    return userInfo.value
  }

  // 设置用户信息
  const setUserInfo = (info: Partial<UserInfo>) => {
    userInfo.value = { ...userInfo.value, ...info }
    isLoggedIn.value = !!userInfo.value.token
    // 保存到本地存储
    try {
      uni.setStorageSync('userInfo', JSON.stringify(userInfo.value))
    } catch (error) {
      console.error('Failed to save user info:', error)
    }
  }

  // 重置用户信息
  const resetUserInfo = () => {
    userInfo.value = { ...defaultUserInfo }
    isLoggedIn.value = false
    // 清除本地存储
    try {
      uni.removeStorageSync('userInfo')
    } catch (error) {
      console.error('Failed to remove user info:', error)
    }
  }

  // 登录
  const login = async (username: string, password: string) => {
    try {
      // 模拟登录API调用
      // 实际项目中应该替换为真实的API调用
      const response = await uni.request({
        url: '/api/user/login',
        method: 'POST',
        data: {
          username,
          password
        }
      })

      if (response.statusCode === 200 && response.data.success) {
        const userData = response.data.data
        setUserInfo(userData)
        return true
      } else {
        uni.showToast({
          title: response.data.message || '登录失败',
          icon: 'none'
        })
        return false
      }
    } catch (error) {
      console.error('Login failed:', error)
      uni.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
      return false
    }
  }

  // 退出登录
  const logout = () => {
    resetUserInfo()
  }

  // 初始化
  initUserInfo()

  return {
    userInfo,
    isLoggedIn,
    getUserInfo,
    setUserInfo,
    resetUserInfo,
    login,
    logout
  }
})