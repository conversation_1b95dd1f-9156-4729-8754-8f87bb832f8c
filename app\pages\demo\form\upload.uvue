<template>
	<cl-page>
		<view class="p-3">
			<demo-item :label="t('基础用法')">
				<cl-upload v-model="form.upload1" test></cl-upload>
			</demo-item>

			<demo-item :label="t('禁用')">
				<cl-upload v-model="form.upload1" disabled test></cl-upload>
			</demo-item>

			<demo-item :label="t('自定义图标、文字、大小')">
				<cl-upload
					v-model="form.upload1"
					icon="id-card-line"
					:text="t('上传证件照')"
					:width="300"
					:height="200"
					test
				></cl-upload>
			</demo-item>

			<demo-item :label="t('多选')">
				<cl-upload multiple v-model="form.upload2" test></cl-upload>
			</demo-item>

			<demo-item :label="t('限制 3 个')">
				<cl-upload multiple :limit="3" v-model="form.upload3" test></cl-upload>
			</demo-item>
		</view>
	</cl-page>
</template>

<script lang="ts" setup>
import { t } from "@/locale";
import DemoItem from "../components/item.uvue";
import { reactive } from "vue";

type Form = {
	upload1: string;
	upload2: string[];
	upload3: string[];
};

const form = reactive<Form>({
	upload1: "",
	upload2: [],
	upload3: []
});
</script>
