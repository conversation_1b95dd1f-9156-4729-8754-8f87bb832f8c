import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { request } from '@/cool/service';

// 设备类型定义
export interface EnergyDevice {
	id: string;
	name: string;
	type: 'led_light' | 'fluorescent' | 'halogen' | 'smart_bulb';
	location: string;
	status: 'online' | 'offline' | 'fault' | 'maintenance';
	brightness: number; // 0-100
	colorTemp: number; // 色温 2700-6500K
	color?: string; // RGB颜色值
	powerConsumption: number; // 当前功耗 W
	totalEnergy: number; // 总耗电量 kWh
	workingHours: number; // 工作时长 小时
	lastOnlineTime: string;
	installDate: string;
	model: string;
	manufacturer: string;
	version?: string;
	ip?: string;
	mac?: string;
	room?: string;
	floor?: string;
	building?: string;
	tags?: string[];
	isScheduled?: boolean;
	scheduleConfig?: {
		enabled: boolean;
		schedules: {
			id: string;
			name: string;
			startTime: string;
			endTime: string;
			brightness: number;
			colorTemp?: number;
			color?: string;
			weekdays: number[]; // 0-6 周日到周六
			enabled: boolean;
		}[];
	};
	faultInfo?: {
		id: string;
		type: string;
		description: string;
		occurTime: string;
		severity: 'low' | 'medium' | 'high' | 'critical';
		status: 'open' | 'processing' | 'resolved' | 'closed';
	};
}

// 设备控制参数
export interface DeviceControlParams {
	deviceId: string;
	action: 'power' | 'brightness' | 'colorTemp' | 'color' | 'scene';
	value: any;
	duration?: number; // 渐变时长 ms
}

// 批量控制参数
export interface BatchControlParams {
	deviceIds: string[];
	action: 'power_on' | 'power_off' | 'brightness' | 'colorTemp' | 'color' | 'scene';
	value?: any;
	duration?: number;
}

// 设备筛选参数
export interface DeviceFilterParams {
	status?: string[];
	type?: string[];
	location?: string;
	room?: string;
	floor?: string;
	building?: string;
	tags?: string[];
	keyword?: string;
	onlineOnly?: boolean;
	faultOnly?: boolean;
}

// 设备统计信息
export interface DeviceStats {
	total: number;
	online: number;
	offline: number;
	fault: number;
	maintenance: number;
	totalPower: number; // 总功耗 W
	totalEnergy: number; // 总耗电量 kWh
	averageWorkingHours: number;
	byType: Record<string, number>;
	byLocation: Record<string, number>;
	byStatus: Record<string, number>;
}

export const useEnergyDeviceStore = defineStore('energyDevice', () => {
	// 状态数据
	const devices = ref<EnergyDevice[]>([]);
	const selectedDevices = ref<string[]>([]);
	const currentDevice = ref<EnergyDevice | null>(null);
	const loading = ref(false);
	const refreshing = ref(false);
	const filterParams = ref<DeviceFilterParams>({});
	const searchKeyword = ref('');
	const sortBy = ref<'name' | 'status' | 'power' | 'energy' | 'workingHours'>('name');
	const sortOrder = ref<'asc' | 'desc'>('asc');
	const pageSize = ref(20);
	const currentPage = ref(1);
	const hasMore = ref(true);

	// 计算属性
	const filteredDevices = computed(() => {
		let result = devices.value;

		// 关键词搜索
		if (searchKeyword.value) {
			const keyword = searchKeyword.value.toLowerCase();
			result = result.filter(device => 
				device.name.toLowerCase().includes(keyword) ||
				device.location.toLowerCase().includes(keyword) ||
				device.model.toLowerCase().includes(keyword) ||
				device.manufacturer.toLowerCase().includes(keyword)
			);
		}

		// 状态筛选
		if (filterParams.value.status?.length) {
			result = result.filter(device => 
				filterParams.value.status!.includes(device.status)
			);
		}

		// 类型筛选
		if (filterParams.value.type?.length) {
			result = result.filter(device => 
				filterParams.value.type!.includes(device.type)
			);
		}

		// 位置筛选
		if (filterParams.value.location) {
			result = result.filter(device => 
				device.location.includes(filterParams.value.location!)
			);
		}

		// 房间筛选
		if (filterParams.value.room) {
			result = result.filter(device => 
				device.room === filterParams.value.room
			);
		}

		// 楼层筛选
		if (filterParams.value.floor) {
			result = result.filter(device => 
				device.floor === filterParams.value.floor
			);
		}

		// 建筑筛选
		if (filterParams.value.building) {
			result = result.filter(device => 
				device.building === filterParams.value.building
			);
		}

		// 标签筛选
		if (filterParams.value.tags?.length) {
			result = result.filter(device => 
				device.tags?.some(tag => 
					filterParams.value.tags!.includes(tag)
				)
			);
		}

		// 仅在线设备
		if (filterParams.value.onlineOnly) {
			result = result.filter(device => device.status === 'online');
		}

		// 仅故障设备
		if (filterParams.value.faultOnly) {
			result = result.filter(device => device.status === 'fault');
		}

		// 排序
		result.sort((a, b) => {
			let aValue: any, bValue: any;
			
			switch (sortBy.value) {
				case 'name':
					aValue = a.name;
					bValue = b.name;
					break;
				case 'status':
					aValue = a.status;
					bValue = b.status;
					break;
				case 'power':
					aValue = a.powerConsumption;
					bValue = b.powerConsumption;
					break;
				case 'energy':
					aValue = a.totalEnergy;
					bValue = b.totalEnergy;
					break;
				case 'workingHours':
					aValue = a.workingHours;
					bValue = b.workingHours;
					break;
				default:
					return 0;
			}

			if (typeof aValue === 'string') {
				return sortOrder.value === 'asc' 
					? aValue.localeCompare(bValue)
					: bValue.localeCompare(aValue);
			} else {
				return sortOrder.value === 'asc' 
					? aValue - bValue
					: bValue - aValue;
			}
		});

		return result;
	});

	const deviceStats = computed((): DeviceStats => {
		const stats: DeviceStats = {
			total: devices.value.length,
			online: 0,
			offline: 0,
			fault: 0,
			maintenance: 0,
			totalPower: 0,
			totalEnergy: 0,
			averageWorkingHours: 0,
			byType: {},
			byLocation: {},
			byStatus: {}
		};

		devices.value.forEach(device => {
			// 状态统计
			switch (device.status) {
				case 'online':
					stats.online++;
					break;
				case 'offline':
					stats.offline++;
					break;
				case 'fault':
					stats.fault++;
					break;
				case 'maintenance':
					stats.maintenance++;
					break;
			}

			// 功耗和能耗统计
			stats.totalPower += device.powerConsumption;
			stats.totalEnergy += device.totalEnergy;

			// 类型统计
			stats.byType[device.type] = (stats.byType[device.type] || 0) + 1;

			// 位置统计
			stats.byLocation[device.location] = (stats.byLocation[device.location] || 0) + 1;

			// 状态统计
			stats.byStatus[device.status] = (stats.byStatus[device.status] || 0) + 1;
		});

		// 平均工作时长
		if (devices.value.length > 0) {
			stats.averageWorkingHours = devices.value.reduce((sum, device) => 
				sum + device.workingHours, 0
			) / devices.value.length;
		}

		return stats;
	});

	const selectedDeviceList = computed(() => {
		return devices.value.filter(device => 
			selectedDevices.value.includes(device.id)
		);
	});

	const onlineDevices = computed(() => {
		return devices.value.filter(device => device.status === 'online');
	});

	const faultDevices = computed(() => {
		return devices.value.filter(device => device.status === 'fault');
	});

	// Actions
	const fetchDevices = async (refresh = false) => {
		if (refresh) {
			refreshing.value = true;
			currentPage.value = 1;
			hasMore.value = true;
		} else {
			loading.value = true;
		}

		try {
			const params = {
				page: currentPage.value,
				size: pageSize.value,
				keyword: searchKeyword.value,
				...filterParams.value
			};

			const res = await request({
				url: '/energy/device/list',
				method: 'GET',
				params
			});

			if (refresh || currentPage.value === 1) {
				devices.value = res.data.list || [];
			} else {
				devices.value.push(...(res.data.list || []));
			}

			hasMore.value = res.data.pagination?.hasMore || false;
			currentPage.value++;

			return res.data;
		} catch (error) {
			console.error('获取设备列表失败:', error);
			throw error;
		} finally {
			loading.value = false;
			refreshing.value = false;
		}
	};

	const fetchDeviceDetail = async (deviceId: string) => {
		loading.value = true;
		try {
			const res = await request({
				url: `/energy/device/${deviceId}`,
				method: 'GET'
			});

			currentDevice.value = res.data;
			
			// 更新设备列表中的对应设备
			const index = devices.value.findIndex(d => d.id === deviceId);
			if (index !== -1) {
				devices.value[index] = res.data;
			}

			return res.data;
		} catch (error) {
			console.error('获取设备详情失败:', error);
			throw error;
		} finally {
			loading.value = false;
		}
	};

	const controlDevice = async (params: DeviceControlParams) => {
		try {
			const res = await request({
				url: '/energy/device/control',
				method: 'POST',
				data: params
			});

			// 更新本地设备状态
			const device = devices.value.find(d => d.id === params.deviceId);
			if (device && res.data) {
				Object.assign(device, res.data);
			}

			return res.data;
		} catch (error) {
			console.error('设备控制失败:', error);
			throw error;
		}
	};

	const batchControl = async (params: BatchControlParams) => {
		try {
			const res = await request({
				url: '/energy/device/batch-control',
				method: 'POST',
				data: params
			});

			// 更新本地设备状态
			if (res.data?.results) {
				res.data.results.forEach((result: any) => {
					const device = devices.value.find(d => d.id === result.deviceId);
					if (device && result.success) {
						Object.assign(device, result.data);
					}
				});
			}

			return res.data;
		} catch (error) {
			console.error('批量控制失败:', error);
			throw error;
		}
	};

	const addDevice = async (deviceData: Partial<EnergyDevice>) => {
		try {
			const res = await request({
				url: '/energy/device',
				method: 'POST',
				data: deviceData
			});

			devices.value.unshift(res.data);
			return res.data;
		} catch (error) {
			console.error('添加设备失败:', error);
			throw error;
		}
	};

	const updateDevice = async (deviceId: string, deviceData: Partial<EnergyDevice>) => {
		try {
			const res = await request({
				url: `/energy/device/${deviceId}`,
				method: 'PUT',
				data: deviceData
			});

			const index = devices.value.findIndex(d => d.id === deviceId);
			if (index !== -1) {
				devices.value[index] = res.data;
			}

			if (currentDevice.value?.id === deviceId) {
				currentDevice.value = res.data;
			}

			return res.data;
		} catch (error) {
			console.error('更新设备失败:', error);
			throw error;
		}
	};

	const deleteDevice = async (deviceId: string) => {
		try {
			await request({
				url: `/energy/device/${deviceId}`,
				method: 'DELETE'
			});

			devices.value = devices.value.filter(d => d.id !== deviceId);
			selectedDevices.value = selectedDevices.value.filter(id => id !== deviceId);

			if (currentDevice.value?.id === deviceId) {
				currentDevice.value = null;
			}
		} catch (error) {
			console.error('删除设备失败:', error);
			throw error;
		}
	};

	// 工具方法
	const setFilter = (filter: Partial<DeviceFilterParams>) => {
		filterParams.value = { ...filterParams.value, ...filter };
		currentPage.value = 1;
		hasMore.value = true;
	};

	const clearFilter = () => {
		filterParams.value = {};
		searchKeyword.value = '';
		currentPage.value = 1;
		hasMore.value = true;
	};

	const setSort = (field: typeof sortBy.value, order: typeof sortOrder.value) => {
		sortBy.value = field;
		sortOrder.value = order;
	};

	const toggleDeviceSelection = (deviceId: string) => {
		const index = selectedDevices.value.indexOf(deviceId);
		if (index > -1) {
			selectedDevices.value.splice(index, 1);
		} else {
			selectedDevices.value.push(deviceId);
		}
	};

	const selectAllDevices = () => {
		selectedDevices.value = filteredDevices.value.map(d => d.id);
	};

	const clearSelection = () => {
		selectedDevices.value = [];
	};

	const refreshDeviceStatus = async (deviceId?: string) => {
		try {
			const url = deviceId 
				? `/energy/device/${deviceId}/status`
				: '/energy/device/status';

			const res = await request({
				url,
				method: 'GET'
			});

			if (deviceId) {
				// 更新单个设备状态
				const device = devices.value.find(d => d.id === deviceId);
				if (device && res.data) {
					Object.assign(device, res.data);
				}
			} else {
				// 批量更新设备状态
				if (res.data?.devices) {
					res.data.devices.forEach((statusData: any) => {
						const device = devices.value.find(d => d.id === statusData.id);
						if (device) {
							Object.assign(device, statusData);
						}
					});
				}
			}

			return res.data;
		} catch (error) {
			console.error('刷新设备状态失败:', error);
			throw error;
		}
	};

	return {
		// 状态
		devices,
		selectedDevices,
		currentDevice,
		loading,
		refreshing,
		filterParams,
		searchKeyword,
		sortBy,
		sortOrder,
		pageSize,
		currentPage,
		hasMore,

		// 计算属性
		filteredDevices,
		deviceStats,
		selectedDeviceList,
		onlineDevices,
		faultDevices,

		// 方法
		fetchDevices,
		fetchDeviceDetail,
		controlDevice,
		batchControl,
		addDevice,
		updateDevice,
		deleteDevice,
		setFilter,
		clearFilter,
		setSort,
		toggleDeviceSelection,
		selectAllDevices,
		clearSelection,
		refreshDeviceStatus
	};
});