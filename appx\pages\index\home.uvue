<template>
  <cl-page>
    <view class="home-container">
      <!-- 顶部状态栏 -->
      <view class="status-bar"></view>
      
      <!-- 头部信息 -->
      <view class="header">
        <text class="welcome-text">欢迎使用节能灯管理系统</text>
        <text class="subtitle">实时监控您的照明设备</text>
      </view>
      
      <!-- 数据概览 -->
      <view class="overview-cards">
        <view class="card">
          <text class="card-title">设备总数</text>
          <text class="card-value">{{ totalDevices }}</text>
        </view>
        <view class="card">
          <text class="card-title">在线设备</text>
          <text class="card-value">{{ onlineDevices }}</text>
        </view>
        <view class="card">
          <text class="card-title">今日能耗</text>
          <text class="card-value">{{ todayEnergy }} kWh</text>
        </view>
      </view>
      
      <!-- 快速控制 -->
      <view class="quick-controls">
        <view class="control-title">快速控制</view>
        <view class="control-buttons">
          <button class="control-btn" @click="navigateToDeviceList">
            <image src="/static/icons/device.png" class="btn-icon" />
            <text>设备列表</text>
          </button>
          <button class="control-btn" @click="navigateToLightingControl">
            <image src="/static/icons/light.png" class="btn-icon" />
            <text>照明控制</text>
          </button>
          <button class="control-btn" @click="navigateToEnergyMonitor">
            <image src="/static/icons/energy.png" class="btn-icon" />
            <text>能耗监控</text>
          </button>
        </view>
      </view>
      
      <!-- 设备状态图表 -->
      <view class="chart-container">
        <text class="chart-title">近7日能耗趋势</text>
        <!-- 这里将使用echarts或其他图表组件 -->
        <view class="chart-placeholder">
          <text>图表加载中...</text>
        </view>
      </view>
    </view>
  </cl-page>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useEnergyStore } from '@/stores/energy'
import { useDeviceStore } from '@/stores/device'
import { navigateTo } from 'uni-app'

// 响应式数据
const totalDevices = ref(0)
const onlineDevices = ref(0)
const todayEnergy = ref(0)
const energyTrend = ref<number[]>([])

//  stores
const energyStore = useEnergyStore()
const deviceStore = useDeviceStore()

// 生命周期
onMounted(() => {
  console.log('Home page mounted')
  // 初始化数据
  initData()
})

// 初始化数据
const initData = async () => {
  try {
    // 获取设备总数和在线数
    const deviceStats = await deviceStore.getDeviceStats()
    totalDevices.value = deviceStats.total
    onlineDevices.value = deviceStats.online

    // 获取今日能耗
    const energyData = await energyStore.getTodayEnergy()
    todayEnergy.value = energyData

    // 获取近7日能耗趋势
    const trendData = await energyStore.getEnergyTrend(7)
    energyTrend.value = trendData
  } catch (error) {
    console.error('Failed to initialize data:', error)
    uni.showToast({
      title: '数据加载失败',
      icon: 'none'
    })
  }
}

// 导航到设备列表
const navigateToDeviceList = () => {
  navigateTo({
    url: '/pages/energy/device-list'
  })
}

// 导航到照明控制
const navigateToLightingControl = () => {
  navigateTo({
    url: '/pages/energy/lighting-control'
  })
}

// 导航到能耗监控
const navigateToEnergyMonitor = () => {
  navigateTo({
    url: '/pages/energy/energy-monitor'
  })
}
</script>

<style>
.home-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  padding: 0 20rpx;
}

.status-bar {
  height: var(--status-bar-height);
  background-color: #ffffff;
}

.header {
  padding: 30rpx 0;
  text-align: center;
}

.welcome-text {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 14px;
  color: #666;
  margin-top: 10rpx;
}

.overview-cards {
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0;
}

.card {
  flex: 1;
  margin: 0 10rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  text-align: center;
}

.card-title {
  font-size: 14px;
  color: #666;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-top: 10rpx;
}

.quick-controls {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 20rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.control-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.control-buttons {
  display: flex;
  justify-content: space-between;
}

.control-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 12rpx;
  padding: 20rpx 10rpx;
  margin: 0 10rpx;
}

.btn-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.chart-container {
  flex: 1;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 20rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.chart-placeholder {
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
}
</style>