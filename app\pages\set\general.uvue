<template>
	<cl-page>
		<view class="p-3">
			<cl-list>
				<cl-list-item :label="t('深色模式')">
					<cl-switch :model-value="isDark" @change="onThemeChange"></cl-switch>
				</cl-list-item>

				<cl-list-item :label="t('多语言')" arrow hoverable @tap="setLocale"> </cl-list-item>
			</cl-list>
		</view>

		<locale-set :ref="refs.set('localeSet')"></locale-set>
	</cl-page>
</template>

<script setup lang="ts">
import { isDark, toggleTheme, useRefs } from "@/cool";
import { t } from "@/locale";
import LocaleSet from "@/components/locale-set.uvue";

const refs = useRefs();

function onThemeChange() {
	toggleTheme();
}

function setLocale() {
	refs.open("localeSet");
}
</script>
