import { service as request } from '/@/utils/request';
import { useBaseApi } from '../base';

/**
 * 报警管理API接口集合
 * @method getPage 获取报警分页列表
 * @method getList 获取报警列表
 * @method add 添加报警
 * @method update 更新报警
 * @method delete 删除报警
 * @method getStats 获取报警统计
 * @method getRecentAlarms 获取最近报警
 * @method handleAlarm 处理报警
 */
export function useAlarmApi() {
	return {
		...useBaseApi('alarm'),
		
		// 获取报警统计
		getStats: () => {
			return request({
				url: '/api/alarm/stats',
				method: 'get',
			});
		},
		
		// 获取报警列表
		getAlarmList: (params?: any) => {
			return request({
				url: '/api/alarm/list',
				method: 'get',
				params,
			});
		},
		
		// 获取最近报警
		getRecentAlarms: (params: any) => {
			return request({
				url: '/api/alarm/recent',
				method: 'get',
				params,
			});
		},
		
		// 处理报警
		handleAlarm: (data: any) => {
			return request({
				url: '/api/alarm/handle',
				method: 'post',
				data,
			});
		},
	};
}