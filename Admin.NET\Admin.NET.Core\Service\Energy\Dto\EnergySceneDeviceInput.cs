// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 场景设备分页查询输入参数
/// </summary>
public class EnergySceneDeviceInput : BasePageInput
{
    /// <summary>
    /// 场景ID
    /// </summary>
    public long? SceneId { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long? DeviceId { get; set; }

    /// <summary>
    /// 设备编码
    /// </summary>
    public string? DeviceCode { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string? DeviceName { get; set; }

    /// <summary>
    /// 控制类型
    /// </summary>
    public string? ControlType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
}

/// <summary>
/// 场景设备ID查询输入参数
/// </summary>
public class QueryByIdEnergySceneDeviceInput : DeleteEnergySceneDeviceInput
{
}

/// <summary>
/// 添加场景设备输入参数
/// </summary>
public class AddEnergySceneDeviceInput
{
    /// <summary>
    /// 场景ID
    /// </summary>
    [Required(ErrorMessage = "场景ID不能为空")]
    public long SceneId { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public long DeviceId { get; set; }

    /// <summary>
    /// 控制类型
    /// </summary>
    [Required(ErrorMessage = "控制类型不能为空")]
    public string ControlType { get; set; }

    /// <summary>
    /// 控制命令
    /// </summary>
    [Required(ErrorMessage = "控制命令不能为空")]
    public string ControlCommand { get; set; }

    /// <summary>
    /// 控制参数
    /// </summary>
    public string? ControlParams { get; set; }

    /// <summary>
    /// 执行延迟（秒）
    /// </summary>
    public int ExecuteDelay { get; set; } = 0;

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; } = 100;

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; } = 1;

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// 更新场景设备输入参数
/// </summary>
public class UpdateEnergySceneDeviceInput : AddEnergySceneDeviceInput
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [Required(ErrorMessage = "主键ID不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// 删除场景设备输入参数
/// </summary>
public class DeleteEnergySceneDeviceInput : BaseIdInput
{
}

/// <summary>
/// 批量添加场景设备输入参数
/// </summary>
public class BatchAddSceneDeviceInput
{
    /// <summary>
    /// 场景ID
    /// </summary>
    [Required(ErrorMessage = "场景ID不能为空")]
    public long SceneId { get; set; }

    /// <summary>
    /// 场景设备列表
    /// </summary>
    [Required(ErrorMessage = "场景设备列表不能为空")]
    public List<AddEnergySceneDeviceInput> SceneDevices { get; set; }
}

/// <summary>
/// 批量删除场景设备输入参数
/// </summary>
public class BatchDeleteSceneDeviceInput
{
    /// <summary>
    /// 场景设备ID列表
    /// </summary>
    [Required(ErrorMessage = "场景设备ID列表不能为空")]
    public List<long> Ids { get; set; }
}

/// <summary>
/// 场景设备状态输入参数
/// </summary>
public class EnergySceneDeviceStatusInput : BaseIdInput
{
    /// <summary>
    /// 状态
    /// </summary>
    [Required(ErrorMessage = "状态不能为空")]
    public int Status { get; set; }
}

/// <summary>
/// 调整场景设备排序输入参数
/// </summary>
public class AdjustSceneDeviceSortInput : BaseIdInput
{
    /// <summary>
    /// 新排序值
    /// </summary>
    [Required(ErrorMessage = "排序值不能为空")]
    public int Sort { get; set; }
}

/// <summary>
/// 复制场景设备输入参数
/// </summary>
public class CopySceneDeviceInput
{
    /// <summary>
    /// 源场景ID
    /// </summary>
    [Required(ErrorMessage = "源场景ID不能为空")]
    public long SourceSceneId { get; set; }

    /// <summary>
    /// 目标场景ID
    /// </summary>
    [Required(ErrorMessage = "目标场景ID不能为空")]
    public long TargetSceneId { get; set; }

    /// <summary>
    /// 设备ID列表（为空则复制所有设备）
    /// </summary>
    public List<long>? DeviceIds { get; set; }
}