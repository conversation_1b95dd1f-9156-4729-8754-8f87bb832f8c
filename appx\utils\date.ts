// 日期时间工具函数

// 格式化日期
export const formatDate = (date: Date | string | number, format: string = 'YYYY-MM-DD'): string => {
  const d = new Date(date)
  if (isNaN(d.getTime())) {
    return ''
  }

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

// 格式化时间
export const formatTime = (date: Date | string | number): string => {
  return formatDate(date, 'HH:mm:ss')
}

// 格式化日期时间
export const formatDateTime = (date: Date | string | number): string => {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss')
}

// 相对时间格式化
export const formatRelativeTime = (date: Date | string | number): string => {
  const d = new Date(date)
  const now = new Date()
  const diff = now.getTime() - d.getTime()

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`
  } else {
    return `${Math.floor(diff / year)}年前`
  }
}

// 获取今天的开始时间
export const getStartOfDay = (date?: Date | string | number): Date => {
  const d = date ? new Date(date) : new Date()
  d.setHours(0, 0, 0, 0)
  return d
}

// 获取今天的结束时间
export const getEndOfDay = (date?: Date | string | number): Date => {
  const d = date ? new Date(date) : new Date()
  d.setHours(23, 59, 59, 999)
  return d
}

// 获取本周的开始时间
export const getStartOfWeek = (date?: Date | string | number): Date => {
  const d = date ? new Date(date) : new Date()
  const day = d.getDay()
  const diff = d.getDate() - day + (day === 0 ? -6 : 1) // 周一为一周的开始
  const startOfWeek = new Date(d.setDate(diff))
  startOfWeek.setHours(0, 0, 0, 0)
  return startOfWeek
}

// 获取本月的开始时间
export const getStartOfMonth = (date?: Date | string | number): Date => {
  const d = date ? new Date(date) : new Date()
  return new Date(d.getFullYear(), d.getMonth(), 1, 0, 0, 0, 0)
}

// 获取本年的开始时间
export const getStartOfYear = (date?: Date | string | number): Date => {
  const d = date ? new Date(date) : new Date()
  return new Date(d.getFullYear(), 0, 1, 0, 0, 0, 0)
}

// 获取日期范围
export const getDateRange = (type: string): { start: Date; end: Date } => {
  const now = new Date()
  let start: Date
  let end: Date

  switch (type) {
    case 'today':
      start = getStartOfDay(now)
      end = getEndOfDay(now)
      break
    case 'yesterday':
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      start = getStartOfDay(yesterday)
      end = getEndOfDay(yesterday)
      break
    case '7days':
      start = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000)
      start = getStartOfDay(start)
      end = getEndOfDay(now)
      break
    case '30days':
      start = new Date(now.getTime() - 29 * 24 * 60 * 60 * 1000)
      start = getStartOfDay(start)
      end = getEndOfDay(now)
      break
    case 'thisWeek':
      start = getStartOfWeek(now)
      end = getEndOfDay(now)
      break
    case 'thisMonth':
      start = getStartOfMonth(now)
      end = getEndOfDay(now)
      break
    case 'lastMonth':
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      start = getStartOfMonth(lastMonth)
      end = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999)
      break
    case 'thisYear':
      start = getStartOfYear(now)
      end = getEndOfDay(now)
      break
    default:
      start = getStartOfDay(now)
      end = getEndOfDay(now)
  }

  return { start, end }
}

// 判断是否为同一天
export const isSameDay = (date1: Date | string | number, date2: Date | string | number): boolean => {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  return d1.getFullYear() === d2.getFullYear() &&
         d1.getMonth() === d2.getMonth() &&
         d1.getDate() === d2.getDate()
}

// 判断是否为今天
export const isToday = (date: Date | string | number): boolean => {
  return isSameDay(date, new Date())
}

// 判断是否为昨天
export const isYesterday = (date: Date | string | number): boolean => {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return isSameDay(date, yesterday)
}

// 计算两个日期之间的天数差
export const getDaysDiff = (date1: Date | string | number, date2: Date | string | number): number => {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  const timeDiff = Math.abs(d2.getTime() - d1.getTime())
  return Math.ceil(timeDiff / (1000 * 3600 * 24))
}

// 添加天数
export const addDays = (date: Date | string | number, days: number): Date => {
  const d = new Date(date)
  d.setDate(d.getDate() + days)
  return d
}

// 添加小时
export const addHours = (date: Date | string | number, hours: number): Date => {
  const d = new Date(date)
  d.setHours(d.getHours() + hours)
  return d
}

// 添加分钟
export const addMinutes = (date: Date | string | number, minutes: number): Date => {
  const d = new Date(date)
  d.setMinutes(d.getMinutes() + minutes)
  return d
}

// 获取月份天数
export const getDaysInMonth = (year: number, month: number): number => {
  return new Date(year, month, 0).getDate()
}

// 获取星期几
export const getWeekday = (date: Date | string | number, locale: string = 'zh-CN'): string => {
  const d = new Date(date)
  const weekdays = {
    'zh-CN': ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
    'en-US': ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  }
  return weekdays[locale as keyof typeof weekdays][d.getDay()]
}

// 解析日期字符串
export const parseDate = (dateString: string): Date | null => {
  try {
    const date = new Date(dateString)
    return isNaN(date.getTime()) ? null : date
  } catch (error) {
    return null
  }
}

// 验证日期格式
export const isValidDate = (date: any): boolean => {
  return date instanceof Date && !isNaN(date.getTime())
}

// 获取时间戳
export const getTimestamp = (date?: Date | string | number): number => {
  return date ? new Date(date).getTime() : Date.now()
}

// 从时间戳创建日期
export const fromTimestamp = (timestamp: number): Date => {
  return new Date(timestamp)
}
