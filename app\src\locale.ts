import { ref } from 'vue';

// 当前语言
export const locale = ref('zh-CN');

// 语言包
const messages = {
  'zh-CN': {
    '无权限': '无权限',
    '服务异常': '服务异常',
    '网络异常': '网络异常',
    '请求超时': '请求超时',
    '登录': '登录',
    '退出': '退出',
    '确认': '确认',
    '取消': '取消'
  },
  'en-US': {
    '无权限': 'No Permission',
    '服务异常': 'Service Error',
    '网络异常': 'Network Error',
    '请求超时': 'Request Timeout',
    '登录': 'Login',
    '退出': 'Logout',
    '确认': 'Confirm',
    '取消': 'Cancel'
  }
};

// 翻译函数
export function t(key: string): string {
  const currentMessages = messages[locale.value] || messages['zh-CN'];
  return currentMessages[key] || key;
}

// 设置语言
export function setLocale(lang: string) {
  locale.value = lang;
}

// 初始化语言设置
export function initLocale() {
  // 检查 uni 对象是否存在
  if (typeof uni === 'undefined') {
    locale.value = 'zh-CN';
    return;
  }
  
  // 从本地存储获取语言设置，如果没有则使用默认语言
  const savedLocale = uni.getStorageSync('locale') || 'zh-CN';
  locale.value = savedLocale;
}