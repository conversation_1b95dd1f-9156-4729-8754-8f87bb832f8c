import { ref, computed } from 'vue';
import { defineStore } from 'pinia';

// 用户设置类型定义
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  timezone: string;
  dateFormat: 'YYYY-MM-DD' | 'MM/DD/YYYY' | 'DD/MM/YYYY';
  timeFormat: '24h' | '12h';
}

// 通知设置
export interface NotificationSettings {
  enabled: boolean;
  email: boolean;
  push: boolean;
  sms: boolean;
  deviceOffline: boolean;
  energyAlert: boolean;
  maintenanceReminder: boolean;
  weeklyReport: boolean;
  monthlyReport: boolean;
  alertThreshold: {
    powerUsage: number; // 功率使用阈值 (W)
    energyConsumption: number; // 能耗阈值 (kWh)
    cost: number; // 费用阈值 (元)
  };
}

// 仪表板设置
export interface DashboardSettings {
  layout: 'grid' | 'list' | 'card';
  refreshInterval: number; // 刷新间隔 (秒)
  showRealTime: boolean;
  showCharts: boolean;
  showSuggestions: boolean;
  defaultTimeRange: '1h' | '6h' | '24h' | '7d' | '30d';
  widgets: {
    id: string;
    type: 'power' | 'energy' | 'cost' | 'devices' | 'chart' | 'suggestions';
    position: { x: number; y: number; w: number; h: number };
    visible: boolean;
  }[];
}

// 控制设置
export interface ControlSettings {
  autoSchedule: boolean;
  scheduleRules: {
    id: string;
    name: string;
    enabled: boolean;
    deviceIds: string[];
    timeRanges: {
      start: string; // HH:mm
      end: string; // HH:mm
      days: number[]; // 0-6 (周日到周六)
      action: 'on' | 'off' | 'dim';
      brightness?: number;
    }[];
  }[];
  autoBrightness: boolean;
  brightnessSchedule: {
    enabled: boolean;
    rules: {
      time: string; // HH:mm
      brightness: number; // 0-100
    }[];
  };
  energySaving: {
    enabled: boolean;
    mode: 'eco' | 'balanced' | 'performance';
    maxPowerLimit: number; // 最大功率限制 (W)
    autoOffDelay: number; // 自动关闭延迟 (分钟)
  };
}

// 隐私设置
export interface PrivacySettings {
  dataCollection: boolean;
  analytics: boolean;
  crashReports: boolean;
  usageStatistics: boolean;
  locationTracking: boolean;
  dataRetention: number; // 数据保留天数
  exportData: boolean;
  deleteAccount: boolean;
}

// 设置更新参数
export interface SettingsUpdateParams {
  user?: Partial<UserSettings>;
  notification?: Partial<NotificationSettings>;
  dashboard?: Partial<DashboardSettings>;
  control?: Partial<ControlSettings>;
  privacy?: Partial<PrivacySettings>;
}

export const useEnergySettingsStore = defineStore('energySettings', () => {
  // 用户设置
  const userSettings = ref<UserSettings>({
    theme: 'auto',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: '24h'
  });
  
  // 通知设置
  const notificationSettings = ref<NotificationSettings>({
    enabled: true,
    email: true,
    push: true,
    sms: false,
    deviceOffline: true,
    energyAlert: true,
    maintenanceReminder: true,
    weeklyReport: true,
    monthlyReport: true,
    alertThreshold: {
      powerUsage: 100,
      energyConsumption: 50,
      cost: 100
    }
  });
  
  // 仪表板设置
  const dashboardSettings = ref<DashboardSettings>({
    layout: 'grid',
    refreshInterval: 30,
    showRealTime: true,
    showCharts: true,
    showSuggestions: true,
    defaultTimeRange: '24h',
    widgets: [
      { id: '1', type: 'power', position: { x: 0, y: 0, w: 2, h: 1 }, visible: true },
      { id: '2', type: 'energy', position: { x: 2, y: 0, w: 2, h: 1 }, visible: true },
      { id: '3', type: 'cost', position: { x: 4, y: 0, w: 2, h: 1 }, visible: true },
      { id: '4', type: 'devices', position: { x: 0, y: 1, w: 3, h: 2 }, visible: true },
      { id: '5', type: 'chart', position: { x: 3, y: 1, w: 3, h: 2 }, visible: true },
      { id: '6', type: 'suggestions', position: { x: 0, y: 3, w: 6, h: 1 }, visible: true }
    ]
  });
  
  // 控制设置
  const controlSettings = ref<ControlSettings>({
    autoSchedule: false,
    scheduleRules: [],
    autoBrightness: false,
    brightnessSchedule: {
      enabled: false,
      rules: [
        { time: '08:00', brightness: 80 },
        { time: '12:00', brightness: 100 },
        { time: '18:00', brightness: 60 },
        { time: '22:00', brightness: 30 }
      ]
    },
    energySaving: {
      enabled: false,
      mode: 'balanced',
      maxPowerLimit: 1000,
      autoOffDelay: 30
    }
  });
  
  // 隐私设置
  const privacySettings = ref<PrivacySettings>({
    dataCollection: true,
    analytics: true,
    crashReports: true,
    usageStatistics: true,
    locationTracking: false,
    dataRetention: 365,
    exportData: true,
    deleteAccount: false
  });
  
  // 加载状态
  const loading = ref(false);
  
  // 计算属性：当前主题
  const currentTheme = computed(() => {
    if (userSettings.value.theme === 'auto') {
      // 根据系统时间判断
      const hour = new Date().getHours();
      return hour >= 6 && hour < 18 ? 'light' : 'dark';
    }
    return userSettings.value.theme;
  });
  
  // 计算属性：是否启用通知
  const notificationsEnabled = computed(() => {
    return notificationSettings.value.enabled;
  });
  
  // 加载设置
  const loadSettings = async () => {
    loading.value = true;
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 从本地存储或API加载设置
      const savedSettings = localStorage.getItem('energy-settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        if (parsed.user) userSettings.value = { ...userSettings.value, ...parsed.user };
        if (parsed.notification) notificationSettings.value = { ...notificationSettings.value, ...parsed.notification };
        if (parsed.dashboard) dashboardSettings.value = { ...dashboardSettings.value, ...parsed.dashboard };
        if (parsed.control) controlSettings.value = { ...controlSettings.value, ...parsed.control };
        if (parsed.privacy) privacySettings.value = { ...privacySettings.value, ...parsed.privacy };
      }
    } catch (error) {
      console.error('加载设置失败:', error);
    } finally {
      loading.value = false;
    }
  };
  
  // 保存设置
  const saveSettings = async (params: SettingsUpdateParams) => {
    loading.value = true;
    try {
      // 更新本地状态
      if (params.user) {
        userSettings.value = { ...userSettings.value, ...params.user };
      }
      if (params.notification) {
        notificationSettings.value = { ...notificationSettings.value, ...params.notification };
      }
      if (params.dashboard) {
        dashboardSettings.value = { ...dashboardSettings.value, ...params.dashboard };
      }
      if (params.control) {
        controlSettings.value = { ...controlSettings.value, ...params.control };
      }
      if (params.privacy) {
        privacySettings.value = { ...privacySettings.value, ...params.privacy };
      }
      
      // 保存到本地存储
      const settingsToSave = {
        user: userSettings.value,
        notification: notificationSettings.value,
        dashboard: dashboardSettings.value,
        control: controlSettings.value,
        privacy: privacySettings.value
      };
      localStorage.setItem('energy-settings', JSON.stringify(settingsToSave));
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.error('保存设置失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };
  
  // 重置设置
  const resetSettings = async (type?: 'user' | 'notification' | 'dashboard' | 'control' | 'privacy') => {
    try {
      if (!type || type === 'user') {
        userSettings.value = {
          theme: 'auto',
          language: 'zh-CN',
          timezone: 'Asia/Shanghai',
          dateFormat: 'YYYY-MM-DD',
          timeFormat: '24h'
        };
      }
      
      if (!type || type === 'notification') {
        notificationSettings.value = {
          enabled: true,
          email: true,
          push: true,
          sms: false,
          deviceOffline: true,
          energyAlert: true,
          maintenanceReminder: true,
          weeklyReport: true,
          monthlyReport: true,
          alertThreshold: {
            powerUsage: 100,
            energyConsumption: 50,
            cost: 100
          }
        };
      }
      
      // 清除本地存储
      if (!type) {
        localStorage.removeItem('energy-settings');
      }
      
    } catch (error) {
      console.error('重置设置失败:', error);
      throw error;
    }
  };
  
  // 添加定时规则
  const addScheduleRule = (rule: Omit<ControlSettings['scheduleRules'][0], 'id'>) => {
    const newRule = {
      ...rule,
      id: Date.now().toString()
    };
    controlSettings.value.scheduleRules.push(newRule);
  };
  
  // 删除定时规则
  const removeScheduleRule = (ruleId: string) => {
    const index = controlSettings.value.scheduleRules.findIndex(rule => rule.id === ruleId);
    if (index > -1) {
      controlSettings.value.scheduleRules.splice(index, 1);
    }
  };
  
  // 更新仪表板布局
  const updateDashboardLayout = (widgets: DashboardSettings['widgets']) => {
    dashboardSettings.value.widgets = widgets;
  };
  
  return {
    userSettings,
    notificationSettings,
    dashboardSettings,
    controlSettings,
    privacySettings,
    loading,
    currentTheme,
    notificationsEnabled,
    loadSettings,
    saveSettings,
    resetSettings,
    addScheduleRule,
    removeScheduleRule,
    updateDashboardLayout
  };
});